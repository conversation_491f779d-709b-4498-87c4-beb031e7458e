{
  "extends": "./tsconfig.json", // Inherit from the main tsconfig.json
  "compilerOptions": {
    "sourceMap": true, // Often good to have sourcemaps for builds, even if not for dev
    "declaration": true, // Ensure declarations are generated if this were a library
    "declarationMap": true
  },
  "exclude": [
    "node_modules",
    "dist",
    "test",
    "**/*.test.ts",
    "**/*.spec.ts",
    "src/dev-tools" // Example: exclude development-only tools from build
  ]
}
