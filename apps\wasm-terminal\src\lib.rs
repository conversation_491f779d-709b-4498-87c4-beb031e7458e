use wasm_bindgen::prelude::*;
use web_sys::console;
use serde::{Deserialize, Serialize};
use js_sys::Promise;

// Card data structure
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct CardData {
    pub pan: Option<String>,
    pub expiry_date: Option<String>,
    pub cardholder_name: Option<String>,
    pub track1: Option<String>,
    pub track2: Option<String>,
    pub card_type: Option<String>,
    pub service_code: Option<String>,
}

// Terminal info structure
#[derive(Serialize, Deserialize, Debug)]
pub struct TerminalInfo {
    pub serial_number: String,
    pub model: String,
    pub firmware_version: String,
    pub battery_level: u8,
    pub is_hardware_available: bool,
}

// Payment result structure
#[derive(Serialize, Deserialize, Debug)]
pub struct PaymentResult {
    pub success: bool,
    pub transaction_id: Option<String>,
    pub auth_code: Option<String>,
    pub error_message: Option<String>,
}

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = console)]
    fn log(s: &str);
    
    // Android WebView interface
    #[wasm_bindgen(js_namespace = Android, catch)]
    fn readCard(callback: &str) -> Result<(), JsValue>;
    
    #[wasm_bindgen(js_namespace = Android, catch)]
    fn printText(text: &str) -> Result<bool, JsValue>;
    
    #[wasm_bindgen(js_namespace = Android, catch)]
    fn getTerminalInfo() -> Result<String, JsValue>;
    
    #[wasm_bindgen(js_namespace = Android, catch)]
    fn showToast(message: &str) -> Result<(), JsValue>;
}

// Macro for console logging
macro_rules! console_log {
    ($($t:tt)*) => (log(&format_args!($($t)*).to_string()))
}

#[wasm_bindgen]
pub struct PAXTerminal {
    is_initialized: bool,
    card_reading_callback: Option<js_sys::Function>,
}

#[wasm_bindgen]
impl PAXTerminal {
    #[wasm_bindgen(constructor)]
    pub fn new() -> PAXTerminal {
        console_log!("Initializing PAX Terminal WASM module");
        
        // Set panic hook for better error messages
        std::panic::set_hook(Box::new(console_error_panic_hook::hook));
        
        PAXTerminal {
            is_initialized: true,
            card_reading_callback: None,
        }
    }

    #[wasm_bindgen]
    pub fn is_hardware_available(&self) -> bool {
        // Check if Android interface is available
        let window = web_sys::window().unwrap();
        let android = js_sys::Reflect::get(&window, &JsValue::from_str("Android"));
        
        match android {
            Ok(android_obj) => !android_obj.is_undefined(),
            Err(_) => false,
        }
    }

    #[wasm_bindgen]
    pub fn get_terminal_info(&self) -> Result<JsValue, JsValue> {
        if self.is_hardware_available() {
            match getTerminalInfo() {
                Ok(info_str) => {
                    // Parse the JSON string from Android
                    let info: TerminalInfo = serde_json::from_str(&info_str)
                        .map_err(|e| JsValue::from_str(&format!("Failed to parse terminal info: {}", e)))?;
                    
                    Ok(serde_wasm_bindgen::to_value(&info)?)
                }
                Err(e) => Err(e),
            }
        } else {
            // Return simulated terminal info
            let info = TerminalInfo {
                serial_number: "WASM001".to_string(),
                model: "PAX A920 Pro (WASM)".to_string(),
                firmware_version: "1.0.0-wasm".to_string(),
                battery_level: 100,
                is_hardware_available: false,
            };
            
            Ok(serde_wasm_bindgen::to_value(&info)?)
        }
    }

    #[wasm_bindgen]
    pub fn start_card_reading(&mut self, callback: js_sys::Function) -> Result<(), JsValue> {
        console_log!("Starting card reading...");
        
        self.card_reading_callback = Some(callback);
        
        if self.is_hardware_available() {
            // Use Android interface
            readCard("onCardReadComplete")?;
        } else {
            // Simulate card reading for development
            self.simulate_card_reading();
        }
        
        Ok(())
    }

    fn simulate_card_reading(&self) {
        console_log!("Simulating card reading (no hardware available)");
        
        let callback = self.card_reading_callback.as_ref().unwrap().clone();
        
        // Simulate card reading delay
        let closure = Closure::once_into_js(move || {
            let simulated_card = CardData {
                pan: Some("****************".to_string()),
                expiry_date: Some("1225".to_string()),
                cardholder_name: Some("TEST CARDHOLDER".to_string()),
                track1: None,
                track2: Some("****************=25121010000000000000".to_string()),
                card_type: Some("chip".to_string()),
                service_code: Some("101".to_string()),
            };
            
            let card_data_js = serde_wasm_bindgen::to_value(&simulated_card).unwrap();
            let _ = callback.call1(&JsValue::NULL, &card_data_js);
        });
        
        let window = web_sys::window().unwrap();
        let _ = window.set_timeout_with_callback_and_timeout_and_arguments_0(
            closure.as_ref().unchecked_ref(),
            3000, // 3 second delay
        );
    }

    #[wasm_bindgen]
    pub fn stop_card_reading(&mut self) {
        console_log!("Stopping card reading");
        self.card_reading_callback = None;
    }

    #[wasm_bindgen]
    pub fn print_receipt(&self, receipt_text: &str) -> Result<bool, JsValue> {
        console_log!("Printing receipt: {}", receipt_text);
        
        if self.is_hardware_available() {
            printText(receipt_text)
        } else {
            // Fallback to browser print
            self.browser_print(receipt_text);
            Ok(true)
        }
    }

    fn browser_print(&self, text: &str) {
        let window = web_sys::window().unwrap();
        let document = window.document().unwrap();
        
        // Create print window
        let print_window = window.open_with_url_and_target("", "_blank").unwrap().unwrap();
        let print_document = print_window.document().unwrap();
        
        let html_content = format!(
            r#"
            <html>
                <head>
                    <title>Receipt</title>
                    <style>
                        body {{
                            font-family: 'Courier New', monospace;
                            font-size: 12px;
                            line-height: 1.2;
                            margin: 0;
                            padding: 10px;
                            white-space: pre-wrap;
                        }}
                        @media print {{
                            body {{ margin: 0; padding: 5px; }}
                        }}
                    </style>
                </head>
                <body>{}</body>
            </html>
            "#,
            text
        );
        
        print_document.write(&html_content).unwrap();
        print_document.close().unwrap();
        print_window.print().unwrap();
        print_window.close().unwrap();
    }

    #[wasm_bindgen]
    pub fn show_message(&self, message: &str) {
        console_log!("Terminal Message: {}", message);
    }

    #[wasm_bindgen]
    pub fn beep(&self, duration: u32) {
        if !self.is_hardware_available() {
            // Browser beep fallback using Web Audio API
            self.browser_beep(duration);
        }
    }

    fn browser_beep(&self, duration: u32) {
        let window = web_sys::window().unwrap();
        let audio_context = web_sys::AudioContext::new().unwrap();
        
        let oscillator = audio_context.create_oscillator().unwrap();
        let gain_node = audio_context.create_gain().unwrap();
        
        oscillator.connect_with_audio_node(&gain_node).unwrap();
        gain_node.connect_with_audio_node(&audio_context.destination()).unwrap();
        
        oscillator.set_frequency(800.0);
        oscillator.set_type(web_sys::OscillatorType::Sine);
        
        let gain = gain_node.gain();
        let current_time = audio_context.current_time();
        gain.set_value_at_time(0.3, current_time).unwrap();
        gain.exponential_ramp_to_value_at_time(0.01, current_time + (duration as f64 / 1000.0)).unwrap();
        
        oscillator.start().unwrap();
        oscillator.stop_with_when(current_time + (duration as f64 / 1000.0)).unwrap();
    }

    #[wasm_bindgen]
    pub fn vibrate(&self, duration: u32) {
        console_log!("Vibrate for {}ms", duration);
        // Note: Vibration API would be implemented here in a real environment
    }

    #[wasm_bindgen]
    pub fn process_payment(&self, amount: u32, card_data: JsValue) -> Promise {
        let card: CardData = serde_wasm_bindgen::from_value(card_data).unwrap();
        
        wasm_bindgen_futures::future_to_promise(async move {
            // Simulate payment processing
            console_log!("Processing payment for amount: {} with card: {:?}", amount, card);
            
            // In a real implementation, this would call your payment API
            let result = PaymentResult {
                success: true,
                transaction_id: Some(format!("TXN_{}", js_sys::Date::now() as u64)),
                auth_code: Some("123456".to_string()),
                error_message: None,
            };
            
            Ok(serde_wasm_bindgen::to_value(&result)?)
        })
    }

    #[wasm_bindgen]
    pub fn format_card_number(&self, pan: &str) -> String {
        let cleaned: String = pan.chars().filter(|c| c.is_ascii_digit()).collect();
        let mut formatted = String::new();
        
        for (i, c) in cleaned.chars().enumerate() {
            if i > 0 && i % 4 == 0 {
                formatted.push(' ');
            }
            formatted.push(c);
        }
        
        formatted
    }

    #[wasm_bindgen]
    pub fn mask_card_number(&self, pan: &str) -> String {
        let cleaned: String = pan.chars().filter(|c| c.is_ascii_digit()).collect();
        
        if cleaned.len() < 4 {
            return "****".to_string();
        }
        
        let last_four = &cleaned[cleaned.len() - 4..];
        format!("**** **** **** {}", last_four)
    }
}

// Global callback for Android WebView
#[wasm_bindgen]
pub fn on_card_read_complete(card_data_json: &str) {
    console_log!("Card read complete: {}", card_data_json);
    
    // This will be called by the Android WebView when card reading is complete
    // The actual callback handling is done in the JavaScript layer
}

#[wasm_bindgen]
pub fn on_card_read_error(error: &str) {
    console_log!("Card read error: {}", error);
}

// Initialize the module
#[wasm_bindgen(start)]
pub fn main() {
    console_log!("PAX Terminal WASM module loaded successfully");
}
