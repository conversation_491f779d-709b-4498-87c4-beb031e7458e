/**
 * PAX A920 + Stripe Integration Service
 * 
 * This service integrates PAX A920 Pro terminal hardware with Stripe payments
 * using Neptune Service APIs and POSitive integration patterns.
 * 
 * Features:
 * - Card reading via Neptune API
 * - Receipt printing via Neptune Printer API
 * - Stripe payment processing
 * - Real-time transaction logging
 * - Protocol 101.1, 101.3, 101.5, 101.8 support
 */

import { PAXNeptuneSDK, PAXHardwareConfig, CardData, ReceiptLine } from './paxNeptuneSDK';
import { useAppStore } from '../store/useAppStore';

export interface StripePaymentResult {
  success: boolean;
  paymentIntent?: any;
  error?: string;
  transactionId?: string;
  receiptData?: ReceiptData;
}

export interface ReceiptData {
  transactionId: string;
  amount: number;
  cardType: string;
  cardLast4: string;
  timestamp: string;
  merchantName: string;
  terminalId: string;
  approvalCode?: string;
  rrn?: string;
}

export interface PAXStripeConfig {
  stripePublishableKey: string;
  merchantName: string;
  merchantAddress: string;
  terminalId: string;
  enableLogging?: boolean;
}

export class PAXStripeIntegration {
  private neptuneSDK: PAXNeptuneSDK;
  private config: PAXStripeConfig;
  private stripe: any = null;
  private appStore: any;

  constructor(config: PAXStripeConfig) {
    this.config = config;
    
    // Initialize Neptune SDK
    const neptuneConfig: PAXHardwareConfig = {
      terminalId: config.terminalId,
      merchantId: 'STRIPE_MERCHANT',
      enableDebugMode: true,
      timeout: 30000
    };
    
    this.neptuneSDK = new PAXNeptuneSDK(neptuneConfig);
    this.appStore = useAppStore.getState();
    
    // Initialize Stripe
    this.initializeStripe();
  }

  /**
   * Initialize Stripe SDK
   */
  private async initializeStripe(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && (window as any).Stripe) {
        this.stripe = (window as any).Stripe(this.config.stripePublishableKey);
        this.log('info', 'Stripe SDK initialized');
      } else {
        throw new Error('Stripe SDK not loaded');
      }
    } catch (error) {
      this.log('error', 'Failed to initialize Stripe SDK', { error });
    }
  }

  /**
   * Initialize the PAX hardware
   */
  async initialize(): Promise<boolean> {
    try {
      const neptuneReady = await this.neptuneSDK.initialize();
      
      if (neptuneReady) {
        this.log('info', 'PAX Stripe integration initialized successfully');
        return true;
      } else {
        this.log('warn', 'PAX hardware not available - using simulation mode');
        return false;
      }
    } catch (error) {
      this.log('error', 'Failed to initialize PAX Stripe integration', { error });
      return false;
    }
  }

  /**
   * Process a complete payment transaction
   */
  async processPayment(amount: number, currency: string = 'usd'): Promise<StripePaymentResult> {
    try {
      this.log('info', 'Starting payment process', { amount, currency });

      // Step 1: Create Stripe Payment Intent
      const paymentIntent = await this.createPaymentIntent(amount, currency);
      if (!paymentIntent) {
        throw new Error('Failed to create payment intent');
      }

      // Step 2: Read card from PAX terminal
      const cardData = await this.readCard();
      if (!cardData) {
        throw new Error('Failed to read card');
      }

      // Step 3: Process payment with Stripe
      const paymentResult = await this.confirmPayment(paymentIntent, cardData);
      if (!paymentResult.success) {
        throw new Error(paymentResult.error || 'Payment failed');
      }

      // Step 4: Generate and print receipt
      const receiptData: ReceiptData = {
        transactionId: paymentIntent.id,
        amount,
        cardType: this.detectCardType(cardData.pan || '') || 'UNKNOWN',
        cardLast4: cardData.pan?.slice(-4) || '****',
        timestamp: new Date().toISOString(),
        merchantName: this.config.merchantName,
        terminalId: this.config.terminalId,
        approvalCode: paymentResult.paymentIntent?.charges?.data?.[0]?.outcome?.seller_message,
        rrn: paymentResult.paymentIntent?.charges?.data?.[0]?.balance_transaction
      };

      await this.printReceipt(receiptData);

      this.log('info', 'Payment processed successfully', { transactionId: paymentIntent.id });

      return {
        success: true,
        paymentIntent: paymentResult.paymentIntent,
        transactionId: paymentIntent.id,
        receiptData
      };

    } catch (error) {
      this.log('error', 'Payment processing failed', { error });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Create Stripe Payment Intent
   */
  private async createPaymentIntent(amount: number, currency: string): Promise<any> {
    try {
      const response = await fetch('/api/v1/stripe/payment-intents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          currency,
          metadata: {
            terminal_id: this.config.terminalId,
            merchant_name: this.config.merchantName
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create payment intent');
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      this.log('error', 'Failed to create payment intent', { error });
      return null;
    }
  }

  /**
   * Read card using PAX Neptune API
   */
  private async readCard(): Promise<CardData | null> {
    return new Promise((resolve) => {
      this.neptuneSDK.startCardReading(
        (cardData: CardData) => {
          this.log('info', 'Card read successfully', { cardType: cardData.contactless ? 'contactless' : 'chip' });
          resolve(cardData);
        },
        (error: string) => {
          this.log('error', 'Card reading failed', { error });
          resolve(null);
        },
        30000 // 30 second timeout
      );
    });
  }

  /**
   * Confirm payment with Stripe
   */
  private async confirmPayment(paymentIntent: any, cardData: CardData): Promise<StripePaymentResult> {
    try {
      if (!this.stripe) {
        throw new Error('Stripe not initialized');
      }

      // For PAX terminal integration, we'll use the payment method from card data
      const paymentMethodData = {
        type: 'card',
        card: {
          number: cardData.pan,
          exp_month: cardData.expiryDate?.substring(2, 4),
          exp_year: cardData.expiryDate?.substring(0, 2),
          cvc: '000' // CVC not available from card reader
        }
      };

      const result = await this.stripe.confirmCardPayment(paymentIntent.client_secret, {
        payment_method: paymentMethodData
      });

      if (result.error) {
        return {
          success: false,
          error: result.error.message
        };
      }

      return {
        success: true,
        paymentIntent: result.paymentIntent
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment confirmation failed'
      };
    }
  }

  /**
   * Print receipt using PAX Neptune Printer API
   */
  async printReceipt(receiptData: ReceiptData): Promise<boolean> {
    try {
      const receiptLines: ReceiptLine[] = [
        { text: this.config.merchantName, config: { alignment: 'center' as const, bold: true, fontSize: 'large' as const } },
        { text: this.config.merchantAddress, config: { alignment: 'center' as const, fontSize: 'small' as const } },
        { text: '', config: {} }, // Empty line
        { text: '================================', config: { alignment: 'center' as const } },
        { text: 'PAYMENT RECEIPT', config: { alignment: 'center' as const, bold: true } },
        { text: '================================', config: { alignment: 'center' as const } },
        { text: '', config: {} },
        { text: `Transaction ID: ${receiptData.transactionId}`, config: { fontSize: 'small' as const } },
        { text: `Terminal ID: ${receiptData.terminalId}`, config: { fontSize: 'small' as const } },
        { text: `Date/Time: ${new Date(receiptData.timestamp).toLocaleString()}`, config: { fontSize: 'small' as const } },
        { text: '', config: {} },
        { text: `Card Type: ${receiptData.cardType}`, config: {} },
        { text: `Card Number: ****${receiptData.cardLast4}`, config: {} },
        { text: '', config: {} },
        { text: `Amount: $${(receiptData.amount / 100).toFixed(2)}`, config: { bold: true, fontSize: 'large' as const } },
        { text: 'APPROVED', config: { alignment: 'center' as const, bold: true, fontSize: 'large' as const } },
        { text: '', config: {} },
        { text: receiptData.approvalCode ? `Approval: ${receiptData.approvalCode}` : '', config: { fontSize: 'small' as const } },
        { text: receiptData.rrn ? `RRN: ${receiptData.rrn}` : '', config: { fontSize: 'small' as const } },
        { text: '', config: {} },
        { text: 'Thank you for your business!', config: { alignment: 'center' as const } },
        { text: '', config: {} },
        { text: '================================', config: { alignment: 'center' as const } },
      ].filter(line => line.text !== ''); // Remove empty approval/rrn lines

      const printed = await this.neptuneSDK.printReceipt(receiptLines);
      
      if (printed) {
        this.log('info', 'Receipt printed successfully');
      } else {
        this.log('error', 'Failed to print receipt');
      }

      return printed;
    } catch (error) {
      this.log('error', 'Receipt printing failed', { error });
      return false;
    }
  }

  /**
   * Get terminal status
   */
  async getTerminalStatus(): Promise<any> {
    try {
      const terminalInfo = await this.neptuneSDK.getTerminalInfo();
      return {
        hardware: terminalInfo,
        stripe: !!this.stripe,
        ready: this.neptuneSDK.isHardwareAvailable()
      };
    } catch (error) {
      this.log('error', 'Failed to get terminal status', { error });
      return null;
    }
  }

  /**
   * Detect card type from PAN
   */
  private detectCardType(pan: string): string {
    if (!pan) return 'UNKNOWN';

    const firstDigit = pan.charAt(0);
    const firstTwoDigits = pan.substring(0, 2);
    const firstFourDigits = pan.substring(0, 4);

    // Visa
    if (firstDigit === '4') {
      return 'VISA';
    }

    // Mastercard
    if (firstDigit === '5' || (firstTwoDigits >= '22' && firstTwoDigits <= '27')) {
      return 'MASTERCARD';
    }

    // American Express
    if (firstTwoDigits === '34' || firstTwoDigits === '37') {
      return 'AMEX';
    }

    // Discover
    if (firstTwoDigits === '60' || firstFourDigits === '6011' ||
        (firstTwoDigits >= '64' && firstTwoDigits <= '65')) {
      return 'DISCOVER';
    }

    return 'UNKNOWN';
  }

  /**
   * Log system events
   */
  private log(level: 'info' | 'warn' | 'error' | 'debug', message: string, details?: any): void {
    if (this.config.enableLogging && this.appStore?.addSystemLog) {
      this.appStore.addSystemLog({
        level,
        category: 'payment',
        message: `[PAX-Stripe] ${message}`,
        details: {
          terminalId: this.config.terminalId,
          ...details
        }
      });
    }

    // Also log to console
    console[level](`[PAX-Stripe] ${message}`, details);
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    try {
      this.neptuneSDK.destroy();
      this.log('info', 'PAX Stripe integration destroyed');
    } catch (error) {
      this.log('error', 'Failed to destroy PAX Stripe integration', { error });
    }
  }
}
