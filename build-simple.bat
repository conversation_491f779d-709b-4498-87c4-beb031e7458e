@echo off
setlocal enabledelayedexpansion

echo 🚀 Building PAX A920 Pro POS Terminal - Simple Build (Windows)
echo ================================================================

:: Check if we're in the right directory
if not exist "package.json" (
    echo ❌ Error: Please run this script from the project root directory
    exit /b 1
)

:: Install dependencies
echo [INFO] Installing dependencies...
call npm install
if errorlevel 1 (
    echo ❌ Failed to install root dependencies
    exit /b 1
)

:: Install backend dependencies
echo [INFO] Installing backend dependencies...
cd apps\backend
call npm install
if errorlevel 1 (
    echo ❌ Failed to install backend dependencies
    exit /b 1
)
cd ..\..

:: Install frontend dependencies
echo [INFO] Installing frontend dependencies...
cd apps\frontend
call npm install
if errorlevel 1 (
    echo ❌ Failed to install frontend dependencies
    exit /b 1
)
cd ..\..

:: Install mock bank dependencies
echo [INFO] Installing mock bank dependencies...
cd apps\mock-bank
call npm install
if errorlevel 1 (
    echo ❌ Failed to install mock bank dependencies
    exit /b 1
)
cd ..\..

echo ✅ Dependencies installed

:: Try to build WASM module (optional)
echo [INFO] Attempting to build WASM module...

cd apps\wasm-terminal

:: Check if Rust is available
where rustc >nul 2>&1
if %errorlevel% == 0 (
    echo [INFO] Rust found, checking for wasm-pack...
    
    :: Check if wasm-pack is available
    where wasm-pack >nul 2>&1
    if %errorlevel% == 0 (
        echo [INFO] Building WASM module...
        call wasm-pack build --target web --out-dir pkg --release --out-name pax_terminal_wasm
        
        :: Check if build was successful
        if exist "pkg\pax_terminal_wasm.js" (
            :: Copy WASM files to frontend
            if not exist "..\frontend\public\wasm" mkdir "..\frontend\public\wasm"
            if not exist "..\frontend\src\types" mkdir "..\frontend\src\types"
            
            copy "pkg\pax_terminal_wasm.js" "..\frontend\public\wasm\" >nul
            copy "pkg\pax_terminal_wasm_bg.wasm" "..\frontend\public\wasm\" >nul
            copy "pkg\pax_terminal_wasm.d.ts" "..\frontend\src\types\" >nul
            
            echo ✅ WASM module built and copied successfully
        ) else (
            echo ⚠️ WASM build failed, but continuing with fallback mode
        )
    ) else (
        echo ⚠️ wasm-pack not found, skipping WASM build
        echo    Install wasm-pack: https://rustwasm.github.io/wasm-pack/installer/
    )
) else (
    echo ⚠️ Rust not found, skipping WASM build
    echo    Install Rust: https://rustup.rs/
)

cd ..\..

:: Build backend
echo [INFO] Building backend...
cd apps\backend
call npm run build
if errorlevel 1 (
    echo ❌ Backend build failed
    exit /b 1
)
cd ..\..
echo ✅ Backend built successfully

:: Build frontend
echo [INFO] Building frontend...
cd apps\frontend
call npm run build
if errorlevel 1 (
    echo ❌ Frontend build failed
    exit /b 1
)
cd ..\..
echo ✅ Frontend built successfully

:: Build mock bank
echo [INFO] Building mock bank...
cd apps\mock-bank
call npm run build
if errorlevel 1 (
    echo ❌ Mock bank build failed
    exit /b 1
)
cd ..\..
echo ✅ Mock bank built successfully

:: Create simple production bundle
echo [INFO] Creating production bundle...

if not exist "dist\simple" mkdir "dist\simple"

:: Copy backend
xcopy "apps\backend\dist" "dist\simple\backend\" /E /I /Y >nul
copy "apps\backend\package.json" "dist\simple\backend\" >nul

:: Copy frontend
xcopy "apps\frontend\dist" "dist\simple\frontend\" /E /I /Y >nul

:: Copy mock bank
xcopy "apps\mock-bank\dist" "dist\simple\mock-bank\" /E /I /Y >nul
copy "apps\mock-bank\package.json" "dist\simple\mock-bank\" >nul

:: Copy WASM files if they exist
if exist "apps\frontend\public\wasm" (
    if not exist "dist\simple\frontend\wasm" mkdir "dist\simple\frontend\wasm"
    xcopy "apps\frontend\public\wasm\*" "dist\simple\frontend\wasm\" /Y >nul 2>&1
    echo ✅ WASM files included in bundle
) else (
    echo ⚠️ No WASM files found - frontend will use fallback mode
)

:: Create Windows startup script
(
echo @echo off
echo echo 🚀 Starting PAX A920 Pro POS Terminal
echo.
echo :: Check if Node.js is available
echo where node ^>nul 2^>^&1
echo if %%errorlevel%% neq 0 ^(
echo     echo ❌ Node.js is not installed. Please install Node.js 18 or later.
echo     exit /b 1
echo ^)
echo.
echo :: Install production dependencies
echo echo 📦 Installing production dependencies...
echo.
echo cd backend ^&^& npm install --production --silent ^&^& cd ..
echo cd mock-bank ^&^& npm install --production --silent ^&^& cd ..
echo.
echo echo ✅ Dependencies installed
echo.
echo :: Start services
echo echo 🔄 Starting services...
echo.
echo :: Start backend
echo echo Starting backend on port 3001...
echo cd backend
echo start "Backend" cmd /c "npm start"
echo cd ..
echo.
echo :: Start mock bank
echo echo Starting mock bank on port 3002...
echo cd mock-bank
echo start "Mock Bank" cmd /c "npm start"
echo cd ..
echo.
echo :: Start frontend server
echo echo Starting frontend on port 3000...
echo cd frontend
echo.
echo :: Try different methods to serve static files
echo where python ^>nul 2^>^&1
echo if %%errorlevel%% == 0 ^(
echo     start "Frontend" cmd /c "python -m http.server 3000"
echo ^) else ^(
echo     where npx ^>nul 2^>^&1
echo     if %%errorlevel%% == 0 ^(
echo         start "Frontend" cmd /c "npx serve -s . -l 3000"
echo     ^) else ^(
echo         echo ❌ No static file server available. Please install Python or Node.js serve package.
echo         exit /b 1
echo     ^)
echo ^)
echo.
echo cd ..
echo.
echo echo.
echo echo 🎉 All services started successfully!
echo echo ==================================
echo echo Frontend:  http://localhost:3000
echo echo Backend:   http://localhost:3001
echo echo Mock Bank: http://localhost:3002
echo echo.
echo echo Press any key to stop all services...
echo pause ^>nul
echo.
echo :: Stop services
echo echo 🛑 Stopping all services...
echo taskkill /f /im node.exe /t ^>nul 2^>^&1
echo taskkill /f /im python.exe /t ^>nul 2^>^&1
echo echo ✅ All services stopped
) > "dist\simple\start.bat"

:: Create environment template
(
echo # PAX A920 Pro POS Terminal Configuration
echo NODE_ENV=production
echo.
echo # Database
echo MONGO_URI=mongodb://localhost:27017/pax_pos
echo.
echo # Stripe Configuration ^(REPLACE WITH YOUR KEYS^)
echo STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
echo STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
echo STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
echo.
echo # Security
echo JWT_SECRET=your_super_secure_jwt_secret_at_least_32_characters
echo CORS_ORIGIN=http://localhost:3000
echo.
echo # Terminal Configuration
echo TERMINAL_ID=PAX_A920_001
echo MERCHANT_ID=your_merchant_id
echo DEFAULT_CURRENCY=usd
echo PAYMENT_TIMEOUT=30000
echo MIN_AMOUNT=50
echo MAX_AMOUNT=99999999
echo.
echo # Features
echo CARD_READER_ENABLED=true
echo PRINTER_ENABLED=true
echo AUDIO_ENABLED=true
echo VIBRATION_ENABLED=true
echo MANUAL_ENTRY_ENABLED=true
echo.
echo # Receipt
echo RECEIPT_ENABLED=true
echo RECEIPT_COPIES=2
echo RECEIPT_SIGNATURE=true
echo RECEIPT_FOOTER=Thank you for your business!
echo.
echo # UI
echo UI_THEME=auto
echo UI_LANGUAGE=en
echo UI_FONT_SIZE=medium
echo UI_ORIENTATION=portrait
echo.
echo # Logging
echo LOG_LEVEL=info
echo.
echo # Rate Limiting
echo RATE_LIMIT_MAX=100
echo RATE_LIMIT_WINDOW=900000
) > "dist\simple\.env.example"

echo ✅ Production bundle created in dist\simple\

echo.
echo 🎉 Simple build completed successfully!
echo ======================================
echo.
echo 📦 Build Output: dist\simple\
echo.
echo 🚀 To start the application:
echo    cd dist\simple
echo    start.bat
echo.
echo 🔧 Configuration:
echo    1. Copy .env.example to .env
echo    2. Configure your Stripe keys
echo    3. Set up MongoDB connection
echo.
echo 📱 For APK creation:
echo    bash create-simple-apk.sh
echo.
echo ⚠️  Notes:
if not exist "apps\frontend\public\wasm" (
    echo    - WASM module not built ^(Rust/wasm-pack not available^)
    echo    - Frontend will use fallback mode for card simulation
    echo    - Install Rust and wasm-pack for hardware integration
) else (
    echo    - WASM module included for hardware integration
    echo    - Frontend supports both real hardware and fallback mode
)
echo.
echo ✅ Ready for deployment! 🚀

pause
