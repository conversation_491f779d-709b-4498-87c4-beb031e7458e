// MongoDB initialization script for PAX POS System
// This script runs when the MongoDB container starts for the first time

// Switch to the application database
db = db.getSiblingDB('pax_pos_system');

// Create application user with read/write permissions
db.createUser({
  user: 'pax_pos_user',
  pwd: 'pax_pos_password',
  roles: [
    {
      role: 'readWrite',
      db: 'pax_pos_system'
    }
  ]
});

// Create collections with validation
db.createCollection('transactions', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['amount', 'status', 'createdAt', 'updatedAt'],
      properties: {
        amount: {
          bsonType: 'number',
          minimum: 0.01,
          description: 'Transaction amount must be a positive number'
        },
        status: {
          bsonType: 'string',
          enum: ['success', 'failure', 'pending'],
          description: 'Status must be success, failure, or pending'
        },
        protocolCode: {
          bsonType: 'string',
          pattern: '^101\\.[1358]$',
          description: 'Protocol code must be 101.1, 101.3, 101.5, or 101.8'
        },
        stripePaymentIntentId: {
          bsonType: 'string',
          pattern: '^pi_[a-zA-Z0-9_]+$',
          description: 'Stripe payment intent ID must be valid format'
        },
        receiptUrl: {
          bsonType: 'string',
          pattern: '^https?://.+',
          description: 'Receipt URL must be a valid HTTP/HTTPS URL'
        },
        metadata: {
          bsonType: 'object',
          description: 'Metadata must be an object'
        },
        createdAt: {
          bsonType: 'string',
          description: 'Created timestamp must be a string'
        },
        updatedAt: {
          bsonType: 'string',
          description: 'Updated timestamp must be a string'
        }
      }
    }
  }
});

// Create indexes for better performance
db.transactions.createIndex({ createdAt: -1 });
db.transactions.createIndex({ status: 1 });
db.transactions.createIndex({ stripePaymentIntentId: 1 }, { sparse: true });
db.transactions.createIndex({ protocolCode: 1 }, { sparse: true });

// Create logs collection for application logs
db.createCollection('logs', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['level', 'message', 'timestamp'],
      properties: {
        level: {
          bsonType: 'string',
          enum: ['fatal', 'error', 'warn', 'info', 'debug', 'trace'],
          description: 'Log level must be valid'
        },
        message: {
          bsonType: 'string',
          description: 'Log message is required'
        },
        timestamp: {
          bsonType: 'string',
          description: 'Timestamp is required'
        },
        module: {
          bsonType: 'string',
          description: 'Module name'
        },
        metadata: {
          bsonType: 'object',
          description: 'Additional log metadata'
        }
      }
    }
  }
});

// Create index for logs
db.logs.createIndex({ timestamp: -1 });
db.logs.createIndex({ level: 1 });
db.logs.createIndex({ module: 1 });

// Create TTL index to automatically delete old logs (30 days)
db.logs.createIndex({ "timestamp": 1 }, { expireAfterSeconds: 2592000 });

print('PAX POS System database initialized successfully');
print('Created collections: transactions, logs');
print('Created user: pax_pos_user');
print('Created indexes for performance optimization');
