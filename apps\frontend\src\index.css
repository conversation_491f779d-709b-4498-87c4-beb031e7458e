@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}
/*
@tailwind base;
@tailwind components;
@tailwind utilities;
*/

/* Basic global styles if not using a framework like Tailwind */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f4f6f8;
  color: #333;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

#root {
  /* You might want your app to take up the full viewport height */
  /* min-height: 100vh; */
  /* display: flex; */
  /* flex-direction: column; */
}

.app-container {
  max-width: 1200px; /* Example max width */
  margin: 0 auto; /* Center content */
  /* padding: 20px; */ /* Example padding */
}
