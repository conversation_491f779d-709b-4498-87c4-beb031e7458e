import mongoose, { Schema, Document } from 'mongoose';
import { SystemLog as ISystemLog } from 'shared-types';

export interface SystemLogDocument extends Omit<ISystemLog, '_id' | 'createdAt' | 'updatedAt'>, Document {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
}

const systemLogSchema = new Schema<SystemLogDocument>({
  timestamp: {
    type: Date,
    required: true,
    default: Date.now
  },
  level: {
    type: String,
    required: true,
    enum: ['info', 'warn', 'error', 'debug']
  },
  category: {
    type: String,
    required: true,
    enum: ['payment', 'hardware', 'protocol', 'system', 'security']
  },
  message: {
    type: String,
    required: true
  },
  details: {
    type: Schema.Types.Mixed,
    required: false
  },
  userId: {
    type: String,
    required: false
  },
  sessionId: {
    type: String,
    required: false
  }
}, {
  timestamps: true,
  collection: 'systemlogs'
});

// Index for efficient querying
systemLogSchema.index({ timestamp: -1 });
systemLogSchema.index({ level: 1 });
systemLogSchema.index({ category: 1 });
systemLogSchema.index({ userId: 1 });
systemLogSchema.index({ sessionId: 1 });

export const SystemLog = mongoose.model<SystemLogDocument>('SystemLog', systemLogSchema);
