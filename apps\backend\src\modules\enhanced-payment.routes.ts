/**
 * Enhanced Payment Routes
 * 
 * Handles PAX terminal payment processing with webhook integration
 * Provides endpoints for payment creation, status checking, and management
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { enhancedPaymentService, PaymentRequest } from '../services/enhancedPaymentService';

interface CreatePaymentRequest {
  amount: number;
  currency?: string;
  description?: string;
  metadata?: Record<string, string>;
  terminalId: string;
  merchantName: string;
  cardData?: {
    encryptedPan?: string;
    maskedPan?: string;
    expiryDate?: string;
    cardholderName?: string;
    cardBrand?: string;
    cardType?: string;
    emvData?: any;
    ksn?: string;
  };
}

interface PaymentStatusRequest {
  paymentIntentId: string;
}



export default async function enhancedPaymentRoutes(fastify: FastifyInstance) {
  
  /**
   * Create payment with PAX terminal data
   */
  fastify.post('/payments/terminal', async (
    request: FastifyRequest<{ Body: CreatePaymentRequest }>,
    reply: FastifyReply
  ) => {
    try {
      const {
        amount,
        currency = 'usd',
        description,
        metadata,
        terminalId,
        merchantName,
        cardData
      } = request.body;

      // Validate required fields
      if (!amount || !terminalId || !merchantName) {
        return reply.status(400).send({
          success: false,
          error: 'Missing required fields: amount, terminalId, merchantName'
        });
      }

      if (amount <= 0) {
        return reply.status(400).send({
          success: false,
          error: 'Amount must be greater than 0'
        });
      }

      if (!cardData?.encryptedPan) {
        return reply.status(400).send({
          success: false,
          error: 'Encrypted card data is required'
        });
      }

      // Create payment request
      const paymentRequest: PaymentRequest = {
        amount,
        currency,
        description: description || `PAX Terminal Payment - ${terminalId}`,
        metadata: {
          source: 'pax_terminal',
          terminal_id: terminalId,
          merchant_name: merchantName,
          ...metadata
        },
        terminalId,
        merchantName,
        cardData
      };

      // Process payment
      const result = await enhancedPaymentService.processTerminalPayment(paymentRequest);

      if (!result.success) {
        return reply.status(400).send({
          success: false,
          error: result.error || 'Payment processing failed'
        });
      }

      fastify.log.info('Terminal payment processed successfully', {
        paymentIntentId: result.paymentIntent?.id,
        terminalId,
        amount
      });

      reply.send({
        success: true,
        data: {
          paymentIntentId: result.paymentIntent?.id,
          paymentMethodId: result.paymentMethod?.id,
          status: result.paymentIntent?.status,
          amount,
          currency,
          transactionId: result.transactionId,
          receiptData: result.receiptData,
          clientSecret: result.paymentIntent?.client_secret
        }
      });

    } catch (error) {
      fastify.log.error('Terminal payment processing failed', error);
      
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      });
    }
  });

  /**
   * Get payment status (Enhanced Payment API)
   */
  fastify.get('/payments/terminal/:paymentIntentId/status', async (
    request: FastifyRequest<{ Params: PaymentStatusRequest }>,
    reply: FastifyReply
  ) => {
    try {
      const { paymentIntentId } = request.params;

      if (!paymentIntentId) {
        return reply.status(400).send({
          success: false,
          error: 'Payment Intent ID is required'
        });
      }

      const status = await enhancedPaymentService.getPaymentStatus(paymentIntentId);

      reply.send({
        success: true,
        data: {
          paymentIntentId,
          ...status
        }
      });

    } catch (error) {
      fastify.log.error('Failed to get payment status', error);
      
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get payment status'
      });
    }
  });

  /**
   * Cancel payment (Enhanced Payment API)
   */
  fastify.post('/payments/terminal/:paymentIntentId/cancel', async (
    request: FastifyRequest<{ 
      Params: { paymentIntentId: string };
      Body: { reason?: string } 
    }>,
    reply: FastifyReply
  ) => {
    try {
      const { paymentIntentId } = request.params;
      const { reason } = request.body;

      if (!paymentIntentId) {
        return reply.status(400).send({
          success: false,
          error: 'Payment Intent ID is required'
        });
      }

      const canceled = await enhancedPaymentService.cancelPayment(paymentIntentId);

      if (!canceled) {
        return reply.status(400).send({
          success: false,
          error: 'Failed to cancel payment'
        });
      }

      fastify.log.info('Payment canceled', {
        paymentIntentId,
        reason: reason || 'No reason provided'
      });

      reply.send({
        success: true,
        data: {
          paymentIntentId,
          status: 'canceled',
          reason: reason || 'Canceled by request'
        }
      });

    } catch (error) {
      fastify.log.error('Failed to cancel payment', error);
      
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to cancel payment'
      });
    }
  });

  /**
   * Process manual payment (fallback)
   */
  fastify.post('/payments/manual', async (
    request: FastifyRequest<{ 
      Body: {
        amount: number;
        currency?: string;
        cardNumber: string;
        expiryMonth: number;
        expiryYear: number;
        cvc: string;
        cardholderName?: string;
        terminalId: string;
        merchantName: string;
        description?: string;
        metadata?: Record<string, string>;
      }
    }>,
    reply: FastifyReply
  ) => {
    try {
      const {
        amount,
        currency = 'usd',
        cardNumber,
        expiryMonth,
        expiryYear,
        cvc,
        cardholderName,
        terminalId,
        merchantName,
        description,
        metadata
      } = request.body;

      // Validate required fields
      if (!amount || !cardNumber || !expiryMonth || !expiryYear || !cvc || !terminalId || !merchantName) {
        return reply.status(400).send({
          success: false,
          error: 'Missing required fields for manual payment'
        });
      }

      // Create manual payment request (simulate encrypted data)
      const paymentRequest: PaymentRequest = {
        amount,
        currency,
        description: description || `Manual Payment - ${terminalId}`,
        metadata: {
          source: 'manual_entry',
          terminal_id: terminalId,
          merchant_name: merchantName,
          ...metadata
        },
        terminalId,
        merchantName,
        cardData: {
          encryptedPan: Buffer.from(cardNumber).toString('base64'), // Simple encoding for demo
          maskedPan: `****${cardNumber.slice(-4)}`,
          expiryDate: `${expiryMonth.toString().padStart(2, '0')}${expiryYear.toString().slice(-2)}`,
          cardholderName: cardholderName || 'Manual Entry',
          cardType: 'manual',
          cardBrand: 'unknown'
        }
      };

      const result = await enhancedPaymentService.processTerminalPayment(paymentRequest);

      if (!result.success) {
        return reply.status(400).send({
          success: false,
          error: result.error || 'Manual payment processing failed'
        });
      }

      fastify.log.info('Manual payment processed successfully', {
        paymentIntentId: result.paymentIntent?.id,
        terminalId,
        amount
      });

      reply.send({
        success: true,
        data: {
          paymentIntentId: result.paymentIntent?.id,
          paymentMethodId: result.paymentMethod?.id,
          status: result.paymentIntent?.status,
          amount,
          currency,
          transactionId: result.transactionId,
          receiptData: result.receiptData
        }
      });

    } catch (error) {
      fastify.log.error('Manual payment processing failed', error);
      
      reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      });
    }
  });

  /**
   * Health check for enhanced payment service
   */
  fastify.get('/payments/terminal/health', async (_request, reply) => {
    reply.send({
      success: true,
      message: 'Enhanced payment service is healthy',
      timestamp: new Date().toISOString(),
      services: {
        stripe: 'connected',
        webhook: 'configured',
        encryption: 'available'
      }
    });
  });
}
