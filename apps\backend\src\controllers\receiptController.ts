import { FastifyRequest, FastifyReply } from 'fastify';
import { ReceiptService } from '../services/receiptService';
import Transaction from '../models/Transaction.mongo';
import { logger } from '../config/logger';

interface ReceiptParams {
  transactionId: string;
}

interface PrintReceiptBody {
  transactionId: string;
  copies?: number;
  customerCopy?: boolean;
}

export const receiptController = {
  // Generate receipt for a transaction
  async generateReceipt(
    request: FastifyRequest<{ Params: ReceiptParams }>,
    reply: FastifyReply
  ) {
    try {
      const { transactionId } = request.params;

      const transaction = await Transaction.findById(transactionId);
      if (!transaction) {
        return reply.status(404).send({
          success: false,
          error: 'Transaction not found'
        });
      }

      const customerReceipt = ReceiptService.generateCustomerReceipt(transaction);
      const merchantReceipt = ReceiptService.generateMerchantReceipt(transaction);

      logger.info(`Receipt generated for transaction ${transactionId}`);

      return reply.send({
        success: true,
        data: {
          transactionId,
          customerReceipt,
          merchantReceipt,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Error generating receipt:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to generate receipt'
      });
    }
  },

  // Print receipt (simulate printer commands)
  async printReceipt(
    request: FastifyRequest<{ Body: PrintReceiptBody }>,
    reply: FastifyReply
  ) {
    try {
      const { transactionId, copies = 1, customerCopy = true } = request.body;

      const transaction = await Transaction.findById(transactionId);
      if (!transaction) {
        return reply.status(404).send({
          success: false,
          error: 'Transaction not found'
        });
      }

      const receipt = customerCopy 
        ? ReceiptService.generateCustomerReceipt(transaction)
        : ReceiptService.generateMerchantReceipt(transaction);

      // Simulate printing process
      const printJobs = [];
      for (let i = 0; i < copies; i++) {
        printJobs.push({
          jobId: `print_${Date.now()}_${i}`,
          receipt,
          status: 'queued',
          timestamp: new Date().toISOString()
        });
      }

      logger.info(`Print job created for transaction ${transactionId}, copies: ${copies}`);

      return reply.send({
        success: true,
        data: {
          transactionId,
          printJobs,
          message: `${copies} receipt(s) sent to printer`
        }
      });
    } catch (error) {
      logger.error('Error printing receipt:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to print receipt'
      });
    }
  },

  // Get receipt in different formats
  async getReceiptFormats(
    request: FastifyRequest<{ Params: ReceiptParams }>,
    reply: FastifyReply
  ) {
    try {
      const { transactionId } = request.params;

      const transaction = await Transaction.findById(transactionId);
      if (!transaction) {
        return reply.status(404).send({
          success: false,
          error: 'Transaction not found'
        });
      }

      const customerReceipt = ReceiptService.generateCustomerReceipt(transaction);
      const merchantReceipt = ReceiptService.generateMerchantReceipt(transaction);

      return reply.send({
        success: true,
        data: {
          transactionId,
          formats: {
            text: {
              customer: customerReceipt,
              merchant: merchantReceipt
            },
            html: {
              customer: this.convertToHTML(customerReceipt),
              merchant: this.convertToHTML(merchantReceipt)
            },
            json: {
              transactionId: transaction._id,
              amount: transaction.amount,
              status: transaction.status,
              timestamp: transaction.createdAt,
              protocolCode: transaction.protocolCode
            }
          }
        }
      });
    } catch (error) {
      logger.error('Error getting receipt formats:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to get receipt formats'
      });
    }
  },

  // Email receipt (placeholder for future implementation)
  async emailReceipt(
    request: FastifyRequest<{ 
      Body: { transactionId: string; email: string; customerCopy?: boolean } 
    }>,
    reply: FastifyReply
  ) {
    try {
      const { transactionId, email } = request.body;

      const transaction = await Transaction.findById(transactionId);
      if (!transaction) {
        return reply.status(404).send({
          success: false,
          error: 'Transaction not found'
        });
      }

      // Placeholder for email service integration
      logger.info(`Email receipt requested for transaction ${transactionId} to ${email}`);

      return reply.send({
        success: true,
        data: {
          transactionId,
          email,
          message: 'Receipt email queued for delivery',
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Error emailing receipt:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to email receipt'
      });
    }
  },

  // Helper method to convert text receipt to HTML
  convertToHTML(textReceipt: string): string {
    return `
      <div style="font-family: 'Courier New', monospace; white-space: pre-wrap; background: white; padding: 20px; max-width: 400px; margin: 0 auto; border: 1px solid #ccc;">
        ${textReceipt.replace(/\n/g, '<br>')}
      </div>
    `;
  }
};
