export interface CardData {
  pan?: string;
  expiryDate?: string;
  cardholderName?: string;
  track1?: string;
  track2?: string;
  emvData?: string;
  cardType?: 'chip' | 'swipe' | 'tap' | 'manual';
  serviceCode?: string;
  discretionaryData?: string;
}

export interface TerminalInfo {
  serialNumber: string;
  model: string;
  firmwareVersion: string;
  batteryLevel: number;
  isHardwareAvailable: boolean;
  capabilities: string[];
}

export interface PrinterOptions {
  copies?: number;
  fontSize?: 'small' | 'medium' | 'large';
  alignment?: 'left' | 'center' | 'right';
  bold?: boolean;
}

// Android WebView interface for PAX terminal
declare global {
  interface Window {
    AndroidTerminal?: {
      readCard: (callback: string) => void;
      printText: (text: string) => boolean;
      getTerminalInfo: () => string;
      showToast: (message: string) => void;
      beep: (duration: number) => void;
      vibrate: (duration: number) => void;
    };
    // Global callbacks for Android WebView
    onCardReadComplete?: (cardDataJson: string) => void;
    onCardReadError?: (error: string) => void;
  }
}

export class PAXTerminalHardware {
  private static instance: PAXTerminalHardware;
  private cardReadingCallback?: (data: CardData) => void;
  private cardErrorCallback?: (error: string) => void;
  private isReading = false;
  private readingTimeout?: NodeJS.Timeout;

  private constructor() {
    this.initializeHardware();
  }

  public static getInstance(): PAXTerminalHardware {
    if (!PAXTerminalHardware.instance) {
      PAXTerminalHardware.instance = new PAXTerminalHardware();
    }
    return PAXTerminalHardware.instance;
  }

  private initializeHardware() {
    // Setup global callbacks for Android WebView
    window.onCardReadComplete = (cardDataJson: string) => {
      try {
        const cardData: CardData = JSON.parse(cardDataJson);
        this.handleCardReadSuccess(cardData);
      } catch (error) {
        console.error('Failed to parse card data:', error);
        this.handleCardReadError('Failed to parse card data');
      }
    };

    window.onCardReadError = (error: string) => {
      this.handleCardReadError(error);
    };

    console.log('PAX Terminal Hardware initialized');
  }

  // Check if hardware is available
  public isHardwareAvailable(): boolean {
    return !!(window.AndroidTerminal || this.isWebUSBAvailable() || this.isWebSerialAvailable());
  }

  // Check for Web USB API (for USB card readers)
  private isWebUSBAvailable(): boolean {
    return 'usb' in navigator;
  }

  // Check for Web Serial API (for serial card readers)
  private isWebSerialAvailable(): boolean {
    return 'serial' in navigator;
  }

  // Get terminal information
  public async getTerminalInfo(): Promise<TerminalInfo> {
    if (window.AndroidTerminal) {
      try {
        const infoStr = window.AndroidTerminal.getTerminalInfo();
        const info = JSON.parse(infoStr);
        return {
          serialNumber: info.serial_number || 'ANDROID_001',
          model: info.model || 'PAX A920 Pro (Android)',
          firmwareVersion: info.firmware_version || '1.0.0-android',
          batteryLevel: info.battery_level || 85,
          isHardwareAvailable: true,
          capabilities: ['card_reader', 'printer', 'display', 'audio', 'vibration']
        };
      } catch (error) {
        console.warn('Failed to get Android terminal info:', error);
      }
    }

    // Fallback terminal info
    return {
      serialNumber: this.generateSerialNumber(),
      model: 'PAX A920 Pro (TypeScript)',
      firmwareVersion: '1.0.0-ts',
      batteryLevel: this.getBatteryLevel(),
      isHardwareAvailable: this.isHardwareAvailable(),
      capabilities: this.getAvailableCapabilities()
    };
  }

  // Start card reading
  public async startCardReading(
    callback: (data: CardData) => void,
    errorCallback?: (error: string) => void,
    timeout: number = 30000
  ): Promise<void> {
    if (this.isReading) {
      throw new Error('Card reading already in progress');
    }

    this.isReading = true;
    this.cardReadingCallback = callback;
    this.cardErrorCallback = errorCallback;

    // Set timeout
    this.readingTimeout = setTimeout(() => {
      this.handleCardReadError('Card reading timeout');
    }, timeout);

    try {
      if (window.AndroidTerminal) {
        // Use Android WebView interface
        window.AndroidTerminal.readCard('onCardReadComplete');
      } else if (this.isWebUSBAvailable()) {
        // Use Web USB for USB card readers
        await this.readCardViaWebUSB();
      } else if (this.isWebSerialAvailable()) {
        // Use Web Serial for serial card readers
        await this.readCardViaWebSerial();
      } else {
        // Simulation mode
        await this.simulateCardReading();
      }
    } catch (error) {
      this.handleCardReadError(`Card reading failed: ${error}`);
    }
  }

  // Stop card reading
  public stopCardReading(): void {
    this.isReading = false;
    this.cardReadingCallback = undefined;
    this.cardErrorCallback = undefined;

    if (this.readingTimeout) {
      clearTimeout(this.readingTimeout);
      this.readingTimeout = undefined;
    }

    console.log('Card reading stopped');
  }

  // Web USB card reading (for USB card readers)
  private async readCardViaWebUSB(): Promise<void> {
    try {
      const device = await (navigator as any).usb.requestDevice({
        filters: [
          { vendorId: 0x0b00 }, // PAX vendor ID
          { vendorId: 0x1234 }, // Generic card reader
        ]
      });

      await device.open();
      console.log('USB card reader connected');

      // Simulate reading from USB device
      setTimeout(() => {
        const cardData: CardData = {
          pan: '****************',
          expiryDate: '1225',
          cardholderName: 'USB CARDHOLDER',
          cardType: 'chip',
          track2: '****************=25121010000000000000'
        };
        this.handleCardReadSuccess(cardData);
      }, 3000);

    } catch (error) {
      throw new Error(`USB card reader error: ${error}`);
    }
  }

  // Web Serial card reading (for serial card readers)
  private async readCardViaWebSerial(): Promise<void> {
    try {
      const port = await (navigator as any).serial.requestPort();
      await port.open({ baudRate: 9600 });
      console.log('Serial card reader connected');

      // Simulate reading from serial device
      setTimeout(() => {
        const cardData: CardData = {
          pan: '****************',
          expiryDate: '1225',
          cardholderName: 'SERIAL CARDHOLDER',
          cardType: 'swipe',
          track2: '****************=25121010000000000000'
        };
        this.handleCardReadSuccess(cardData);
      }, 3000);

    } catch (error) {
      throw new Error(`Serial card reader error: ${error}`);
    }
  }

  // Simulate card reading for development/testing
  private async simulateCardReading(): Promise<void> {
    console.log('Simulating card reading...');

    // Simulate different card types randomly
    const cardTypes: Array<'chip' | 'swipe' | 'tap'> = ['chip', 'swipe', 'tap'];
    const randomCardType = cardTypes[Math.floor(Math.random() * cardTypes.length)];

    const simulatedCards = [
      {
        pan: '****************',
        expiryDate: '1225',
        cardholderName: 'TEST CARDHOLDER',
        cardType: randomCardType,
        track2: '****************=25121010000000000000'
      },
      {
        pan: '****************',
        expiryDate: '0327',
        cardholderName: 'JANE DOE',
        cardType: randomCardType,
        track2: '****************=27031010000000000000'
      },
      {
        pan: '***************',
        expiryDate: '1224',
        cardholderName: 'JOHN SMITH',
        cardType: randomCardType,
        track2: '***************=24121010000000000000'
      }
    ];

    const randomCard = simulatedCards[Math.floor(Math.random() * simulatedCards.length)];

    // Simulate reading delay
    setTimeout(() => {
      this.handleCardReadSuccess(randomCard as CardData);
    }, 2000 + Math.random() * 2000); // 2-4 seconds
  }

  // Handle successful card read
  private handleCardReadSuccess(cardData: CardData): void {
    if (!this.isReading) return;

    this.isReading = false;
    if (this.readingTimeout) {
      clearTimeout(this.readingTimeout);
      this.readingTimeout = undefined;
    }

    // Validate card data
    const validation = this.validateCardData(cardData);
    if (!validation.isValid) {
      this.handleCardReadError(`Invalid card data: ${validation.errors.join(', ')}`);
      return;
    }

    // Success beep
    this.beep(200);

    if (this.cardReadingCallback) {
      this.cardReadingCallback(cardData);
    }
  }

  // Handle card read error
  private handleCardReadError(error: string): void {
    if (!this.isReading) return;

    this.isReading = false;
    if (this.readingTimeout) {
      clearTimeout(this.readingTimeout);
      this.readingTimeout = undefined;
    }

    // Error beep
    this.beep(500);

    console.error('Card reading error:', error);

    if (this.cardErrorCallback) {
      this.cardErrorCallback(error);
    }
  }

  // Print receipt
  public async printReceipt(receiptText: string, options?: PrinterOptions): Promise<boolean> {
    try {
      if (window.AndroidTerminal) {
        return window.AndroidTerminal.printText(receiptText);
      } else {
        // Browser print fallback
        return this.browserPrint(receiptText, options);
      }
    } catch (error) {
      console.error('Print failed:', error);
      return false;
    }
  }

  // Browser print fallback
  private browserPrint(text: string, options?: PrinterOptions): boolean {
    try {
      const printWindow = window.open('', '_blank');
      if (!printWindow) return false;

      const fontSize = options?.fontSize || 'medium';
      const alignment = options?.alignment || 'left';
      const fontWeight = options?.bold ? 'bold' : 'normal';

      const htmlContent = `
        <html>
          <head>
            <title>Receipt</title>
            <style>
              body {
                font-family: 'Courier New', monospace;
                font-size: ${fontSize === 'small' ? '10px' : fontSize === 'large' ? '14px' : '12px'};
                font-weight: ${fontWeight};
                line-height: 1.2;
                margin: 0;
                padding: 10px;
                white-space: pre-wrap;
                text-align: ${alignment};
              }
              @media print {
                body { margin: 0; padding: 5px; }
              }
            </style>
          </head>
          <body>${text}</body>
        </html>
      `;
      printWindow.document.open();
      printWindow.document.write(htmlContent);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();
      return true;
    } catch (error) {
      console.error('Browser print failed:', error);
      return false;
    }
  }

  // Audio feedback
  public beep(duration: number = 200): void {
    if (window.AndroidTerminal) {
      window.AndroidTerminal.beep(duration);
      return;
    }

    // Browser audio fallback
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = 800;
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration / 1000);
    } catch (error) {
      console.warn('Audio not available:', error);
    }
  }

  // Vibration feedback
  public vibrate(duration: number = 200): void {
    if (window.AndroidTerminal) {
      window.AndroidTerminal.vibrate(duration);
      return;
    }

    // Browser vibration API
    if ('vibrate' in navigator) {
      navigator.vibrate(duration);
    }
  }

  // Show message
  public showMessage(message: string): void {
    if (window.AndroidTerminal) {
      window.AndroidTerminal.showToast(message);
    } else {
      console.log('Terminal Message:', message);
    }
  }

  // Utility methods
  public formatCardNumber(pan: string): string {
    const cleaned = pan.replace(/\D/g, '');
    return cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
  }

  public maskCardNumber(pan: string): string {
    const cleaned = pan.replace(/\D/g, '');
    if (cleaned.length < 4) return '****';
    return '**** **** **** ' + cleaned.slice(-4);
  }

  // Validate card data
  private validateCardData(cardData: CardData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!cardData.pan || cardData.pan.length < 13 || cardData.pan.length > 19) {
      errors.push('Invalid card number length');
    }

    if (!cardData.expiryDate || !/^\d{4}$/.test(cardData.expiryDate)) {
      errors.push('Invalid expiry date format');
    }

    if (cardData.expiryDate) {
      const year = parseInt(cardData.expiryDate.substring(0, 2)) + 2000;
      const month = parseInt(cardData.expiryDate.substring(2, 4));
      const expiry = new Date(year, month - 1);
      if (expiry < new Date()) {
        errors.push('Card has expired');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Helper methods
  private generateSerialNumber(): string {
    return 'TS' + Date.now().toString().slice(-6);
  }

  private getBatteryLevel(): number {
    // Try to get actual battery level
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        return Math.round(battery.level * 100);
      });
    }
    return 100; // Default to 100%
  }

  private getAvailableCapabilities(): string[] {
    const capabilities = ['display', 'audio'];

    if (this.isHardwareAvailable()) {
      capabilities.push('card_reader');
    }

    if ('vibrate' in navigator) {
      capabilities.push('vibration');
    }

    if (typeof window.print === 'function') {
      capabilities.push('printer');
    }

    return capabilities;
  }
}

// Export singleton instance
export const terminalHardware = PAXTerminalHardware.getInstance();
