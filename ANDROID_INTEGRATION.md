# 📱 PAX A920 Pro Android Integration Guide

## 🔧 **Android WebView Integration for Card Reading**

### **1. Android App Structure**

```kotlin
// MainActivity.kt
package com.paxpos.terminal

import android.os.Bundle
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.JavascriptInterface
import androidx.appcompat.app.AppCompatActivity
import com.pax.dal.ICardReaderHelper
import com.pax.dal.entity.EReaderType
import com.pax.dal.entity.CardSlotType
import org.json.JSONObject

class MainActivity : AppCompatActivity() {
    private lateinit var webView: WebView
    private lateinit var cardReaderHelper: ICardReaderHelper
    private lateinit var terminalBridge: TerminalBridge

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // Initialize PAX SDK
        initializePAXSDK()
        
        // Setup WebView
        setupWebView()
    }

    private fun initializePAXSDK() {
        // Initialize PAX terminal SDK
        cardReaderHelper = getDal(this).getCardReaderHelper()
        terminalBridge = TerminalBridge(cardReaderHelper, this)
    }

    private fun setupWebView() {
        webView = findViewById(R.id.webview)
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            allowFileAccess = true
            allowContentAccess = true
        }

        // Add JavaScript interface
        webView.addJavascriptInterface(terminalBridge, "Android")
        
        // Load POS application
        webView.loadUrl("file:///android_asset/index.html")
        
        webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                // Inject terminal info
                val terminalInfo = terminalBridge.getTerminalInfo()
                webView.evaluateJavascript(
                    "window.terminalInfo = $terminalInfo;",
                    null
                )
            }
        }
    }
}
```

### **2. Terminal Bridge Class**

```kotlin
// TerminalBridge.kt
package com.paxpos.terminal

import android.content.Context
import android.webkit.JavascriptInterface
import android.widget.Toast
import com.pax.dal.ICardReaderHelper
import com.pax.dal.entity.*
import com.pax.dal.exceptions.PedDevException
import org.json.JSONObject
import kotlinx.coroutines.*

class TerminalBridge(
    private val cardReaderHelper: ICardReaderHelper,
    private val context: Context
) {
    private var cardReadingJob: Job? = null
    private var currentCallback: String? = null

    @JavascriptInterface
    fun readCard(callbackName: String) {
        currentCallback = callbackName
        startCardReading()
    }

    private fun startCardReading() {
        cardReadingJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                // Configure card reader
                val cardSlotTypes = arrayOf(
                    CardSlotType.ICC1,    // Chip card
                    CardSlotType.RF,      // Contactless
                    CardSlotType.SWIPE    // Magnetic stripe
                )

                // Start card reading
                val cardInfo = cardReaderHelper.readCard(
                    EReaderType.BAISC,
                    cardSlotTypes,
                    30000  // 30 second timeout
                )

                // Process card data
                val cardData = processCardInfo(cardInfo)
                
                // Return to main thread and call JavaScript
                withContext(Dispatchers.Main) {
                    val webView = (context as MainActivity).findViewById<WebView>(R.id.webview)
                    webView.evaluateJavascript(
                        "$currentCallback('${cardData.toString()}');",
                        null
                    )
                }

            } catch (e: PedDevException) {
                withContext(Dispatchers.Main) {
                    val webView = (context as MainActivity).findViewById<WebView>(R.id.webview)
                    webView.evaluateJavascript(
                        "onCardReadError('${e.message}');",
                        null
                    )
                }
            }
        }
    }

    private fun processCardInfo(cardInfo: CardInfo): JSONObject {
        val cardData = JSONObject()
        
        try {
            // Extract PAN (Primary Account Number)
            cardInfo.pan?.let { pan ->
                cardData.put("pan", pan)
            }

            // Extract expiry date
            cardInfo.expDate?.let { expiry ->
                cardData.put("expiryDate", expiry)
            }

            // Extract cardholder name
            cardInfo.holderName?.let { name ->
                cardData.put("cardholderName", name)
            }

            // Extract track data
            cardInfo.track1?.let { track1 ->
                cardData.put("track1", track1)
            }

            cardInfo.track2?.let { track2 ->
                cardData.put("track2", track2)
            }

            // Determine card type based on how it was read
            val cardType = when {
                cardInfo.cardExistslot == CardSlotType.ICC1 -> "chip"
                cardInfo.cardExistslot == CardSlotType.RF -> "tap"
                cardInfo.cardExistslot == CardSlotType.SWIPE -> "swipe"
                else -> "unknown"
            }
            cardData.put("cardType", cardType)

            // Extract EMV data if available
            cardInfo.emvData?.let { emvData ->
                cardData.put("emvData", emvData.toString())
            }

        } catch (e: Exception) {
            cardData.put("error", "Failed to process card data: ${e.message}")
        }

        return cardData
    }

    @JavascriptInterface
    fun printText(text: String): Boolean {
        return try {
            val printerHelper = getDal(context).getPrinterHelper()
            
            // Configure printer
            printerHelper.init()
            printerHelper.setGray(5) // Set print density
            
            // Print text
            printerHelper.printStr(text, null)
            printerHelper.start()
            
            true
        } catch (e: Exception) {
            false
        }
    }

    @JavascriptInterface
    fun getTerminalInfo(): String {
        val terminalInfo = JSONObject()
        
        try {
            val sysParam = getDal(context).getSysParam()
            
            terminalInfo.put("serialNumber", sysParam.get("serialNumber") ?: "Unknown")
            terminalInfo.put("model", "PAX A920 Pro")
            terminalInfo.put("firmwareVersion", sysParam.get("firmwareVersion") ?: "1.0.0")
            
            // Get battery level
            val batteryLevel = getBatteryLevel()
            terminalInfo.put("batteryLevel", batteryLevel)
            
        } catch (e: Exception) {
            terminalInfo.put("error", "Failed to get terminal info: ${e.message}")
        }
        
        return terminalInfo.toString()
    }

    @JavascriptInterface
    fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun getBatteryLevel(): Int {
        return try {
            val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
        } catch (e: Exception) {
            100 // Default to 100% if unable to read
        }
    }

    fun stopCardReading() {
        cardReadingJob?.cancel()
        currentCallback = null
    }
}
```

### **3. Android Manifest Configuration**

```xml
<!-- AndroidManifest.xml -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.paxpos.terminal">

    <!-- PAX Terminal Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    
    <!-- PAX SDK Permissions -->
    <uses-permission android:name="com.pax.permission.PED" />
    <uses-permission android:name="com.pax.permission.PRINTER" />
    <uses-permission android:name="com.pax.permission.SCANNER" />

    <application
        android:name=".PaxApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme"
        android:hardwareAccelerated="true">

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- PAX Terminal Service -->
        <service
            android:name="com.pax.service.PaxService"
            android:enabled="true"
            android:exported="false" />

    </application>
</manifest>
```

### **4. Build Configuration**

```gradle
// app/build.gradle
android {
    compileSdkVersion 33
    
    defaultConfig {
        applicationId "com.paxpos.terminal"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1
        versionName "1.0.0"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    
    // PAX SDK Dependencies
    implementation files('libs/pax-dal.jar')
    implementation files('libs/pax-common.jar')
    implementation files('libs/pax-printer.jar')
}
```

### **5. Asset Files Structure**

```
app/src/main/assets/
├── index.html
├── static/
│   ├── css/
│   │   └── main.css
│   └── js/
│       ├── main.js
│       └── chunk-vendors.js
└── manifest.json
```

### **6. HTML Entry Point**

```html
<!-- app/src/main/assets/index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no">
    <title>PAX A920 Pro POS Terminal</title>
    <link href="./static/css/main.css" rel="stylesheet">
</head>
<body>
    <noscript>
        <strong>We're sorry but PAX POS Terminal doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    
    <!-- Built files -->
    <script src="./static/js/chunk-vendors.js"></script>
    <script src="./static/js/main.js"></script>
    
    <script>
        // Initialize terminal when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PAX Terminal WebView initialized');
            
            // Check if Android interface is available
            if (typeof Android !== 'undefined') {
                console.log('PAX Terminal hardware interface available');
                window.PAXTerminalAvailable = true;
            } else {
                console.log('Running in browser mode');
                window.PAXTerminalAvailable = false;
            }
        });
    </script>
</body>
</html>
```

### **7. Build and Deploy Script**

```bash
#!/bin/bash
# build-android.sh

echo "🏗️ Building PAX A920 Pro Android App..."

# Build React app
cd apps/frontend
npm run build

# Copy built files to Android assets
rm -rf ../../android/app/src/main/assets/static
cp -r dist/* ../../android/app/src/main/assets/

# Build Android APK
cd ../../android
./gradlew assembleRelease

# Sign APK
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 \
  -keystore release-key.keystore \
  app/build/outputs/apk/release/app-release-unsigned.apk \
  pax_pos_key

# Align APK
zipalign -v 4 \
  app/build/outputs/apk/release/app-release-unsigned.apk \
  app/build/outputs/apk/release/pax-pos-terminal.apk

echo "✅ Android APK built: android/app/build/outputs/apk/release/pax-pos-terminal.apk"
```

### **8. Installation on PAX Terminal**

```bash
# Install on PAX A920 Pro
adb install -r pax-pos-terminal.apk

# Set as kiosk app (requires device owner)
adb shell dpm set-device-owner com.paxpos.terminal/.DeviceAdminReceiver

# Configure auto-start
adb shell settings put global policy_control immersive.full=com.paxpos.terminal

# Start application
adb shell am start -n com.paxpos.terminal/.MainActivity
```

This integration allows your React frontend to directly communicate with the PAX A920 Pro hardware for real card reading, printing, and terminal management.
