import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import axios from 'axios';
import { ApiService } from '../api';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

describe('ApiService', () => {
  let apiService: ApiService;

  beforeEach(() => {
    apiService = new ApiService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('simulatePayment', () => {
    it('should simulate payment successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            transaction: {
              _id: '123',
              amount: 1000,
              status: 'success',
              createdAt: '2024-01-01T00:00:00.000Z',
              updatedAt: '2024-01-01T00:00:00.000Z',
            },
          },
        },
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      const request = {
        amount: 1000,
        status: 'success' as const,
        protocolCode: '101.1' as const,
      };

      const result = await apiService.simulatePayment(request);

      expect(mockedAxios.post).toHaveBeenCalledWith('/payment/simulate', request);
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('Network error');
      mockedAxios.post.mockRejectedValue(mockError);

      const request = {
        amount: 1000,
        status: 'success' as const,
      };

      await expect(apiService.simulatePayment(request)).rejects.toThrow('Network error');
    });
  });

  describe('triggerProtocol', () => {
    it('should trigger protocol successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            protocolCode: '101.1',
            description: 'Authorization Request',
            bankResponse: { success: true },
          },
        },
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      const request = {
        protocolEventCode: '101.1' as const,
        transactionId: '123',
        amount: 1000,
      };

      const result = await apiService.triggerProtocol(request);

      expect(mockedAxios.post).toHaveBeenCalledWith('/protocol/trigger', request);
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('getConnectionToken', () => {
    it('should get connection token successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            secret: 'pst_test_123456789',
          },
        },
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      const result = await apiService.getConnectionToken();

      expect(mockedAxios.post).toHaveBeenCalledWith('/stripe/connection_token');
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('createPaymentIntent', () => {
    it('should create payment intent successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            client_secret: 'pi_test_123_secret',
            id: 'pi_test_123',
            amount: 1000,
            currency: 'usd',
            status: 'requires_payment_method',
          },
        },
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      const request = {
        amount: 1000,
        currency: 'usd',
        metadata: { test: true },
      };

      const result = await apiService.createPaymentIntent(request);

      expect(mockedAxios.post).toHaveBeenCalledWith('/stripe/payment_intent', request);
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('capturePaymentIntent', () => {
    it('should capture payment intent successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            id: 'pi_test_123',
            status: 'succeeded',
            amount_received: 1000,
          },
        },
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      const request = {
        paymentIntentId: 'pi_test_123',
        amountToCapture: 1000,
      };

      const result = await apiService.capturePaymentIntent(request);

      expect(mockedAxios.post).toHaveBeenCalledWith('/stripe/capture_intent', request);
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('getTransactions', () => {
    it('should get transactions successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            transactions: [
              {
                _id: '123',
                amount: 1000,
                status: 'success',
                createdAt: '2024-01-01T00:00:00.000Z',
                updatedAt: '2024-01-01T00:00:00.000Z',
              },
            ],
            pagination: {
              limit: 50,
              offset: 0,
              count: 1,
            },
          },
        },
      };

      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await apiService.getTransactions(50, 0);

      expect(mockedAxios.get).toHaveBeenCalledWith('/transaction?limit=50&offset=0');
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle optional parameters', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: { transactions: [], pagination: {} },
        },
      };

      mockedAxios.get.mockResolvedValue(mockResponse);

      await apiService.getTransactions();

      expect(mockedAxios.get).toHaveBeenCalledWith('/transaction?');
    });
  });

  describe('getTransactionById', () => {
    it('should get transaction by ID successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            _id: '123',
            amount: 1000,
            status: 'success',
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
          },
        },
      };

      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await apiService.getTransactionById('123');

      expect(mockedAxios.get).toHaveBeenCalledWith('/transaction/123');
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('createTransaction', () => {
    it('should create transaction successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            transaction: {
              _id: '123',
              amount: 1000,
              status: 'success',
              createdAt: '2024-01-01T00:00:00.000Z',
              updatedAt: '2024-01-01T00:00:00.000Z',
            },
            receipt: 'Receipt content',
          },
        },
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      const request = {
        amount: 1000,
        status: 'success' as const,
      };

      const result = await apiService.createTransaction(request);

      expect(mockedAxios.post).toHaveBeenCalledWith('/transaction', request);
      expect(result).toEqual(mockResponse.data);
    });
  });
});
