import { useState, useEffect, useRef } from 'react';
import WASMLoader, { WASMTerminal } from '../services/wasmLoader';

type TransactionState = 
  | 'idle'
  | 'amount-entry'
  | 'waiting-card'
  | 'card-reading'
  | 'authorizing'
  | 'approved'
  | 'declined'
  | 'printing'
  | 'complete'
  | 'error';

interface CardData {
  pan?: string;
  expiry_date?: string;
  cardholder_name?: string;
  track1?: string;
  track2?: string;
  card_type?: string;
  service_code?: string;
}

interface TerminalInfo {
  serial_number: string;
  model: string;
  firmware_version: string;
  battery_level: number;
  is_hardware_available: boolean;
}

interface RealTimeTransaction {
  id: string;
  amount: number;
  status: TransactionState;
  paymentIntentId?: string;
  cardData?: CardData;
  authCode?: string;
  receiptData?: string;
  errorMessage?: string;
  timestamp: Date;
}

// WASM module interface
declare global {
  interface Window {
    paxTerminalWasm?: any;
  }
}

export function WASMTerminalPOS() {
  const [transaction, setTransaction] = useState<RealTimeTransaction>({
    id: '',
    amount: 0,
    status: 'idle',
    timestamp: new Date()
  });

  const [amountInput, setAmountInput] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [terminalInfo, setTerminalInfo] = useState<TerminalInfo | null>(null);
  const [wasmLoaded, setWasmLoaded] = useState(false);
  const [manualCardData, setManualCardData] = useState({
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    cardholderName: ''
  });

  const intervalRef = useRef<NodeJS.Timeout>();
  const paxTerminalRef = useRef<WASMTerminal | null>(null);

  // Load WASM module
  useEffect(() => {
    const loadWASM = async () => {
      try {
        // Load terminal using WASM loader with fallback
        const terminal = await WASMLoader.loadTerminal();
        paxTerminalRef.current = terminal;

        // Get terminal info
        const info = await terminal.get_terminal_info();
        setTerminalInfo(info);

        // Check if WASM is actually available
        const isWASMAvailable = WASMLoader.isWASMAvailable();
        setWasmLoaded(isWASMAvailable);

        if (isWASMAvailable) {
          console.log('WASM PAX Terminal module loaded successfully');
        } else {
          console.log('Using fallback terminal implementation');
          setShowManualEntry(true);
        }
      } catch (error) {
        console.error('Failed to load terminal:', error);

        // Create fallback terminal info
        setTerminalInfo({
          serial_number: 'ERROR_001',
          model: 'PAX A920 Pro (Error)',
          firmware_version: '1.0.0-error',
          battery_level: 0,
          is_hardware_available: false
        });

        setShowManualEntry(true);
        setWasmLoaded(false);
      }
    };

    loadWASM();
  }, []);

  // Countdown timer for card reading
  useEffect(() => {
    if (transaction.status === 'waiting-card') {
      setCountdown(30);
      intervalRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            setTransaction(prev => ({
              ...prev,
              status: 'error',
              errorMessage: 'Card reading timeout - please try again'
            }));
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [transaction.status]);

  const isTerminalAvailable = () => {
    return wasmLoaded && paxTerminalRef.current?.is_hardware_available();
  };

  const startCardReading = async () => {
    if (!paxTerminalRef.current) {
      setShowManualEntry(true);
      return;
    }

    try {
      await paxTerminalRef.current.start_card_reading((cardData: CardData) => {
        // Success callback
        paxTerminalRef.current?.beep(200);
        setTransaction(prev => ({
          ...prev,
          status: 'card-reading',
          cardData
        }));

        setTimeout(() => {
          processCardData(cardData);
        }, 2000);
      });
    } catch (error) {
      console.warn('Card reading failed, showing manual entry');
      setShowManualEntry(true);
    }
  };

  const processCardData = async (cardData: CardData) => {
    setTransaction(prev => ({ ...prev, status: 'authorizing' }));

    try {
      const response = await fetch('/api/v1/payments/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: transaction.amount,
          currency: 'usd',
          payment_method_data: {
            type: 'card',
            card: {
              number: cardData.pan?.slice(-4) || '****',
              exp_month: cardData.expiry_date?.slice(2, 4) || '12',
              exp_year: cardData.expiry_date?.slice(0, 2) || '25',
            }
          },
          confirm: true,
          metadata: {
            card_type: cardData.card_type,
            terminal_id: terminalInfo?.serial_number || 'WASM_001'
          }
        }),
      });

      const paymentResult = await response.json();

      if (paymentResult.success && paymentResult.data.status === 'succeeded') {
        setTransaction(prev => ({
          ...prev,
          status: 'approved',
          paymentIntentId: paymentResult.data.id,
          authCode: generateAuthCode()
        }));

        setTimeout(() => {
          generateAndPrintReceipt();
        }, 2000);
      } else {
        setTransaction(prev => ({
          ...prev,
          status: 'declined',
          errorMessage: paymentResult.error || 'Payment was declined'
        }));
      }
    } catch (error) {
      setTransaction(prev => ({
        ...prev,
        status: 'error',
        errorMessage: 'Payment processing failed'
      }));
    }
  };

  const processManualCard = async () => {
    if (!manualCardData.cardNumber || !manualCardData.expiryMonth || !manualCardData.expiryYear || !manualCardData.cvv) {
      alert('Please fill in all card details');
      return;
    }

    const cardData: CardData = {
      pan: manualCardData.cardNumber,
      expiry_date: `${manualCardData.expiryYear}${manualCardData.expiryMonth}`,
      cardholder_name: manualCardData.cardholderName,
      card_type: 'manual'
    };

    setShowManualEntry(false);
    setTransaction(prev => ({ ...prev, cardData }));
    await processCardData(cardData);
  };

  const startNewTransaction = () => {
    const newId = `TXN_${Date.now()}`;
    setTransaction({
      id: newId,
      amount: 0,
      status: 'amount-entry',
      timestamp: new Date()
    });
    setAmountInput('');
    setShowManualEntry(false);
    setManualCardData({
      cardNumber: '',
      expiryMonth: '',
      expiryYear: '',
      cvv: '',
      cardholderName: ''
    });
  };

  const processPayment = () => {
    if (!amountInput || parseFloat(amountInput) <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    const amount = Math.round(parseFloat(amountInput) * 100);
    setTransaction(prev => ({
      ...prev,
      amount,
      status: 'waiting-card'
    }));

    // Start card reading
    startCardReading();
  };

  const generateAndPrintReceipt = async () => {
    setTransaction(prev => ({ ...prev, status: 'printing' }));

    try {
      const response = await fetch(`/api/v1/receipts/${transaction.id}`);
      const receiptData = await response.json();

      if (receiptData.success) {
        const receiptText = receiptData.data.customerReceipt;
        setTransaction(prev => ({
          ...prev,
          receiptData: receiptText,
          status: 'complete'
        }));

        // Print using WASM module
        if (paxTerminalRef.current) {
          await paxTerminalRef.current.print_receipt(receiptText);
        }
      }
    } catch (error) {
      console.error('Receipt generation failed:', error);
      setTransaction(prev => ({ ...prev, status: 'complete' }));
    }
  };

  const generateAuthCode = () => {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  };

  const getStatusDisplay = () => {
    switch (transaction.status) {
      case 'idle':
        return { text: 'Ready', icon: '💳', color: 'bg-blue-500' };
      case 'amount-entry':
        return { text: 'Enter Amount', icon: '💰', color: 'bg-yellow-500' };
      case 'waiting-card':
        return { text: 'Insert or Tap Card', icon: '📱', color: 'bg-orange-500' };
      case 'card-reading':
        return { text: 'Reading Card...', icon: '📖', color: 'bg-blue-600' };
      case 'authorizing':
        return { text: 'Authorizing...', icon: '⚡', color: 'bg-purple-600' };
      case 'approved':
        return { text: 'Approved!', icon: '✅', color: 'bg-green-500' };
      case 'declined':
        return { text: 'Declined', icon: '❌', color: 'bg-red-500' };
      case 'printing':
        return { text: 'Printing...', icon: '🖨️', color: 'bg-indigo-500' };
      case 'complete':
        return { text: 'Complete', icon: '🎉', color: 'bg-green-600' };
      case 'error':
        return { text: 'Error', icon: '⚠️', color: 'bg-red-600' };
      default:
        return { text: 'Ready', icon: '💳', color: 'bg-gray-500' };
    }
  };

  const status = getStatusDisplay();

  return (
    <div className="w-full max-w-md mx-auto h-full">
      <div className="bg-white rounded-xl sm:rounded-2xl shadow-2xl border border-slate-200 overflow-hidden h-full flex flex-col">
        {/* Status Header */}
        <div className={`${status.color} p-4 sm:p-6 text-white text-center flex-shrink-0`}>
          <div className="text-3xl sm:text-4xl lg:text-6xl mb-2 sm:mb-3">{status.icon}</div>
          <h1 className="text-lg sm:text-xl lg:text-3xl font-bold mb-1 sm:mb-2">{status.text}</h1>
          {transaction.amount > 0 && (
            <div className="text-lg sm:text-xl lg:text-2xl font-mono">
              ${(transaction.amount / 100).toFixed(2)}
            </div>
          )}
          {countdown > 0 && (
            <div className="text-sm sm:text-lg mt-1 sm:mt-2 opacity-90">
              {countdown}s remaining
            </div>
          )}
        </div>

        {/* Terminal Info */}
        {terminalInfo && (
          <div className="bg-slate-50 px-4 py-2 text-center border-b border-slate-200">
            <div className="text-xs text-slate-600">
              {terminalInfo.model} • {terminalInfo.serial_number} • 
              {terminalInfo.is_hardware_available ? ' Hardware Ready' : ' Simulation Mode'}
            </div>
          </div>
        )}

        {/* Content Area */}
        <div className="flex-1 p-3 sm:p-4 lg:p-6 overflow-auto">
          {/* Amount Entry */}
          {transaction.status === 'amount-entry' && (
            <div className="space-y-4 sm:space-y-6">
              <div>
                <label className="block text-base sm:text-lg font-semibold text-slate-700 mb-3 sm:mb-4 text-center">
                  Transaction Amount
                </label>
                <div className="relative">
                  <span className="absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-slate-500 text-xl sm:text-2xl">$</span>
                  <input
                    type="number"
                    value={amountInput}
                    onChange={(e) => setAmountInput(e.target.value)}
                    className="w-full pl-10 sm:pl-12 pr-4 py-3 sm:py-4 border-2 border-slate-300 rounded-xl sm:rounded-2xl focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-transparent text-xl sm:text-2xl lg:text-3xl font-bold text-center"
                    placeholder="0.00"
                    min="0.01"
                    step="0.01"
                    autoFocus
                  />
                </div>
              </div>
              
              <button
                onClick={processPayment}
                disabled={!amountInput || parseFloat(amountInput) <= 0}
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 sm:py-4 px-6 rounded-xl sm:rounded-2xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-bold text-base sm:text-lg shadow-lg active:scale-95"
              >
                💳 Process Payment
              </button>
            </div>
          )}

          {/* Waiting for Card */}
          {transaction.status === 'waiting-card' && (
            <div className="text-center space-y-4 sm:space-y-6">
              <div className="animate-pulse">
                <div className="text-4xl sm:text-6xl mb-3 sm:mb-4">💳</div>
                <p className="text-base sm:text-lg text-slate-600">
                  {isTerminalAvailable() 
                    ? 'Insert, tap, or swipe your card'
                    : 'Card reader not available'
                  }
                </p>
              </div>
              
              {!isTerminalAvailable() && (
                <button
                  onClick={() => setShowManualEntry(true)}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-xl hover:bg-blue-700 transition-colors font-semibold active:scale-95"
                >
                  Enter Card Details Manually
                </button>
              )}
            </div>
          )}

          {/* Manual Card Entry */}
          {showManualEntry && (
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-base sm:text-lg font-bold text-center text-slate-800">Enter Card Details</h3>
              
              <input
                type="text"
                placeholder="Card Number"
                value={manualCardData.cardNumber}
                onChange={(e) => setManualCardData(prev => ({ ...prev, cardNumber: e.target.value }))}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                maxLength={19}
              />
              
              <div className="grid grid-cols-2 gap-2 sm:gap-3">
                <select
                  value={manualCardData.expiryMonth}
                  onChange={(e) => setManualCardData(prev => ({ ...prev, expiryMonth: e.target.value }))}
                  className="px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                >
                  <option value="">Month</option>
                  {Array.from({ length: 12 }, (_, i) => (
                    <option key={i + 1} value={String(i + 1).padStart(2, '0')}>
                      {String(i + 1).padStart(2, '0')}
                    </option>
                  ))}
                </select>
                
                <select
                  value={manualCardData.expiryYear}
                  onChange={(e) => setManualCardData(prev => ({ ...prev, expiryYear: e.target.value }))}
                  className="px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                >
                  <option value="">Year</option>
                  {Array.from({ length: 10 }, (_, i) => (
                    <option key={i} value={String(new Date().getFullYear() + i).slice(-2)}>
                      {new Date().getFullYear() + i}
                    </option>
                  ))}
                </select>
              </div>
              
              <input
                type="text"
                placeholder="CVV"
                value={manualCardData.cvv}
                onChange={(e) => setManualCardData(prev => ({ ...prev, cvv: e.target.value }))}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                maxLength={4}
              />
              
              <input
                type="text"
                placeholder="Cardholder Name (Optional)"
                value={manualCardData.cardholderName}
                onChange={(e) => setManualCardData(prev => ({ ...prev, cardholderName: e.target.value }))}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
              />
              
              <div className="grid grid-cols-2 gap-2 sm:gap-3">
                <button
                  onClick={() => setShowManualEntry(false)}
                  className="bg-gray-600 text-white py-2 sm:py-3 px-4 rounded-lg sm:rounded-xl hover:bg-gray-700 transition-colors font-semibold text-sm sm:text-base active:scale-95"
                >
                  Cancel
                </button>
                <button
                  onClick={processManualCard}
                  className="bg-green-600 text-white py-2 sm:py-3 px-4 rounded-lg sm:rounded-xl hover:bg-green-700 transition-colors font-semibold text-sm sm:text-base active:scale-95"
                >
                  Process
                </button>
              </div>
            </div>
          )}

          {/* Processing States */}
          {['card-reading', 'authorizing'].includes(transaction.status) && (
            <div className="text-center space-y-4 sm:space-y-6">
              <div className="animate-spin rounded-full h-12 w-12 sm:h-16 sm:w-16 border-b-4 border-blue-600 mx-auto"></div>
              <div className="text-base sm:text-lg text-slate-600">
                {transaction.status === 'card-reading' && 'Do not remove your card...'}
                {transaction.status === 'authorizing' && 'Contacting your bank...'}
              </div>
            </div>
          )}

          {/* Success State */}
          {transaction.status === 'approved' && (
            <div className="text-center space-y-3 sm:space-y-4">
              <div className="text-4xl sm:text-6xl">✅</div>
              <div className="text-xl sm:text-2xl font-bold text-green-600">Payment Approved!</div>
              <div className="bg-green-50 border border-green-200 rounded-lg sm:rounded-xl p-3 sm:p-4">
                <div className="grid grid-cols-2 gap-2 sm:gap-3 text-xs sm:text-sm">
                  <div>
                    <span className="font-medium text-slate-600">Amount:</span>
                    <div className="text-base sm:text-lg font-bold">${(transaction.amount / 100).toFixed(2)}</div>
                  </div>
                  <div>
                    <span className="font-medium text-slate-600">Card:</span>
                    <div className="font-mono">****{transaction.cardData?.pan?.slice(-4) || '****'}</div>
                  </div>
                  <div className="col-span-2">
                    <span className="font-medium text-slate-600">Auth Code:</span>
                    <div className="font-mono text-base sm:text-lg">{transaction.authCode}</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Error States */}
          {['declined', 'error'].includes(transaction.status) && (
            <div className="text-center space-y-4 sm:space-y-6">
              <div className="text-4xl sm:text-6xl">❌</div>
              <div className="text-lg sm:text-xl font-bold text-red-600">
                {transaction.status === 'declined' ? 'Payment Declined' : 'Transaction Error'}
              </div>
              {transaction.errorMessage && (
                <div className="bg-red-50 border border-red-200 rounded-lg sm:rounded-xl p-3 sm:p-4 text-red-700 text-xs sm:text-sm">
                  {transaction.errorMessage}
                </div>
              )}
              <div className="grid grid-cols-2 gap-2 sm:gap-3">
                <button
                  onClick={() => setTransaction(prev => ({ ...prev, status: 'amount-entry' }))}
                  className="bg-blue-600 text-white py-2 sm:py-3 px-4 rounded-lg sm:rounded-xl hover:bg-blue-700 transition-colors font-semibold text-sm sm:text-base active:scale-95"
                >
                  🔄 Retry
                </button>
                <button
                  onClick={startNewTransaction}
                  className="bg-gray-600 text-white py-2 sm:py-3 px-4 rounded-lg sm:rounded-xl hover:bg-gray-700 transition-colors font-semibold text-sm sm:text-base active:scale-95"
                >
                  ❌ Cancel
                </button>
              </div>
            </div>
          )}

          {/* Complete State */}
          {transaction.status === 'complete' && (
            <div className="text-center space-y-4 sm:space-y-6">
              <div className="text-4xl sm:text-6xl">🎉</div>
              <div className="text-lg sm:text-xl font-bold text-green-600">Transaction Complete!</div>
              <div className="text-sm text-slate-600">Receipt has been printed</div>
              
              <button
                onClick={startNewTransaction}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 sm:py-4 px-6 rounded-xl sm:rounded-2xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-500 transition-all duration-200 font-bold text-base sm:text-lg shadow-lg active:scale-95"
              >
                🆕 New Transaction
              </button>
            </div>
          )}

          {/* Idle State */}
          {transaction.status === 'idle' && (
            <div className="text-center">
              <button
                onClick={startNewTransaction}
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 sm:py-6 px-6 sm:px-8 rounded-xl sm:rounded-2xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-500 transition-all duration-200 font-bold text-lg sm:text-xl shadow-lg active:scale-95"
              >
                🚀 Start Transaction
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
