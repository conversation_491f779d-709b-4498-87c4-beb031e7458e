{"$schema": "https://json.schemastore.org/tsconfig", "display": "Node Library", "extends": "./base.json", "compilerOptions": {"lib": ["es2021", "es2015.promise"], "module": "commonjs", "target": "es2021", "outDir": "dist", "rootDir": "src", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test/**/*"]}