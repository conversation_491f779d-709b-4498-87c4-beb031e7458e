{
  "$schema": "https://json.schemastore.org/tsconfig",
  "display": "Node Library",
  "extends": "./base.json",
  "compilerOptions": {
    "lib": ["es2021"], // Adjust based on Node.js version target
    "module": "commonjs", // Or "esnext" if using ES modules in Node.js
    "target": "es2021", // Adjust based on Node.js version target
    "outDir": "dist",
    "rootDir": "src"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "test/**/*"]
}
