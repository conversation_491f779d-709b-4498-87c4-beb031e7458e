#!/bin/bash

set -e

echo "📱 Creating APK for PAX A920 Pro POS Terminal"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Capacitor is installed
check_capacitor() {
    if ! command -v npx &> /dev/null; then
        print_error "npx is not available. Please install Node.js"
        exit 1
    fi
    
    print_status "Checking Capacitor installation..."
    
    # Install Capacitor if not available
    if ! npx @capacitor/cli --version &> /dev/null; then
        print_warning "Capacitor not found. Installing Capacitor..."
        npm install -g @capacitor/cli @capacitor/core @capacitor/android
    fi
    
    print_success "Capacitor is available"
}

# Initialize Capacitor project
init_capacitor() {
    print_status "Initializing Capacitor project..."
    
    # Create mobile directory
    mkdir -p mobile
    cd mobile
    
    # Initialize Capacitor
    npx @capacitor/cli init "PAX POS Terminal" "com.paxpos.terminal" --web-dir="../dist/apk-bundle"
    
    # Add Android platform
    npx @capacitor/cli add android
    
    cd ..
    
    print_success "Capacitor project initialized"
}

# Configure Capacitor
configure_capacitor() {
    print_status "Configuring Capacitor..."
    
    # Create capacitor.config.ts
    cat > mobile/capacitor.config.ts << 'EOF'
import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.paxpos.terminal',
  appName: 'PAX POS Terminal',
  webDir: '../dist/apk-bundle',
  server: {
    androidScheme: 'https'
  },
  android: {
    allowMixedContent: true,
    captureInput: true,
    webContentsDebuggingEnabled: true
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: "#3B82F6",
      showSpinner: false
    },
    StatusBar: {
      style: "dark",
      backgroundColor: "#3B82F6"
    }
  }
};

export default config;
EOF
    
    # Configure Android manifest
    cat > mobile/android/app/src/main/AndroidManifest.xml << 'EOF'
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.paxpos.terminal">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:hardwareAccelerated="true">

        <activity
            android:exported="true"
            android:launchMode="singleTask"
            android:name="com.paxpos.terminal.MainActivity"
            android:orientation="portrait"
            android:theme="@style/AppTheme.NoActionBarLaunch">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths"></meta-data>
        </provider>
    </application>

</manifest>
EOF
    
    print_success "Capacitor configured"
}

# Add PAX terminal integration
add_pax_integration() {
    print_status "Adding PAX terminal integration..."
    
    # Create MainActivity with PAX integration
    mkdir -p mobile/android/app/src/main/java/com/paxpos/terminal
    
    cat > mobile/android/app/src/main/java/com/paxpos/terminal/MainActivity.java << 'EOF'
package com.paxpos.terminal;

import android.os.Bundle;
import android.webkit.JavascriptInterface;
import android.widget.Toast;
import com.getcapacitor.BridgeActivity;
import com.getcapacitor.Plugin;
import org.json.JSONObject;

public class MainActivity extends BridgeActivity {
    
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Add PAX terminal bridge
        this.bridge.getWebView().addJavascriptInterface(new PAXTerminalBridge(), "Android");
    }
    
    public class PAXTerminalBridge {
        
        @JavascriptInterface
        public void readCard(String callback) {
            // Simulate card reading for now
            // In production, integrate with PAX SDK
            new Thread(() -> {
                try {
                    Thread.sleep(3000); // Simulate reading delay
                    
                    // Simulate card data
                    JSONObject cardData = new JSONObject();
                    cardData.put("pan", "****************");
                    cardData.put("expiry_date", "1225");
                    cardData.put("cardholder_name", "TEST CARDHOLDER");
                    cardData.put("card_type", "chip");
                    
                    runOnUiThread(() -> {
                        bridge.getWebView().evaluateJavascript(
                            callback + "('" + cardData.toString() + "');", null);
                    });
                    
                } catch (Exception e) {
                    runOnUiThread(() -> {
                        bridge.getWebView().evaluateJavascript(
                            "onCardReadError('" + e.getMessage() + "');", null);
                    });
                }
            }).start();
        }
        
        @JavascriptInterface
        public boolean printText(String text) {
            // Simulate printing
            runOnUiThread(() -> {
                Toast.makeText(MainActivity.this, "Printing receipt...", Toast.LENGTH_SHORT).show();
            });
            return true;
        }
        
        @JavascriptInterface
        public String getTerminalInfo() {
            try {
                JSONObject info = new JSONObject();
                info.put("serial_number", "CAP001");
                info.put("model", "PAX A920 Pro (Capacitor)");
                info.put("firmware_version", "1.0.0-capacitor");
                info.put("battery_level", 85);
                info.put("is_hardware_available", true);
                return info.toString();
            } catch (Exception e) {
                return "{}";
            }
        }
        
        @JavascriptInterface
        public void showToast(String message) {
            runOnUiThread(() -> {
                Toast.makeText(MainActivity.this, message, Toast.LENGTH_SHORT).show();
            });
        }
    }
}
EOF
    
    print_success "PAX integration added"
}

# Build APK
build_apk() {
    print_status "Building APK..."
    
    cd mobile
    
    # Sync Capacitor
    npx @capacitor/cli sync android
    
    # Build APK
    npx @capacitor/cli build android
    
    # Copy APK to root
    if [ -f "android/app/build/outputs/apk/debug/app-debug.apk" ]; then
        cp android/app/build/outputs/apk/debug/app-debug.apk ../pax-pos-terminal.apk
        print_success "APK created: pax-pos-terminal.apk"
    else
        print_error "APK build failed"
        exit 1
    fi
    
    cd ..
}

# Create installation script
create_install_script() {
    print_status "Creating installation script..."
    
    cat > install-apk.sh << 'EOF'
#!/bin/bash

echo "📱 Installing PAX POS Terminal APK"

# Check if ADB is available
if ! command -v adb &> /dev/null; then
    echo "❌ ADB is not installed. Please install Android SDK Platform Tools."
    exit 1
fi

# Check if device is connected
if ! adb devices | grep -q "device$"; then
    echo "❌ No Android device connected. Please connect your PAX A920 Pro terminal."
    exit 1
fi

# Install APK
echo "📦 Installing APK..."
adb install -r pax-pos-terminal.apk

# Launch app
echo "🚀 Launching PAX POS Terminal..."
adb shell am start -n com.paxpos.terminal/.MainActivity

echo "✅ Installation complete!"
echo ""
echo "The PAX POS Terminal is now installed and running on your device."
EOF
    
    chmod +x install-apk.sh
    
    print_success "Installation script created: install-apk.sh"
}

# Main function
main() {
    echo "Starting APK creation process..."
    
    # Build the frontend first
    print_status "Building frontend..."
    ./build-all.sh
    
    # Check Capacitor
    check_capacitor
    
    # Initialize Capacitor project
    if [ ! -d "mobile" ]; then
        init_capacitor
        configure_capacitor
        add_pax_integration
    fi
    
    # Build APK
    build_apk
    
    # Create installation script
    create_install_script
    
    echo ""
    echo "🎉 APK creation completed successfully!"
    echo "========================================"
    echo "📱 APK file: pax-pos-terminal.apk"
    echo "📋 Installation script: install-apk.sh"
    echo ""
    echo "To install on PAX A920 Pro:"
    echo "  1. Enable USB debugging on the terminal"
    echo "  2. Connect via USB"
    echo "  3. Run: ./install-apk.sh"
    echo ""
    echo "Or install manually:"
    echo "  adb install -r pax-pos-terminal.apk"
}

# Run main function
main "$@"
