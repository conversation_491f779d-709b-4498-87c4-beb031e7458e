import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: '2022-11-15' });

export const createConnectionToken = async () => {
  return await stripe.terminal.connectionTokens.create();
};

export const createPaymentIntent = async (amount: number, currency: string) => {
  return await stripe.paymentIntents.create({
    amount,
    currency,
    payment_method_types: ['card_present'],
    capture_method: 'manual',
  });
};

export const capturePaymentIntent = async (paymentIntentId: string) => {
  return await stripe.paymentIntents.capture(paymentIntentId);
};
