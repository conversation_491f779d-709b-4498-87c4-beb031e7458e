import Stripe from 'stripe';
import { env } from '../config/env';
import { logger } from '../config/logger';
import { z } from 'zod';

const stripeLogger = logger.child({ module: 'stripe' });

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-04-10',
  typescript: true,
});

const createPaymentIntentSchema = z.object({
  amount: z.number().positive().int(),
  currency: z.string().length(3),
  metadata: z.record(z.string()).optional(),
});

const capturePaymentIntentSchema = z.object({
  paymentIntentId: z.string().min(1),
  amountToCapture: z.number().positive().int().optional(),
});

export class StripeService {
  async createConnectionToken(): Promise<Stripe.Terminal.ConnectionToken> {
    try {
      stripeLogger.info('Creating Stripe Terminal connection token');
      const token = await stripe.terminal.connectionTokens.create();
      stripeLogger.info({ tokenId: token.id }, 'Connection token created successfully');
      return token;
    } catch (error) {
      stripeLogger.error({ error }, 'Failed to create connection token');
      throw new Error('Failed to create Stripe connection token');
    }
  }

  async createPaymentIntent(params: z.infer<typeof createPaymentIntentSchema>): Promise<Stripe.PaymentIntent> {
    try {
      const validatedParams = createPaymentIntentSchema.parse(params);

      stripeLogger.info({ amount: validatedParams.amount, currency: validatedParams.currency }, 'Creating payment intent');

      const paymentIntent = await stripe.paymentIntents.create({
        amount: validatedParams.amount,
        currency: validatedParams.currency,
        payment_method_types: ['card_present'],
        capture_method: 'manual',
        metadata: validatedParams.metadata || {},
      });

      stripeLogger.info({ paymentIntentId: paymentIntent.id }, 'Payment intent created successfully');
      return paymentIntent;
    } catch (error) {
      if (error instanceof z.ZodError) {
        stripeLogger.error({ error: error.errors }, 'Invalid payment intent parameters');
        throw new Error('Invalid payment intent parameters');
      }
      stripeLogger.error({ error }, 'Failed to create payment intent');
      throw new Error('Failed to create payment intent');
    }
  }

  async capturePaymentIntent(params: z.infer<typeof capturePaymentIntentSchema>): Promise<Stripe.PaymentIntent> {
    try {
      const validatedParams = capturePaymentIntentSchema.parse(params);

      stripeLogger.info({ paymentIntentId: validatedParams.paymentIntentId }, 'Capturing payment intent');

      const captureParams: Stripe.PaymentIntentCaptureParams = {};
      if (validatedParams.amountToCapture) {
        captureParams.amount_to_capture = validatedParams.amountToCapture;
      }

      const paymentIntent = await stripe.paymentIntents.capture(
        validatedParams.paymentIntentId,
        captureParams
      );

      stripeLogger.info({ paymentIntentId: paymentIntent.id }, 'Payment intent captured successfully');
      return paymentIntent;
    } catch (error) {
      if (error instanceof z.ZodError) {
        stripeLogger.error({ error: error.errors }, 'Invalid capture parameters');
        throw new Error('Invalid capture parameters');
      }
      stripeLogger.error({ error }, 'Failed to capture payment intent');
      throw new Error('Failed to capture payment intent');
    }
  }

  async retrievePaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
    try {
      stripeLogger.info({ paymentIntentId }, 'Retrieving payment intent');
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      return paymentIntent;
    } catch (error) {
      stripeLogger.error({ error, paymentIntentId }, 'Failed to retrieve payment intent');
      throw new Error('Failed to retrieve payment intent');
    }
  }
}

export const stripeService = new StripeService();
