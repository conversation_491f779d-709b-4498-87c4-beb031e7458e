import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';

let mongoServer: MongoMemoryServer;

// Setup test environment
beforeAll(async () => {
  // Start in-memory MongoDB instance
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  // Connect to the in-memory database
  await mongoose.connect(mongoUri);
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test_jwt_secret_at_least_32_characters_long';
  process.env.STRIPE_SECRET_KEY = 'sk_test_dummy_key_for_testing';
  process.env.STRIPE_PUBLISHABLE_KEY = 'pk_test_dummy_key_for_testing';
  process.env.STRIPE_WEBHOOK_SECRET = 'whsec_dummy_secret_for_testing';
  process.env.LOG_LEVEL = 'silent';
});

// Cleanup after all tests
afterAll(async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
});

// Clean database before each test
beforeEach(async () => {
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// Additional cleanup after each test
afterEach(async () => {
  // Reset any mocks or spies here if needed
});
