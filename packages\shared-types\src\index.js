"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProtocolEventCode = exports.TransactionStatus = void 0;
var TransactionStatus;
(function (TransactionStatus) {
    TransactionStatus["PENDING"] = "pending";
    TransactionStatus["SUCCESS"] = "success";
    TransactionStatus["FAILURE"] = "failure";
})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));
var ProtocolEventCode;
(function (ProtocolEventCode) {
    ProtocolEventCode["AUTHORIZATION"] = "101.1";
    ProtocolEventCode["CAPTURE"] = "101.3";
    ProtocolEventCode["REVERSAL"] = "101.5";
    ProtocolEventCode["SETTLEMENT"] = "101.8";
})(ProtocolEventCode || (exports.ProtocolEventCode = ProtocolEventCode = {}));
