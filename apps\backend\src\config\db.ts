import mongoose from 'mongoose';
import { env } from './env';
import logger from '../lib/logger'; // Use the default export

let isConnected: boolean = false;

export const connectDB = async (): Promise<void> => {
  if (isConnected) {
    logger.info('MongoDB is already connected.');
    return;
  }

  try {
    logger.info(`Attempting to connect to MongoDB at ${env.MONGO_URI.split('@')[1] || env.MONGO_URI}...`); // Avoid logging credentials

    mongoose.set('strictQuery', true); // Recommended for preparing for Mongoose 7

    await mongoose.connect(env.MONGO_URI, {
      // useNewUrlParser: true, // Deprecated in Mongoose 6+
      // useUnifiedTopology: true, // Deprecated in Mongoose 6+
      // useCreateIndex: true, // Not supported in Mongoose 6+ (indexes are created automatically)
      // useFindAndModify: false, // Not supported in Mongoose 6+ (use findOneAndUpdate, etc.)
      serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
      connectTimeoutMS: 10000, // Connection timeout
    });

    isConnected = true;
    logger.info('MongoDB connected successfully.');

    mongoose.connection.on('error', (err) => {
      logger.error('MongoDB connection error:', err);
      isConnected = false; // Update connection status on error
      // Potentially attempt to reconnect or handle error appropriately
    });

    mongoose.connection.on('disconnected', () => {
      logger.info('MongoDB disconnected.');
      isConnected = false;
      // Potentially attempt to reconnect
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('MongoDB reconnected.');
      isConnected = true;
    });

  } catch (error) {
    logger.error('Failed to connect to MongoDB:', error);
    // process.exit(1); // Exit if DB connection is critical for startup
    throw error; // Re-throw to be caught by the caller in app.ts
  }
};

export const disconnectDB = async (): Promise<void> => {
  if (!isConnected) {
    logger.info('MongoDB is not connected.');
    return;
  }
  try {
    await mongoose.disconnect();
    isConnected = false;
    logger.info('MongoDB disconnected successfully via disconnectDB function.');
  } catch (error) {
    logger.error('Error disconnecting MongoDB:', error);
    throw error;
  }
};

// Graceful shutdown for Mongoose connection
export const registerMongooseShutdown = (appCloseSignal: Promise<void>) => {
    appCloseSignal.then(async () => {
        if (isConnected) {
            logger.info('Closing MongoDB connection due to app shutdown...');
            await mongoose.disconnect();
            logger.info('MongoDB connection closed.');
        }
    });
};

// You can also register this directly with process signals if not using Fastify's app.close
// process.on('SIGINT', async () => {
//   await disconnectDB();
//   process.exit(0);
// });
// process.on('SIGTERM', async () => {
//   await disconnectDB();
//   process.exit(0);
// });
