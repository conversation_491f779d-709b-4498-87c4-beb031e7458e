import React, { useState } from 'react';
import { simulatePayment } from '../services/api';

export const PaymentSimulator: React.FC = () => {
  const [amount, setAmount] = useState(1000);
  const [status, setStatus] = useState<'success' | 'failure'>('success');
  const [result, setResult] = useState<any>(null);

  const handleSimulate = async () => {
    const res = await simulatePayment(amount, status);
    setResult(res);
  };

  return (
    <div>
      <h2>Simulate Payment</h2>
      <input type="number" value={amount} onChange={e => setAmount(Number(e.target.value))} />
      <select value={status} onChange={e => setStatus(e.target.value as any)}>
        <option value="success">Success</option>
        <option value="failure">Failure</option>
      </select>
      <button onClick={handleSimulate}>Simulate</button>
      {result && <pre>{JSON.stringify(result, null, 2)}</pre>}
    </div>
  );
};
