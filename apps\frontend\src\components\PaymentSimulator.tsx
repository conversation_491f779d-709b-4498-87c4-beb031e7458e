import React, { useState } from 'react';
import { useAppStore } from '../store/useAppStore';

export const PaymentSimulator: React.FC = () => {
  const [amount, setAmount] = useState(1000);
  const [status, setStatus] = useState<'success' | 'failure'>('success');
  const [protocolCode, setProtocolCode] = useState<string>('');

  const { simulatePayment, isLoading, error, currentTransaction } = useAppStore();

  const handleSimulate = async () => {
    await simulatePayment(amount, status, protocolCode || undefined);
  };

  const protocolOptions = [
    { value: '', label: 'No Protocol' },
    { value: '101.1', label: '101.1 - Authorization' },
    { value: '101.3', label: '101.3 - Capture' },
    { value: '101.5', label: '101.5 - Reversal' },
    { value: '101.8', label: '101.8 - Settlement' },
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">Payment Simulator</h2>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Amount (cents)
          </label>
          <input
            type="number"
            value={amount}
            onChange={e => setAmount(Number(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            min="1"
            step="1"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Status
          </label>
          <select
            value={status}
            onChange={e => setStatus(e.target.value as 'success' | 'failure')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="success">Success</option>
            <option value="failure">Failure</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Protocol Code (Optional)
          </label>
          <select
            value={protocolCode}
            onChange={e => setProtocolCode(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {protocolOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <button
          onClick={handleSimulate}
          disabled={isLoading}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isLoading ? 'Simulating...' : 'Simulate Payment'}
        </button>
      </div>

      {currentTransaction && (
        <div className="mt-6 p-4 bg-gray-50 rounded-md">
          <h3 className="text-lg font-semibold mb-2">Last Transaction</h3>
          <div className="space-y-1 text-sm">
            <p><span className="font-medium">ID:</span> {currentTransaction._id}</p>
            <p><span className="font-medium">Amount:</span> ${(currentTransaction.amount / 100).toFixed(2)}</p>
            <p><span className="font-medium">Status:</span>
              <span className={`ml-1 px-2 py-1 rounded text-xs ${
                currentTransaction.status === 'success'
                  ? 'bg-green-100 text-green-800'
                  : currentTransaction.status === 'failure'
                  ? 'bg-red-100 text-red-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {currentTransaction.status}
              </span>
            </p>
            {currentTransaction.protocolCode && (
              <p><span className="font-medium">Protocol:</span> {currentTransaction.protocolCode}</p>
            )}
            <p><span className="font-medium">Created:</span> {new Date(currentTransaction.createdAt).toLocaleString()}</p>
          </div>
        </div>
      )}
    </div>
  );
};
