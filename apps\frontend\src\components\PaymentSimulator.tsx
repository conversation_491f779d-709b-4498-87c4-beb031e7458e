import React, { useState } from 'react';
import { useAppStore } from '../store/useAppStore';

export const PaymentSimulator: React.FC = () => {
  const [amount, setAmount] = useState(1000);
  const [status, setStatus] = useState<'success' | 'failure'>('success');
  const [protocolCode, setProtocolCode] = useState<string>('');

  const { simulatePayment, isLoading, error, currentTransaction } = useAppStore();

  const handleSimulate = async () => {
    await simulatePayment(amount, status, protocolCode || undefined);
  };

  const protocolOptions = [
    { value: '', label: '🚫 No Protocol' },
    { value: '101.1', label: '🔐 101.1 - Authorization' },
    { value: '101.2', label: '💰 101.2 - Sale' },
    { value: '101.3', label: '❌ 101.3 - Void' },
    { value: '101.4', label: '↩️ 101.4 - Refund' },
    { value: '101.5', label: '🔒 101.5 - Pre-Authorization' },
    { value: '101.6', label: '✅ 101.6 - Completion' },
    { value: '101.7', label: '🔍 101.7 - Inquiry' },
    { value: '101.8', label: '📋 101.8 - Settlement' },
  ];

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
              <span className="text-xl">💳</span>
            </div>
            <div>
              <h2 className="text-2xl font-bold">Payment Simulator</h2>
              <p className="text-blue-100">Process test transactions</p>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="p-8">
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-xl">
              <div className="flex items-center space-x-2">
                <span className="text-xl">⚠️</span>
                <span className="font-medium">{error}</span>
              </div>
            </div>
          )}

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-semibold text-slate-700 mb-2">
                Transaction Amount
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 font-medium">$</span>
                <input
                  type="number"
                  value={amount ? (amount / 100).toFixed(2) : ''}
                  onChange={(e) => setAmount(Math.round(parseFloat(e.target.value || '0') * 100))}
                  className="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg font-medium"
                  placeholder="0.00"
                  min="0.01"
                  step="0.01"
                />
              </div>
              <p className="text-xs text-slate-500 mt-1">Enter amount in dollars</p>
            </div>

            <div>
              <label className="block text-sm font-semibold text-slate-700 mb-2">
                Transaction Status
              </label>
              <select
                value={status}
                onChange={e => setStatus(e.target.value as 'success' | 'failure')}
                className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
              >
                <option value="success">✅ Success</option>
                <option value="failure">❌ Failure</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-semibold text-slate-700 mb-2">
                Protocol Code (Optional)
              </label>
              <select
                value={protocolCode}
                onChange={e => setProtocolCode(e.target.value)}
                className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
              >
                {protocolOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <button
              onClick={handleSimulate}
              disabled={isLoading || !amount}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold text-lg shadow-lg hover:shadow-xl"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <span>🚀</span>
                  <span>Simulate Payment</span>
                </div>
              )}
            </button>
          </div>

          {currentTransaction && (
            <div className="mt-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl">
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-xl">✅</span>
                <h3 className="font-bold text-slate-800">Last Transaction</h3>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-slate-600 font-medium">Transaction ID</p>
                  <p className="text-slate-900 font-mono">#{currentTransaction._id?.slice(-8) || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-slate-600 font-medium">Amount</p>
                  <p className="text-slate-900 font-bold text-lg">${(currentTransaction.amount / 100).toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-slate-600 font-medium">Status</p>
                  <p className={`font-semibold capitalize ${
                    currentTransaction.status === 'success' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {currentTransaction.status === 'success' ? '✅ Success' : '❌ Failed'}
                  </p>
                </div>
                <div>
                  <p className="text-slate-600 font-medium">Protocol</p>
                  <p className="text-slate-900 font-mono">{currentTransaction.protocolCode || 'N/A'}</p>
                </div>
                <div className="col-span-2">
                  <p className="text-slate-600 font-medium">Created</p>
                  <p className="text-slate-900">{new Date(currentTransaction.createdAt).toLocaleString()}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
