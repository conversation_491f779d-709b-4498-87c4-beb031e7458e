import { CustomCardReader, SecureCardData } from './customCardReader';
import { StripeTokenizationService } from './stripeTokenization';

export interface ProductionPaymentResult {
  success: boolean;
  paymentIntent?: any;
  paymentMethod?: any;
  cardData?: SecureCardData;
  receiptData?: ReceiptData;
  error?: string;
  requiresPin?: boolean;
  requiresSignature?: boolean;
}

export interface ReceiptData {
  transactionId: string;
  amount: number;
  currency: string;
  cardBrand: string;
  cardLast4: string;
  cardType: string;
  timestamp: string;
  merchantName: string;
  terminalId: string;
  approvalCode?: string;
  authCode?: string;
  rrn?: string;
  emvAid?: string;
  emvCryptogram?: string;
}

export interface PaymentConfig {
  stripePublishableKey: string;
  merchantName: string;
  merchantAddress: string;
  terminalId: string;
  enableLogging?: boolean;
}

export class ProductionCardReader {
  private cardReader: CustomCardReader;
  private stripeTokenization: StripeTokenizationService;
  private config: PaymentConfig;
  private isProcessing = false;

  constructor(config: PaymentConfig) {
    this.config = config;
    this.cardReader = new CustomCardReader(config.terminalId);
    this.stripeTokenization = new StripeTokenizationService(config.stripePublishableKey);
  }

  /**
   * Check if hardware is available
   */
  isHardwareAvailable(): boolean {
    return this.cardReader.isAvailable();
  }

  /**
   * Get card reader status
   */
  getStatus(): 'idle' | 'reading' | 'processing' | 'error' | 'unavailable' {
    if (this.isProcessing) return 'processing';
    return this.cardReader.getStatus();
  }

  /**
   * Process complete payment transaction
   */
  async processPayment(
    amount: number,
    currency: string = 'usd',
    billingDetails?: any
  ): Promise<ProductionPaymentResult> {
    if (this.isProcessing) {
      return {
        success: false,
        error: 'Payment processing already in progress'
      };
    }

    this.isProcessing = true;

    try {
      this.log('info', 'Starting payment process', { amount, currency });

      // Step 1: Read card
      const cardResult = await this.cardReader.startCardReading();
      
      if (!cardResult.success || !cardResult.cardData) {
        throw new Error(cardResult.error || 'Card reading failed');
      }

      this.log('info', 'Card read successfully', { 
        cardType: cardResult.cardData.cardType,
        cardBrand: cardResult.cardData.cardBrand 
      });

      // Step 2: Validate card data
      const validation = this.stripeTokenization.validateCardData(cardResult.cardData);
      if (!validation.valid) {
        throw new Error(`Card validation failed: ${validation.errors.join(', ')}`);
      }

      // Step 3: Create Stripe payment method from encrypted data
      const tokenResult = await this.stripeTokenization.createPaymentMethodFromEncryptedData(
        cardResult.cardData,
        billingDetails
      );

      if (!tokenResult.success || !tokenResult.paymentMethod) {
        throw new Error(tokenResult.error || 'Failed to create payment method');
      }

      this.log('info', 'Payment method created', { 
        paymentMethodId: tokenResult.paymentMethod.id 
      });

      // Step 4: Process payment through enhanced payment API
      const paymentResult = await this.processPaymentThroughAPI(
        amount,
        currency,
        cardResult.cardData,
        billingDetails
      );

      if (!paymentResult.success) {
        throw new Error(paymentResult.error || 'Payment processing failed');
      }

      this.log('info', 'Payment processed successfully', {
        paymentIntentId: paymentResult.data?.paymentIntentId
      });

      // Step 5: Generate receipt data from API response
      const receiptData: ReceiptData = paymentResult.data?.receiptData || {
        transactionId: paymentResult.data?.paymentIntentId || 'unknown',
        amount,
        currency,
        cardBrand: cardResult.cardData.cardBrand || 'unknown',
        cardLast4: cardResult.cardData.maskedPan?.slice(-4) || '****',
        cardType: cardResult.cardData.cardType || 'unknown',
        timestamp: new Date().toISOString(),
        merchantName: this.config.merchantName,
        terminalId: this.config.terminalId,
        emvAid: cardResult.cardData.emvData?.aid,
        emvCryptogram: cardResult.cardData.emvData?.cryptogram
      };

      return {
        success: true,
        paymentIntent: paymentResult.data,
        paymentMethod: { id: paymentResult.data?.paymentMethodId },
        cardData: cardResult.cardData,
        receiptData,
        requiresPin: cardResult.requiresPin,
        requiresSignature: cardResult.requiresSignature
      };

    } catch (error) {
      this.log('error', 'Payment processing failed', { error });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment processing failed'
      };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process EMV transaction with enhanced security
   */
  async processEMVTransaction(
    amount: number,
    currency: string = 'usd'
  ): Promise<ProductionPaymentResult> {
    try {
      this.log('info', 'Starting EMV transaction', { amount, currency });

      // Read card with EMV processing
      const cardResult = await this.cardReader.startCardReading();
      
      if (!cardResult.success || !cardResult.cardData) {
        throw new Error(cardResult.error || 'EMV card reading failed');
      }

      // Validate EMV data
      if (!cardResult.cardData.emvData?.cryptogram || !cardResult.cardData.emvData?.aid) {
        throw new Error('Invalid EMV data - cryptogram and AID required');
      }

      // Process EMV transaction with Stripe
      const emvResult = await this.stripeTokenization.processEMVTransaction(
        cardResult.cardData,
        amount,
        currency
      );

      if (!emvResult.success) {
        throw new Error(emvResult.error || 'EMV transaction failed');
      }

      this.log('info', 'EMV transaction completed', { 
        paymentIntentId: emvResult.paymentIntent.id 
      });

      // Generate receipt data
      const receiptData: ReceiptData = {
        transactionId: emvResult.paymentIntent.id,
        amount,
        currency,
        cardBrand: cardResult.cardData.cardBrand || 'unknown',
        cardLast4: cardResult.cardData.maskedPan?.slice(-4) || '****',
        cardType: 'chip', // EMV is always chip
        timestamp: new Date().toISOString(),
        merchantName: this.config.merchantName,
        terminalId: this.config.terminalId,
        emvAid: cardResult.cardData.emvData.aid,
        emvCryptogram: cardResult.cardData.emvData.cryptogram
      };

      return {
        success: true,
        paymentIntent: emvResult.paymentIntent,
        paymentMethod: emvResult.paymentMethod,
        cardData: cardResult.cardData,
        receiptData,
        requiresPin: cardResult.requiresPin,
        requiresSignature: cardResult.requiresSignature
      };

    } catch (error) {
      this.log('error', 'EMV transaction failed', { error });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'EMV transaction failed'
      };
    }
  }

  /**
   * Cancel current transaction
   */
  cancelTransaction(): void {
    this.cardReader.stopCardReading();
    this.isProcessing = false;
    this.log('info', 'Transaction cancelled');
  }

  /**
   * Get terminal information
   */
  getTerminalInfo(): any {
    const terminalInfo = this.cardReader.getTerminalInfo();
    return {
      ...terminalInfo,
      stripeReady: this.stripeTokenization.isReady(),
      supportedCardBrands: this.stripeTokenization.getSupportedCardBrands(),
      pciCompliant: true,
      emvCertified: true
    };
  }

  /**
   * Test card reader functionality
   */
  async testCardReader(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      const status = this.getStatus();
      const terminalInfo = this.getTerminalInfo();
      
      if (status === 'unavailable') {
        return {
          success: false,
          message: 'Card reader hardware not available'
        };
      }

      return {
        success: true,
        message: 'Card reader is ready',
        details: {
          status,
          terminalInfo,
          stripeReady: this.stripeTokenization.isReady()
        }
      };

    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Card reader test failed'
      };
    }
  }

  /**
   * Process manual card entry (fallback)
   */
  async processManualPayment(
    cardNumber: string,
    expiryMonth: number,
    expiryYear: number,
    cvc: string,
    cardholderName: string,
    amount: number,
    currency: string = 'usd',
    billingDetails?: any
  ): Promise<ProductionPaymentResult> {
    try {
      this.log('info', 'Processing manual payment', { amount, currency });

      // Create payment method from manual entry
      const tokenResult = await this.stripeTokenization.createPaymentMethodFromManualEntry(
        cardNumber,
        expiryMonth,
        expiryYear,
        cvc,
        cardholderName,
        billingDetails
      );

      if (!tokenResult.success || !tokenResult.paymentMethod) {
        throw new Error(tokenResult.error || 'Failed to create payment method');
      }

      // Create payment intent
      const paymentIntent = await this.stripeTokenization.createPaymentIntent(
        amount,
        currency,
        tokenResult.paymentMethod.id,
        {
          terminal_id: this.config.terminalId,
          card_type: 'manual',
          merchant_name: this.config.merchantName,
          source: 'manual_entry'
        }
      );

      if (!paymentIntent.success) {
        throw new Error('Payment intent creation failed');
      }

      // Generate receipt data
      const receiptData: ReceiptData = {
        transactionId: paymentIntent.data.id,
        amount,
        currency,
        cardBrand: tokenResult.paymentMethod.card?.brand || 'unknown',
        cardLast4: tokenResult.paymentMethod.card?.last4 || '****',
        cardType: 'manual',
        timestamp: new Date().toISOString(),
        merchantName: this.config.merchantName,
        terminalId: this.config.terminalId
      };

      this.log('info', 'Manual payment processed successfully', { 
        paymentIntentId: paymentIntent.data.id 
      });

      return {
        success: true,
        paymentIntent: paymentIntent.data,
        paymentMethod: tokenResult.paymentMethod,
        receiptData
      };

    } catch (error) {
      this.log('error', 'Manual payment failed', { error });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Manual payment failed'
      };
    }
  }

  /**
   * Process payment through enhanced payment API
   */
  private async processPaymentThroughAPI(
    amount: number,
    currency: string,
    cardData: SecureCardData,
    billingDetails?: any
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const response = await fetch('/api/v1/payments/terminal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          currency,
          terminalId: this.config.terminalId,
          merchantName: this.config.merchantName,
          description: `PAX Terminal Payment - ${this.config.terminalId}`,
          cardData: {
            encryptedPan: cardData.encryptedPan,
            maskedPan: cardData.maskedPan,
            expiryDate: cardData.expiryDate,
            cardholderName: cardData.cardholderName,
            cardBrand: cardData.cardBrand,
            cardType: cardData.cardType,
            emvData: cardData.emvData,
            ksn: cardData.ksn
          },
          metadata: {
            billing_details: JSON.stringify(billingDetails || {}),
            sequence_number: cardData.sequenceNumber || '',
            timestamp: cardData.timestamp
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;

    } catch (error) {
      console.error('Payment API call failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment API call failed'
      };
    }
  }

  /**
   * Log system events
   */
  private log(level: 'info' | 'warn' | 'error' | 'debug', message: string, details?: any): void {
    if (this.config.enableLogging) {
      // Send to system logging service
      fetch('/api/v1/logs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          level,
          category: 'payment',
          message: `[ProductionCardReader] ${message}`,
          details: {
            terminalId: this.config.terminalId,
            ...details
          }
        })
      }).catch(console.error);
    }

    // Also log to console
    console[level](`[ProductionCardReader] ${message}`, details);
  }
}
