import { useState, useEffect, useRef } from 'react';
import { terminalHardware, CardData, TerminalInfo } from '../services/terminalHardware';
import { apiService } from '../services/api';
import { protocolHandler, type Protocol, type PaymentMethod, type ProtocolTransaction } from '../services/protocolHandler';
import { paxHardware, type PAXTerminalInfo } from '../services/paxHardware';

type TransactionState = 
  | 'idle'
  | 'amount-entry'
  | 'waiting-card'
  | 'card-reading'
  | 'authorizing'
  | 'approved'
  | 'declined'
  | 'printing'
  | 'complete'
  | 'error';

interface RealTimeTransaction {
  id: string;
  amount: number;
  status: TransactionState;
  paymentIntentId?: string;
  cardData?: CardData;
  authCode?: string;
  receiptData?: string;
  errorMessage?: string;
  timestamp: Date;
  protocol?: Protocol;
  paymentMethod?: PaymentMethod;
  protocolTransaction?: ProtocolTransaction;
  isOffline?: boolean;
}

export function PAXTerminalPOS() {
  const [transaction, setTransaction] = useState<RealTimeTransaction>({
    id: '',
    amount: 0,
    status: 'idle',
    timestamp: new Date()
  });

  const [amountInput, setAmountInput] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [terminalInfo, setTerminalInfo] = useState<TerminalInfo | null>(null);
  const [paxTerminalInfo, setPaxTerminalInfo] = useState<PAXTerminalInfo | null>(null);
  const [wasmLoaded, setWasmLoaded] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [manualCardData, setManualCardData] = useState({
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    cardholderName: ''
  });

  const intervalRef = useRef<NodeJS.Timeout>();

  // Load terminal hardware
  useEffect(() => {
    const loadTerminal = async () => {
      try {
        // Initialize PAX hardware
        const paxConnected = await paxHardware.initialize();
        const paxInfo = paxHardware.getTerminalInfo();
        setPaxTerminalInfo(paxInfo);

        // Get terminal info
        const info = await terminalHardware.getTerminalInfo();
        setTerminalInfo(info);

        // Check if hardware is available
        const isHardwareAvailable = terminalHardware.isHardwareAvailable() || paxConnected;
        setWasmLoaded(isHardwareAvailable);

        if (paxConnected) {
          console.log('PAX A920 Pro hardware connected');
        } else if (isHardwareAvailable) {
          console.log('PAX Terminal hardware available');
        } else {
          console.log('Using simulation mode');
          setShowManualEntry(true);
        }

        // Monitor connectivity
        const checkConnectivity = async () => {
          const connectivity = await paxHardware.checkConnectivity();
          setIsOnline(connectivity.online);
        };

        checkConnectivity();
        const connectivityInterval = setInterval(checkConnectivity, 30000);

        return () => clearInterval(connectivityInterval);
      } catch (error) {
        console.error('Failed to initialize terminal:', error);

        // Create fallback terminal info based on your actual PAX A920 Pro
        setTerminalInfo({
          serialNumber: '1853944350',
          model: 'PAX A920PC9',
          firmwareVersion: 'PayDroid_10.0.0_Sagittarius_V11-1.29_20240913',
          batteryLevel: 76,
          isHardwareAvailable: false,
          capabilities: ['nfc', 'emv', 'magnetic_stripe', 'contactless', 'wifi', 'bluetooth']
        });
        
        setShowManualEntry(true);
        setWasmLoaded(false);
      }
    };

    loadTerminal();
  }, []);

  // Countdown timer for card reading
  useEffect(() => {
    if (transaction.status === 'waiting-card') {
      setCountdown(30);
      intervalRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            setTransaction(prev => ({
              ...prev,
              status: 'error',
              errorMessage: 'Card reading timeout - please try again'
            }));
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [transaction.status]);

  const isTerminalAvailable = () => {
    return wasmLoaded && terminalHardware.isHardwareAvailable();
  };

  const startCardReading = async () => {
    try {
      await terminalHardware.startCardReading(
        (cardData: CardData) => {
          // Success callback
          terminalHardware.beep(200);
          setTransaction(prev => ({
            ...prev,
            status: 'card-reading',
            cardData
          }));

          setTimeout(() => {
            processCardData(cardData);
          }, 2000);
        },
        (error: string) => {
          // Error callback
          terminalHardware.beep(500);
          setTransaction(prev => ({
            ...prev,
            status: 'error',
            errorMessage: `Card reading failed: ${error}`
          }));
        }
      );
    } catch (error) {
      console.warn('Card reading failed, showing manual entry');
      setShowManualEntry(true);
    }
  };

  const processCardData = async (cardData: CardData) => {
    setTransaction(prev => ({ ...prev, status: 'authorizing' }));

    try {
      // Determine payment method from card data
      const paymentMethod: PaymentMethod = cardData.cardType === 'tap' ? 'contactless_chip' :
                                          cardData.cardType === 'chip' ? 'chip_and_pin' :
                                          cardData.cardType === 'swipe' ? 'magnetic_stripe' : 'chip_and_pin';

      // Select appropriate protocol
      const protocol = protocolHandler.selectProtocol(paymentMethod, transaction.amount, isOnline);

      // Validate transaction against protocol
      const validation = protocolHandler.validateTransaction(protocol, paymentMethod, transaction.amount);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Process with protocol handler
      const protocolTransaction = await protocolHandler.processTransaction(
        protocol,
        transaction.amount,
        paymentMethod,
        cardData
      );

      // Update transaction with protocol info
      setTransaction(prev => ({
        ...prev,
        protocol,
        paymentMethod,
        protocolTransaction,
        isOffline: !isOnline || protocolTransaction.isOffline
      }));

      // Process payment with Stripe (if online)
      if (isOnline && !protocolTransaction.isOffline) {
        const paymentResult = await apiService.processPayment({
          amount: transaction.amount,
          currency: 'usd',
          payment_method_types: ['card'],
          confirm: true,
          description: `PAX Terminal Payment - Protocol ${protocol} - ${cardData.cardType}`,
          customer_email: '<EMAIL>',
          metadata: {
            protocol: protocol,
            paymentMethod: paymentMethod,
            terminalId: paxTerminalInfo?.serialNumber || '1853944350',
            transactionId: protocolTransaction.id
          }
        });

        if (paymentResult.success && paymentResult.data?.status === 'succeeded') {
          setTransaction(prev => ({
            ...prev,
            status: 'approved',
            paymentIntentId: paymentResult.data.id,
            authCode: protocolTransaction.authCode
          }));

          setTimeout(() => {
            generateAndPrintReceipt();
          }, 2000);
        } else {
          setTransaction(prev => ({
            ...prev,
            status: 'declined',
            errorMessage: paymentResult.error || 'Payment was declined'
          }));
        }
      } else {
        // Offline transaction - approve locally
        setTransaction(prev => ({
          ...prev,
          status: 'approved',
          authCode: protocolTransaction.authCode
        }));

        setTimeout(() => {
          generateAndPrintReceipt();
        }, 2000);
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      setTransaction(prev => ({
        ...prev,
        status: 'error',
        errorMessage: error instanceof Error ? error.message : 'Payment processing failed - please try again'
      }));
    }
  };

  const processManualCard = async () => {
    if (!manualCardData.cardNumber || !manualCardData.expiryMonth || !manualCardData.expiryYear || !manualCardData.cvv) {
      alert('Please fill in all card details');
      return;
    }

    const cardData: CardData = {
      pan: manualCardData.cardNumber,
      expiryDate: `${manualCardData.expiryYear}${manualCardData.expiryMonth}`,
      cardholderName: manualCardData.cardholderName,
      cardType: 'manual'
    };

    setShowManualEntry(false);
    setTransaction(prev => ({ ...prev, cardData }));
    await processCardData(cardData);
  };

  const startNewTransaction = () => {
    const newId = `TXN_${Date.now()}`;
    setTransaction({
      id: newId,
      amount: 0,
      status: 'amount-entry',
      timestamp: new Date()
    });
    setAmountInput('');
    setShowManualEntry(false);
    setManualCardData({
      cardNumber: '',
      expiryMonth: '',
      expiryYear: '',
      cvv: '',
      cardholderName: ''
    });
  };

  const processPayment = () => {
    if (!amountInput || parseFloat(amountInput) <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    const amount = Math.round(parseFloat(amountInput) * 100);
    setTransaction(prev => ({
      ...prev,
      amount,
      status: 'waiting-card'
    }));

    // Start card reading
    startCardReading();
  };

  const generateAndPrintReceipt = async () => {
    setTransaction(prev => ({ ...prev, status: 'printing' }));

    try {
      let receiptText: string;

      // Use protocol handler to generate receipt if protocol transaction exists
      if (transaction.protocolTransaction) {
        receiptText = protocolHandler.generateProtocolReceipt(transaction.protocolTransaction);
      } else {
        // Fallback to API receipt generation
        const receiptData = await apiService.generateReceipt(transaction.id);

        if (receiptData.success && receiptData.data) {
          receiptText = receiptData.data.customerReceipt;
        } else {
          receiptText = generateSimpleReceipt();
        }
      }

      setTransaction(prev => ({
        ...prev,
        receiptData: receiptText,
        status: 'complete'
      }));

      // Print using PAX hardware first, fallback to terminal hardware
      try {
        const printed = await paxHardware.printReceipt(receiptText);
        if (!printed) {
          await terminalHardware.printReceipt(receiptText);
        }
      } catch (printError) {
        console.warn('PAX printing failed, using fallback:', printError);
        await terminalHardware.printReceipt(receiptText);
      }
    } catch (error) {
      console.error('Receipt generation failed:', error);

      // Generate a simple receipt as fallback
      const simpleReceipt = generateSimpleReceipt();
      setTransaction(prev => ({
        ...prev,
        receiptData: simpleReceipt,
        status: 'complete'
      }));

      try {
        await paxHardware.printReceipt(simpleReceipt);
      } catch {
        await terminalHardware.printReceipt(simpleReceipt);
      }
    }
  };



  const generateSimpleReceipt = () => {
    const now = new Date();
    const terminalSerial = paxTerminalInfo?.serialNumber || terminalInfo?.serialNumber || '1853944350';

    return `
================================
       PAX A920 Pro Terminal
================================

Date: ${now.toLocaleDateString()}
Time: ${now.toLocaleTimeString()}
Terminal SN: ${terminalSerial}
Model: ${paxTerminalInfo?.model || 'PAX A920PC9'}

--------------------------------
TRANSACTION DETAILS
--------------------------------

Amount: $${(transaction.amount / 100).toFixed(2)}
Card: ****${transaction.cardData?.pan?.slice(-4) || '****'}
Auth Code: ${transaction.authCode || 'N/A'}
Transaction ID: ${transaction.id}

${transaction.protocol ? `
--------------------------------
PROTOCOL INFORMATION
--------------------------------

Protocol: ${transaction.protocol}
Payment Method: ${transaction.paymentMethod?.toUpperCase() || 'CARD'}
Status: ${transaction.isOffline ? 'OFFLINE APPROVED' : 'APPROVED'}
${transaction.isOffline ? 'Will be uploaded when online' : 'Real-time authorization'}
` : `
Status: APPROVED
Payment Method: ${transaction.cardData?.cardType?.toUpperCase() || 'CARD'}
`}

--------------------------------
Thank you for your business!
--------------------------------

${transaction.isOffline ? 'Offline' : 'Customer'} Copy
`;
  };

  const getStatusDisplay = () => {
    switch (transaction.status) {
      case 'idle':
        return { text: 'Ready', icon: '💳', color: 'bg-blue-500' };
      case 'amount-entry':
        return { text: 'Enter Amount', icon: '💰', color: 'bg-yellow-500' };
      case 'waiting-card':
        return { text: 'Insert or Tap Card', icon: '📱', color: 'bg-orange-500' };
      case 'card-reading':
        return { text: 'Reading Card...', icon: '📖', color: 'bg-blue-600' };
      case 'authorizing':
        return { text: 'Authorizing...', icon: '⚡', color: 'bg-purple-600' };
      case 'approved':
        return { text: 'Approved!', icon: '✅', color: 'bg-green-500' };
      case 'declined':
        return { text: 'Declined', icon: '❌', color: 'bg-red-500' };
      case 'printing':
        return { text: 'Printing...', icon: '🖨️', color: 'bg-indigo-500' };
      case 'complete':
        return { text: 'Complete', icon: '🎉', color: 'bg-green-600' };
      case 'error':
        return { text: 'Error', icon: '⚠️', color: 'bg-red-600' };
      default:
        return { text: 'Ready', icon: '💳', color: 'bg-gray-500' };
    }
  };

  const status = getStatusDisplay();

  return (
    <div className="w-full max-w-md mx-auto h-full">
      <div className="bg-white rounded-xl sm:rounded-2xl shadow-2xl border border-slate-200 overflow-hidden h-full flex flex-col">
        {/* Status Header */}
        <div className={`${status.color} p-4 sm:p-6 text-white text-center flex-shrink-0`}>
          <div className="text-3xl sm:text-4xl lg:text-6xl mb-2 sm:mb-3">{status.icon}</div>
          <h1 className="text-lg sm:text-xl lg:text-3xl font-bold mb-1 sm:mb-2">{status.text}</h1>
          {transaction.amount > 0 && (
            <div className="text-lg sm:text-xl lg:text-2xl font-mono">
              ${(transaction.amount / 100).toFixed(2)}
            </div>
          )}
          {countdown > 0 && (
            <div className="text-sm sm:text-lg mt-1 sm:mt-2 opacity-90">
              {countdown}s remaining
            </div>
          )}
        </div>

        {/* Terminal Info */}
        {(terminalInfo || paxTerminalInfo) && (
          <div className="bg-slate-50 px-4 py-2 text-center border-b border-slate-200">
            <div className="text-xs text-slate-600 space-y-1">
              <div>
                {paxTerminalInfo?.model || terminalInfo?.model} • SN: {paxTerminalInfo?.serialNumber || terminalInfo?.serialNumber}
              </div>
              <div className="flex justify-center items-center space-x-3">
                <span className={`inline-flex items-center space-x-1 ${isOnline ? 'text-green-600' : 'text-red-600'}`}>
                  <span className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`}></span>
                  <span>{isOnline ? 'Online' : 'Offline'}</span>
                </span>
                {paxTerminalInfo && (
                  <span className="text-blue-600">
                    🔋 {paxTerminalInfo.batteryLevel}%
                  </span>
                )}
                <span className={terminalInfo?.isHardwareAvailable ? 'text-green-600' : 'text-orange-600'}>
                  {terminalInfo?.isHardwareAvailable ? '🟢 Hardware Ready' : '🟡 Simulation Mode'}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Content Area */}
        <div className="flex-1 p-3 sm:p-4 lg:p-6 overflow-auto">
          {/* Amount Entry */}
          {transaction.status === 'amount-entry' && (
            <div className="space-y-4 sm:space-y-6">
              <div>
                <label className="block text-base sm:text-lg font-semibold text-slate-700 mb-3 sm:mb-4 text-center">
                  Transaction Amount
                </label>
                <div className="relative">
                  <span className="absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-slate-500 text-xl sm:text-2xl">$</span>
                  <input
                    type="number"
                    value={amountInput}
                    onChange={(e) => setAmountInput(e.target.value)}
                    className="w-full pl-10 sm:pl-12 pr-4 py-3 sm:py-4 border-2 border-slate-300 rounded-xl sm:rounded-2xl focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-transparent text-xl sm:text-2xl lg:text-3xl font-bold text-center"
                    placeholder="0.00"
                    min="0.01"
                    step="0.01"
                    autoFocus
                  />
                </div>
              </div>
              
              <button
                onClick={processPayment}
                disabled={!amountInput || parseFloat(amountInput) <= 0}
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 sm:py-4 px-6 rounded-xl sm:rounded-2xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-bold text-base sm:text-lg shadow-lg active:scale-95"
              >
                💳 Process Payment
              </button>
            </div>
          )}

          {/* Waiting for Card */}
          {transaction.status === 'waiting-card' && (
            <div className="text-center space-y-4 sm:space-y-6">
              <div className="animate-pulse">
                <div className="text-4xl sm:text-6xl mb-3 sm:mb-4">💳</div>
                <p className="text-base sm:text-lg text-slate-600">
                  {isTerminalAvailable() 
                    ? 'Insert, tap, or swipe your card'
                    : 'Card reader not available'
                  }
                </p>
              </div>
              
              {!isTerminalAvailable() && (
                <button
                  onClick={() => setShowManualEntry(true)}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-xl hover:bg-blue-700 transition-colors font-semibold active:scale-95"
                >
                  Enter Card Details Manually
                </button>
              )}
            </div>
          )}

          {/* Manual Card Entry */}
          {showManualEntry && (
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-base sm:text-lg font-bold text-center text-slate-800">Enter Card Details</h3>
              
              <input
                type="text"
                placeholder="Card Number"
                value={manualCardData.cardNumber}
                onChange={(e) => setManualCardData(prev => ({ ...prev, cardNumber: e.target.value }))}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                maxLength={19}
              />
              
              <div className="grid grid-cols-2 gap-2 sm:gap-3">
                <select
                  value={manualCardData.expiryMonth}
                  onChange={(e) => setManualCardData(prev => ({ ...prev, expiryMonth: e.target.value }))}
                  className="px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                >
                  <option value="">Month</option>
                  {Array.from({ length: 12 }, (_, i) => (
                    <option key={i + 1} value={String(i + 1).padStart(2, '0')}>
                      {String(i + 1).padStart(2, '0')}
                    </option>
                  ))}
                </select>
                
                <select
                  value={manualCardData.expiryYear}
                  onChange={(e) => setManualCardData(prev => ({ ...prev, expiryYear: e.target.value }))}
                  className="px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                >
                  <option value="">Year</option>
                  {Array.from({ length: 10 }, (_, i) => (
                    <option key={i} value={String(new Date().getFullYear() + i).slice(-2)}>
                      {new Date().getFullYear() + i}
                    </option>
                  ))}
                </select>
              </div>
              
              <input
                type="text"
                placeholder="CVV"
                value={manualCardData.cvv}
                onChange={(e) => setManualCardData(prev => ({ ...prev, cvv: e.target.value }))}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                maxLength={4}
              />
              
              <input
                type="text"
                placeholder="Cardholder Name (Optional)"
                value={manualCardData.cardholderName}
                onChange={(e) => setManualCardData(prev => ({ ...prev, cardholderName: e.target.value }))}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
              />
              
              <div className="grid grid-cols-2 gap-2 sm:gap-3">
                <button
                  onClick={() => setShowManualEntry(false)}
                  className="bg-gray-600 text-white py-2 sm:py-3 px-4 rounded-lg sm:rounded-xl hover:bg-gray-700 transition-colors font-semibold text-sm sm:text-base active:scale-95"
                >
                  Cancel
                </button>
                <button
                  onClick={processManualCard}
                  className="bg-green-600 text-white py-2 sm:py-3 px-4 rounded-lg sm:rounded-xl hover:bg-green-700 transition-colors font-semibold text-sm sm:text-base active:scale-95"
                >
                  Process
                </button>
              </div>
            </div>
          )}

          {/* Processing States */}
          {['card-reading', 'authorizing'].includes(transaction.status) && (
            <div className="text-center space-y-4 sm:space-y-6">
              <div className="animate-spin rounded-full h-12 w-12 sm:h-16 sm:w-16 border-b-4 border-blue-600 mx-auto"></div>
              <div className="text-base sm:text-lg text-slate-600">
                {transaction.status === 'card-reading' && 'Do not remove your card...'}
                {transaction.status === 'authorizing' && 'Contacting your bank...'}
              </div>
            </div>
          )}

          {/* Success State */}
          {transaction.status === 'approved' && (
            <div className="text-center space-y-3 sm:space-y-4">
              <div className="text-4xl sm:text-6xl">✅</div>
              <div className="text-xl sm:text-2xl font-bold text-green-600">
                {transaction.isOffline ? 'Offline Payment Approved!' : 'Payment Approved!'}
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg sm:rounded-xl p-3 sm:p-4">
                <div className="grid grid-cols-2 gap-2 sm:gap-3 text-xs sm:text-sm">
                  <div>
                    <span className="font-medium text-slate-600">Amount:</span>
                    <div className="text-base sm:text-lg font-bold">${(transaction.amount / 100).toFixed(2)}</div>
                  </div>
                  <div>
                    <span className="font-medium text-slate-600">Card:</span>
                    <div className="font-mono">****{transaction.cardData?.pan?.slice(-4) || '****'}</div>
                  </div>
                  {transaction.protocol && (
                    <>
                      <div>
                        <span className="font-medium text-slate-600">Protocol:</span>
                        <div className="font-bold text-blue-600">{transaction.protocol}</div>
                      </div>
                      <div>
                        <span className="font-medium text-slate-600">Method:</span>
                        <div className="text-sm">{transaction.paymentMethod?.replace('_', ' ').toUpperCase()}</div>
                      </div>
                    </>
                  )}
                  <div className="col-span-2">
                    <span className="font-medium text-slate-600">Auth Code:</span>
                    <div className="font-mono text-base sm:text-lg">{transaction.authCode}</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Error States */}
          {['declined', 'error'].includes(transaction.status) && (
            <div className="text-center space-y-4 sm:space-y-6">
              <div className="text-4xl sm:text-6xl">❌</div>
              <div className="text-lg sm:text-xl font-bold text-red-600">
                {transaction.status === 'declined' ? 'Payment Declined' : 'Transaction Error'}
              </div>
              {transaction.errorMessage && (
                <div className="bg-red-50 border border-red-200 rounded-lg sm:rounded-xl p-3 sm:p-4 text-red-700 text-xs sm:text-sm">
                  {transaction.errorMessage}
                </div>
              )}
              <div className="grid grid-cols-2 gap-2 sm:gap-3">
                <button
                  onClick={() => setTransaction(prev => ({ ...prev, status: 'amount-entry' }))}
                  className="bg-blue-600 text-white py-2 sm:py-3 px-4 rounded-lg sm:rounded-xl hover:bg-blue-700 transition-colors font-semibold text-sm sm:text-base active:scale-95"
                >
                  🔄 Retry
                </button>
                <button
                  onClick={startNewTransaction}
                  className="bg-gray-600 text-white py-2 sm:py-3 px-4 rounded-lg sm:rounded-xl hover:bg-gray-700 transition-colors font-semibold text-sm sm:text-base active:scale-95"
                >
                  ❌ Cancel
                </button>
              </div>
            </div>
          )}

          {/* Complete State */}
          {transaction.status === 'complete' && (
            <div className="text-center space-y-4 sm:space-y-6">
              <div className="text-4xl sm:text-6xl">🎉</div>
              <div className="text-lg sm:text-xl font-bold text-green-600">Transaction Complete!</div>
              <div className="text-sm text-slate-600">Receipt has been printed</div>
              
              <button
                onClick={startNewTransaction}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 sm:py-4 px-6 rounded-xl sm:rounded-2xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-500 transition-all duration-200 font-bold text-base sm:text-lg shadow-lg active:scale-95"
              >
                🆕 New Transaction
              </button>
            </div>
          )}

          {/* Idle State */}
          {transaction.status === 'idle' && (
            <div className="text-center">
              <button
                onClick={startNewTransaction}
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 sm:py-6 px-6 sm:px-8 rounded-xl sm:rounded-2xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-500 transition-all duration-200 font-bold text-lg sm:text-xl shadow-lg active:scale-95"
              >
                🚀 Start Transaction
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
