import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { apiService, Transaction } from '../services/api';

export interface AppState {
  // UI State
  isLoading: boolean;
  error: string | null;
  
  // Transaction State
  transactions: Transaction[];
  currentTransaction: Transaction | null;
  
  // Payment State
  connectionToken: string | null;
  paymentIntent: any | null;
  
  // Protocol State
  protocolLogs: Array<{
    id: string;
    timestamp: string;
    protocolCode: string;
    message: string;
    status: 'success' | 'error' | 'pending';
  }>;
}

export interface AppActions {
  // UI Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Transaction Actions
  setTransactions: (transactions: Transaction[]) => void;
  setCurrentTransaction: (transaction: Transaction | null) => void;
  addTransaction: (transaction: Transaction) => void;
  updateTransaction: (id: string, updates: Partial<Transaction>) => void;
  
  // Payment Actions
  setConnectionToken: (token: string | null) => void;
  setPaymentIntent: (intent: any | null) => void;
  
  // Protocol Actions
  addProtocolLog: (log: Omit<AppState['protocolLogs'][0], 'id' | 'timestamp'>) => void;
  clearProtocolLogs: () => void;
  
  // API Actions
  fetchTransactions: () => Promise<void>;
  simulatePayment: (amount: number, status: 'success' | 'failure', protocolCode?: string) => Promise<Transaction | null>;
  triggerProtocol: (protocolCode: string, transactionId: string, amount: number) => Promise<void>;
  getConnectionToken: () => Promise<string | null>;
  createPaymentIntent: (amount: number, currency: string) => Promise<any>;
}

export type AppStore = AppState & AppActions;

const initialState: AppState = {
  isLoading: false,
  error: null,
  transactions: [],
  currentTransaction: null,
  connectionToken: null,
  paymentIntent: null,
  protocolLogs: [],
};

export const useAppStore = create<AppStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // UI Actions
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),
      
      // Transaction Actions
      setTransactions: (transactions) => set({ transactions }),
      setCurrentTransaction: (transaction) => set({ currentTransaction: transaction }),
      addTransaction: (transaction) => set((state) => ({ 
        transactions: [transaction, ...state.transactions] 
      })),
      updateTransaction: (id, updates) => set((state) => ({
        transactions: state.transactions.map(t => 
          t._id === id ? { ...t, ...updates } : t
        ),
        currentTransaction: state.currentTransaction?._id === id 
          ? { ...state.currentTransaction, ...updates } 
          : state.currentTransaction
      })),
      
      // Payment Actions
      setConnectionToken: (token) => set({ connectionToken: token }),
      setPaymentIntent: (intent) => set({ paymentIntent: intent }),
      
      // Protocol Actions
      addProtocolLog: (log) => set((state) => ({
        protocolLogs: [{
          ...log,
          id: Date.now().toString(),
          timestamp: new Date().toISOString(),
        }, ...state.protocolLogs]
      })),
      clearProtocolLogs: () => set({ protocolLogs: [] }),
      
      // API Actions
      fetchTransactions: async () => {
        const { setLoading, setError, setTransactions } = get();
        
        try {
          setLoading(true);
          setError(null);
          
          const response = await apiService.getTransactions(50, 0);
          
          if (response.success && response.data) {
            setTransactions(response.data.transactions);
          } else {
            setError(response.error || 'Failed to fetch transactions');
          }
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Failed to fetch transactions');
        } finally {
          setLoading(false);
        }
      },
      
      simulatePayment: async (amount, status, protocolCode) => {
        const { setLoading, setError, addTransaction, addProtocolLog } = get();
        
        try {
          setLoading(true);
          setError(null);
          
          const response = await apiService.simulatePayment({
            amount,
            status,
            protocolCode: protocolCode as any,
          });
          
          if (response.success && response.data) {
            const transaction = response.data.transaction;
            addTransaction(transaction);
            
            if (protocolCode) {
              addProtocolLog({
                protocolCode,
                message: `Payment ${status} simulation with protocol ${protocolCode}`,
                status: status === 'success' ? 'success' : 'error',
              });
            }
            
            return transaction;
          } else {
            setError(response.error || 'Failed to simulate payment');
            return null;
          }
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Failed to simulate payment');
          return null;
        } finally {
          setLoading(false);
        }
      },
      
      triggerProtocol: async (protocolCode, transactionId, amount) => {
        const { setLoading, setError, addProtocolLog } = get();
        
        try {
          setLoading(true);
          setError(null);
          
          addProtocolLog({
            protocolCode,
            message: `Triggering protocol ${protocolCode} for transaction ${transactionId}`,
            status: 'pending',
          });
          
          const response = await apiService.triggerProtocol({
            protocolEventCode: protocolCode as any,
            transactionId,
            amount,
          });
          
          if (response.success) {
            addProtocolLog({
              protocolCode,
              message: `Protocol ${protocolCode} completed successfully`,
              status: 'success',
            });
          } else {
            addProtocolLog({
              protocolCode,
              message: `Protocol ${protocolCode} failed: ${response.error}`,
              status: 'error',
            });
            setError(response.error || 'Failed to trigger protocol');
          }
        } catch (error) {
          addProtocolLog({
            protocolCode,
            message: `Protocol ${protocolCode} failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            status: 'error',
          });
          setError(error instanceof Error ? error.message : 'Failed to trigger protocol');
        } finally {
          setLoading(false);
        }
      },
      
      getConnectionToken: async () => {
        const { setLoading, setError, setConnectionToken } = get();
        
        try {
          setLoading(true);
          setError(null);
          
          const response = await apiService.getConnectionToken();
          
          if (response.success && response.data) {
            const token = response.data.secret;
            setConnectionToken(token);
            return token;
          } else {
            setError(response.error || 'Failed to get connection token');
            return null;
          }
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Failed to get connection token');
          return null;
        } finally {
          setLoading(false);
        }
      },
      
      createPaymentIntent: async (amount, currency) => {
        const { setLoading, setError, setPaymentIntent } = get();
        
        try {
          setLoading(true);
          setError(null);
          
          const response = await apiService.createPaymentIntent({
            amount,
            currency,
          });
          
          if (response.success && response.data) {
            setPaymentIntent(response.data);
            return response.data;
          } else {
            setError(response.error || 'Failed to create payment intent');
            return null;
          }
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Failed to create payment intent');
          return null;
        } finally {
          setLoading(false);
        }
      },
    }),
    {
      name: 'pax-pos-store',
    }
  )
);
