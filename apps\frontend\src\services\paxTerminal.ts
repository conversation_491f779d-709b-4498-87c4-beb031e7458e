// PAX A920 Pro Terminal Hardware Integration Service

export interface CardData {
  pan?: string;
  expiryDate?: string;
  cardholderName?: string;
  track1?: string;
  track2?: string;
  emvData?: string;
  cardType?: 'chip' | 'swipe' | 'tap';
  serviceCode?: string;
  discretionaryData?: string;
}

export interface PrinterOptions {
  copies?: number;
  fontSize?: 'small' | 'medium' | 'large';
  alignment?: 'left' | 'center' | 'right';
  bold?: boolean;
}

// PAX Terminal Native Bridge Interface
declare global {
  interface Window {
    PAXTerminal?: {
      cardReader: {
        startReading: (callback: (data: CardData) => void, errorCallback?: (error: string) => void) => void;
        stopReading: () => void;
        isAvailable: () => boolean;
        getReaderStatus: () => 'idle' | 'reading' | 'error';
      };
      printer: {
        print: (text: string, options?: PrinterOptions) => Promise<boolean>;
        printReceipt: (receiptData: string) => Promise<boolean>;
        isAvailable: () => boolean;
        getPrinterStatus: () => 'ready' | 'busy' | 'error' | 'paper_low' | 'paper_out';
      };
      display: {
        showMessage: (message: string, timeout?: number) => void;
        clearMessage: () => void;
        setBrightness: (level: number) => void;
      };
      system: {
        getTerminalInfo: () => {
          serialNumber: string;
          model: string;
          firmwareVersion: string;
          batteryLevel: number;
        };
        beep: (duration?: number) => void;
        vibrate: (duration?: number) => void;
      };
    };
    // Android WebView interface for PAX terminal
    Android?: {
      readCard: (callback: string) => void;
      printText: (text: string) => boolean;
      getTerminalInfo: () => string;
      showToast: (message: string) => void;
    };
  }
}

export class PAXTerminalService {
  private static instance: PAXTerminalService;
  private cardReadingCallback?: (data: CardData) => void;
  private cardErrorCallback?: (error: string) => void;

  private constructor() {
    this.initializeTerminal();
  }

  public static getInstance(): PAXTerminalService {
    if (!PAXTerminalService.instance) {
      PAXTerminalService.instance = new PAXTerminalService();
    }
    return PAXTerminalService.instance;
  }

  private initializeTerminal() {
    // Initialize PAX terminal if available
    if (this.isTerminalAvailable()) {
      console.log('PAX Terminal hardware detected');
      this.setupGlobalCallbacks();
    } else {
      console.warn('PAX Terminal hardware not available - running in simulation mode');
    }
  }

  private setupGlobalCallbacks() {
    // Setup global callback for Android WebView
    (window as any).onCardReadComplete = (cardDataJson: string) => {
      try {
        const cardData: CardData = JSON.parse(cardDataJson);
        if (this.cardReadingCallback) {
          this.cardReadingCallback(cardData);
        }
      } catch (error) {
        console.error('Failed to parse card data:', error);
        if (this.cardErrorCallback) {
          this.cardErrorCallback('Failed to read card data');
        }
      }
    };

    (window as any).onCardReadError = (error: string) => {
      if (this.cardErrorCallback) {
        this.cardErrorCallback(error);
      }
    };
  }

  public isTerminalAvailable(): boolean {
    return !!(window.PAXTerminal?.cardReader?.isAvailable() || window.Android);
  }

  public async startCardReading(
    callback: (data: CardData) => void,
    errorCallback?: (error: string) => void
  ): Promise<void> {
    this.cardReadingCallback = callback;
    this.cardErrorCallback = errorCallback;

    if (window.PAXTerminal?.cardReader) {
      // Use PAX Terminal native interface
      window.PAXTerminal.cardReader.startReading(callback, errorCallback);
    } else if (window.Android) {
      // Use Android WebView interface
      window.Android.readCard('onCardReadComplete');
    } else {
      // Simulation mode for development
      console.log('Simulating card reading...');
      setTimeout(() => {
        const simulatedCardData: CardData = {
          pan: '****************',
          expiryDate: '1225',
          cardholderName: 'TEST CARDHOLDER',
          cardType: 'chip',
          track2: '****************=25121010000000000000'
        };
        callback(simulatedCardData);
      }, 3000);
    }
  }

  public stopCardReading(): void {
    if (window.PAXTerminal?.cardReader) {
      window.PAXTerminal.cardReader.stopReading();
    }
    this.cardReadingCallback = undefined;
    this.cardErrorCallback = undefined;
  }

  public getCardReaderStatus(): 'idle' | 'reading' | 'error' {
    if (window.PAXTerminal?.cardReader) {
      return window.PAXTerminal.cardReader.getReaderStatus();
    }
    return 'idle';
  }

  public async printReceipt(receiptText: string, options?: PrinterOptions): Promise<boolean> {
    try {
      if (window.PAXTerminal?.printer) {
        return await window.PAXTerminal.printer.printReceipt(receiptText);
      } else if (window.Android) {
        return window.Android.printText(receiptText);
      } else {
        // Fallback to browser print
        console.log('Printing receipt (browser fallback):', receiptText);
        this.browserPrint(receiptText);
        return true;
      }
    } catch (error) {
      console.error('Print failed:', error);
      return false;
    }
  }

  private browserPrint(text: string): void {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Receipt</title>
            <style>
              body {
                font-family: 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.2;
                margin: 0;
                padding: 10px;
                white-space: pre-wrap;
              }
              @media print {
                body { margin: 0; padding: 5px; }
              }
            </style>
          </head>
          <body>${text}</body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();
    }
  }

  public getPrinterStatus(): 'ready' | 'busy' | 'error' | 'paper_low' | 'paper_out' {
    if (window.PAXTerminal?.printer) {
      return window.PAXTerminal.printer.getPrinterStatus();
    }
    return 'ready';
  }

  public showMessage(message: string, timeout: number = 3000): void {
    if (window.PAXTerminal?.display) {
      window.PAXTerminal.display.showMessage(message, timeout);
    } else if (window.Android) {
      window.Android.showToast(message);
    } else {
      // Fallback to browser alert
      console.log('Terminal Message:', message);
    }
  }

  public beep(duration: number = 100): void {
    if (window.PAXTerminal?.system) {
      window.PAXTerminal.system.beep(duration);
    } else {
      // Browser beep fallback
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.value = 800;
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration / 1000);
    }
  }

  public getTerminalInfo(): {
    serialNumber: string;
    model: string;
    firmwareVersion: string;
    batteryLevel: number;
  } {
    if (window.PAXTerminal?.system) {
      return window.PAXTerminal.system.getTerminalInfo();
    } else if (window.Android) {
      try {
        return JSON.parse(window.Android.getTerminalInfo());
      } catch {
        return {
          serialNumber: 'SIM001',
          model: 'PAX A920 Pro (Simulated)',
          firmwareVersion: '1.0.0',
          batteryLevel: 85
        };
      }
    } else {
      return {
        serialNumber: 'DEV001',
        model: 'PAX A920 Pro (Development)',
        firmwareVersion: '1.0.0-dev',
        batteryLevel: 100
      };
    }
  }

  // Utility methods for card data processing
  public static formatCardNumber(pan: string): string {
    return pan.replace(/\D/g, '').replace(/(\d{4})(?=\d)/g, '$1 ');
  }

  public static maskCardNumber(pan: string): string {
    const cleaned = pan.replace(/\D/g, '');
    if (cleaned.length < 4) return '****';
    return '**** **** **** ' + cleaned.slice(-4);
  }

  public static parseTrack2(track2: string): {
    pan: string;
    expiryDate: string;
    serviceCode: string;
    discretionaryData: string;
  } | null {
    const match = track2.match(/^(\d+)=(\d{4})(\d{3})(.*)$/);
    if (!match) return null;

    return {
      pan: match[1],
      expiryDate: match[2],
      serviceCode: match[3],
      discretionaryData: match[4]
    };
  }

  public static validateCardData(cardData: CardData): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!cardData.pan || cardData.pan.length < 13 || cardData.pan.length > 19) {
      errors.push('Invalid card number length');
    }

    if (!cardData.expiryDate || !/^\d{4}$/.test(cardData.expiryDate)) {
      errors.push('Invalid expiry date format');
    }

    if (cardData.expiryDate) {
      const year = parseInt(cardData.expiryDate.substring(0, 2)) + 2000;
      const month = parseInt(cardData.expiryDate.substring(2, 4));
      const expiry = new Date(year, month - 1);
      if (expiry < new Date()) {
        errors.push('Card has expired');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
export const paxTerminal = PAXTerminalService.getInstance();
