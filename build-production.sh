#!/bin/bash

set -e

echo "🚀 Building PAX A920 Pro POS Terminal - Production Build"
echo "========================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Clean previous builds
print_status "Cleaning previous builds..."
rm -rf dist/
rm -rf apps/*/dist/
rm -rf apps/wasm-terminal/pkg/
rm -rf mobile/

print_success "Cleaned previous builds"

# Check dependencies
print_status "Checking dependencies..."

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18 or later."
    exit 1
fi

# Check npm
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm."
    exit 1
fi

# Check Rust
if ! command -v rustc &> /dev/null; then
    print_warning "Rust is not installed. Installing Rust..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
fi

# Check wasm-pack
if ! command -v wasm-pack &> /dev/null; then
    print_warning "wasm-pack is not installed. Installing wasm-pack..."
    curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh
fi

print_success "All dependencies are available"

# Install Node.js dependencies
print_status "Installing Node.js dependencies..."

# Root dependencies
npm install

# Backend dependencies
cd apps/backend
npm install
cd ../..

# Frontend dependencies
cd apps/frontend
npm install
cd ../..

# Mock bank dependencies
cd apps/mock-bank
npm install
cd ../..

print_success "Node.js dependencies installed"

# Build WASM module
print_status "Building WASM terminal module..."

cd apps/wasm-terminal

# Build WASM module for production
wasm-pack build --target web --out-dir pkg --release --scope pax

# Create optimized WASM files
mkdir -p ../../apps/frontend/public/wasm
cp pkg/pax_terminal_wasm.js ../../apps/frontend/public/wasm/
cp pkg/pax_terminal_wasm_bg.wasm ../../apps/frontend/public/wasm/

# Create TypeScript definitions
mkdir -p ../../apps/frontend/src/types
cp pkg/pax_terminal_wasm.d.ts ../../apps/frontend/src/types/

cd ../..

print_success "WASM module built successfully"

# Build backend
print_status "Building backend..."

cd apps/backend

# Type check and build
npm run build

cd ../..

print_success "Backend built successfully"

# Build frontend
print_status "Building frontend..."

cd apps/frontend

# Build with production optimizations
npm run build

cd ../..

print_success "Frontend built successfully"

# Build mock bank
print_status "Building mock bank..."

cd apps/mock-bank

npm run build

cd ../..

print_success "Mock bank built successfully"

# Create production bundle
print_status "Creating production bundle..."

mkdir -p dist/production

# Copy backend
cp -r apps/backend/dist dist/production/backend
cp apps/backend/package.json dist/production/backend/
cp apps/backend/package-lock.json dist/production/backend/ 2>/dev/null || true

# Copy frontend
cp -r apps/frontend/dist dist/production/frontend

# Copy mock bank
cp -r apps/mock-bank/dist dist/production/mock-bank
cp apps/mock-bank/package.json dist/production/mock-bank/
cp apps/mock-bank/package-lock.json dist/production/mock-bank/ 2>/dev/null || true

# Copy configuration files
cp package.json dist/production/
cp .env.example dist/production/.env.example

# Create production environment file
cat > dist/production/.env.production << 'EOF'
NODE_ENV=production

# Database
MONGO_URI=mongodb://localhost:27017/pax_pos_production

# Stripe (REPLACE WITH YOUR LIVE KEYS)
STRIPE_SECRET_KEY=sk_live_your_actual_live_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_actual_live_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_actual_webhook_secret

# Security
JWT_SECRET=your_super_secure_256_bit_jwt_secret_for_production
CORS_ORIGIN=https://your-domain.com

# Terminal Configuration
TERMINAL_ID=PAX_A920_001
MERCHANT_ID=your_merchant_id
DEFAULT_CURRENCY=usd
PAYMENT_TIMEOUT=30000
MIN_AMOUNT=50
MAX_AMOUNT=********

# Features
CARD_READER_ENABLED=true
PRINTER_ENABLED=true
AUDIO_ENABLED=true
VIBRATION_ENABLED=true
MANUAL_ENTRY_ENABLED=true

# Receipt
RECEIPT_ENABLED=true
RECEIPT_COPIES=2
RECEIPT_SIGNATURE=true
RECEIPT_FOOTER=Thank you for your business!

# UI
UI_THEME=auto
UI_LANGUAGE=en
UI_FONT_SIZE=medium
UI_ORIENTATION=portrait

# Logging
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_MAX=1000
RATE_LIMIT_WINDOW=900000
EOF

# Create production startup script
cat > dist/production/start-production.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting PAX A920 Pro POS Terminal (Production)"

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    echo "❌ .env.production file not found!"
    echo "Please copy .env.example to .env.production and configure with your production values"
    exit 1
fi

# Install production dependencies
echo "📦 Installing production dependencies..."

cd backend && npm install --production --silent && cd ..
cd mock-bank && npm install --production --silent && cd ..

echo "✅ Dependencies installed"

# Start services
echo "🔄 Starting services..."

# Start backend
echo "Starting backend on port 3001..."
cd backend && NODE_ENV=production npm start &
BACKEND_PID=$!
cd ..

# Start mock bank
echo "Starting mock bank on port 3002..."
cd mock-bank && NODE_ENV=production npm start &
MOCK_BANK_PID=$!
cd ..

# Start frontend server
echo "Starting frontend on port 3000..."
cd frontend && python3 -m http.server 3000 &
FRONTEND_PID=$!
cd ..

echo ""
echo "🎉 All services started successfully!"
echo "=================================="
echo "Frontend:  http://localhost:3000"
echo "Backend:   http://localhost:3001"
echo "Mock Bank: http://localhost:3002"
echo ""
echo "Process IDs:"
echo "Backend:   $BACKEND_PID"
echo "Mock Bank: $MOCK_BANK_PID"
echo "Frontend:  $FRONTEND_PID"
echo ""
echo "To stop all services:"
echo "kill $BACKEND_PID $MOCK_BANK_PID $FRONTEND_PID"

# Wait for any process to exit
wait
EOF

chmod +x dist/production/start-production.sh

print_success "Production bundle created"

# Create APK-ready bundle
print_status "Creating APK-ready bundle..."

mkdir -p dist/apk

# Copy frontend build for APK
cp -r apps/frontend/dist/* dist/apk/

# Create Android-optimized index.html
cat > dist/apk/android.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no,maximum-scale=1.0">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>PAX A920 Pro POS Terminal</title>
    <link href="./assets/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        /* Hide scrollbars */
        ::-webkit-scrollbar { display: none; }
        
        /* Prevent zoom and selection */
        * {
            -webkit-user-select: none;
            -webkit-touch-callout: none;
            -webkit-text-size-adjust: 100%;
        }
        
        /* Loading screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #3B82F6, #8B5CF6);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="loading" class="loading-screen">
        <div class="loading-spinner"></div>
        <h2>PAX A920 Pro</h2>
        <p>Loading POS Terminal...</p>
    </div>
    
    <div id="app"></div>
    
    <script src="./assets/index.js"></script>
    
    <script>
        // Hide loading screen when app loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const loading = document.getElementById('loading');
                if (loading) {
                    loading.style.opacity = '0';
                    setTimeout(() => loading.remove(), 300);
                }
            }, 1000);
            
            // Android WebView integration
            console.log('PAX Terminal WebView initialized');
            
            if (typeof Android !== 'undefined') {
                console.log('PAX Terminal hardware interface available');
                window.PAXTerminalAvailable = true;
                
                // Global callbacks for Android
                window.onCardReadComplete = function(cardDataJson) {
                    console.log('Card read complete:', cardDataJson);
                    if (window.paxTerminalWasm) {
                        window.paxTerminalWasm.on_card_read_complete(cardDataJson);
                    }
                };
                
                window.onCardReadError = function(error) {
                    console.log('Card read error:', error);
                    if (window.paxTerminalWasm) {
                        window.paxTerminalWasm.on_card_read_error(error);
                    }
                };
            } else {
                console.log('Running in browser mode');
                window.PAXTerminalAvailable = false;
            }
        });
        
        // Prevent context menu
        document.addEventListener('contextmenu', e => e.preventDefault());
        
        // Prevent zoom gestures
        document.addEventListener('gesturestart', e => e.preventDefault());
        document.addEventListener('gesturechange', e => e.preventDefault());
        document.addEventListener('gestureend', e => e.preventDefault());
    </script>
</body>
</html>
EOF

# Create APK manifest
cat > dist/apk/manifest.json << 'EOF'
{
    "name": "PAX A920 Pro POS Terminal",
    "short_name": "PAX POS",
    "description": "Professional POS terminal with Stripe integration and hardware card reading",
    "start_url": "/android.html",
    "display": "fullscreen",
    "orientation": "portrait-primary",
    "theme_color": "#3B82F6",
    "background_color": "#F8FAFC",
    "scope": "/",
    "icons": [
        {
            "src": "icon-192.png",
            "sizes": "192x192",
            "type": "image/png",
            "purpose": "any maskable"
        },
        {
            "src": "icon-512.png",
            "sizes": "512x512",
            "type": "image/png",
            "purpose": "any maskable"
        }
    ],
    "categories": ["business", "finance", "productivity"],
    "lang": "en",
    "dir": "ltr"
}
EOF

print_success "APK-ready bundle created"

# Create Docker production image
print_status "Creating Docker production image..."

cat > Dockerfile.production << 'EOF'
# Multi-stage build for production
FROM node:18-alpine AS builder

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache dumb-init

WORKDIR /app

# Copy built production bundle
COPY dist/production/ .

# Install production dependencies
RUN cd backend && npm ci --only=production && npm cache clean --force
RUN cd mock-bank && npm ci --only=production && npm cache clean --force

# Production stage
FROM node:18-alpine AS production

# Install security updates and Python for frontend server
RUN apk update && apk upgrade && apk add --no-cache dumb-init python3

# Create non-root user
RUN addgroup -g 1001 -S pax-user && \
    adduser -S pax-user -u 1001

WORKDIR /app

# Copy from builder stage
COPY --from=builder --chown=pax-user:pax-user /app .

# Create logs directory
RUN mkdir -p logs && chown pax-user:pax-user logs

# Security: Run as non-root user
USER pax-user

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Expose ports
EXPOSE 3000 3001 3002

# Start all services
CMD ["sh", "start-production.sh"]
EOF

print_success "Docker production image configuration created"

# Final summary
echo ""
echo "🎉 Production build completed successfully!"
echo "=========================================="
echo ""
echo "📦 Build Outputs:"
echo "  - Production Bundle: dist/production/"
echo "  - APK Bundle: dist/apk/"
echo "  - Docker Config: Dockerfile.production"
echo ""
echo "🚀 Deployment Options:"
echo ""
echo "1. 🖥️  Server Deployment:"
echo "   cd dist/production"
echo "   ./start-production.sh"
echo ""
echo "2. 📱 APK Creation:"
echo "   bash create-apk.sh"
echo ""
echo "3. 🐳 Docker Deployment:"
echo "   docker build -f Dockerfile.production -t pax-pos:latest ."
echo "   docker run -p 3000:3000 -p 3001:3001 -p 3002:3002 pax-pos:latest"
echo ""
echo "⚠️  IMPORTANT: Configure .env.production with your live Stripe keys!"
echo ""
print_success "Ready for production deployment! 🚀"
