name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Test and Build Job
  test-and-build:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:7.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: admin123
        ports:
          - 27017:27017
        options: >-
          --health-cmd "echo 'db.runCommand(\"ping\").ok' | mongosh localhost:27017/test --quiet"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build shared types
      run: npm run build --workspace=shared-types

    - name: Lint code
      run: npm run lint

    - name: Run tests
      run: npm run test
      env:
        MONGO_URI: **********************************************************************
        STRIPE_SECRET_KEY: sk_test_dummy_key_for_testing
        STRIPE_PUBLISHABLE_KEY: pk_test_dummy_key_for_testing
        STRIPE_WEBHOOK_SECRET: whsec_dummy_secret_for_testing
        JWT_SECRET: test_jwt_secret_at_least_32_characters_long

    - name: Build backend
      run: npm run build --workspace=backend

    - name: Build frontend
      run: npm run build --workspace=frontend

    - name: Build mock-bank
      run: npm run build --workspace=mock-bank

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: |
          apps/backend/dist/
          apps/frontend/dist/
          apps/mock-bank/dist/
        retention-days: 7

  # Security Scan Job
  security-scan:
    runs-on: ubuntu-latest
    needs: test-and-build
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run security audit
      run: npm audit --audit-level=moderate

    - name: Run dependency check
      uses: dependency-check/Dependency-Check_Action@main
      with:
        project: 'PAX-POS-System'
        path: '.'
        format: 'ALL'

  # Docker Build and Push Job
  docker-build:
    runs-on: ubuntu-latest
    needs: [test-and-build, security-scan]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        service: [backend, frontend, mock-bank]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: apps/${{ matrix.service }}/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  # Deploy Job (only on main branch)
  deploy:
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Deployment step would go here"
        echo "This could include:"
        echo "- Updating Kubernetes manifests"
        echo "- Deploying to cloud provider"
        echo "- Running database migrations"
        echo "- Updating load balancer configuration"
        # Add actual deployment commands here

    - name: Run smoke tests
      run: |
        echo "Smoke tests would go here"
        echo "- Health check endpoints"
        echo "- Basic functionality tests"
        echo "- Database connectivity"
        # Add actual smoke tests here

    - name: Notify deployment status
      if: always()
      run: |
        echo "Deployment notification would go here"
        echo "- Slack notification"
        echo "- Email notification"
        echo "- Update deployment dashboard"
        # Add notification logic here
