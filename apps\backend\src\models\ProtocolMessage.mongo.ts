import mongoose, { Schema, Document } from 'mongoose';

export interface IProtocolMessage extends Document {
  protocol: string;
  mti: string;
  amount: number;
  pan?: string;
  stan: string;
  authCode?: string;
  origStan?: string;
  timestamp: string;
}

const ProtocolMessageSchema: Schema = new Schema({
  protocol: { type: String, required: true },
  mti: { type: String, required: true },
  amount: { type: Number, required: true },
  pan: String,
  stan: { type: String, required: true },
  authCode: String,
  origStan: String,
  timestamp: { type: String, required: true },
});

export default mongoose.model<IProtocolMessage>('ProtocolMessage', ProtocolMessageSchema);
