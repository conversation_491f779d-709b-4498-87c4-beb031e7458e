import { describe, it, expect, beforeEach } from 'vitest';
import { transactionService } from '../transactionService';
import Transaction from '../../models/Transaction.mongo';
import { createMockTransaction } from '../../test/helpers';

describe('TransactionService', () => {
  beforeEach(async () => {
    await Transaction.deleteMany({});
  });

  describe('createTransaction', () => {
    it('should create a transaction successfully', async () => {
      const transactionData = {
        amount: 1000,
        status: 'success' as const,
        protocolCode: '101.1',
        metadata: { test: true },
      };

      const result = await transactionService.createTransaction(transactionData);

      expect(result).toBeDefined();
      expect(result.amount).toBe(1000);
      expect(result.status).toBe('success');
      expect(result.protocolCode).toBe('101.1');
      expect(result.metadata).toEqual({ test: true });
      expect(result.createdAt).toBeDefined();
      expect(result.updatedAt).toBeDefined();
    });

    it('should throw error for invalid amount', async () => {
      const transactionData = {
        amount: -100,
        status: 'success' as const,
      };

      await expect(
        transactionService.createTransaction(transactionData)
      ).rejects.toThrow('Invalid transaction data');
    });

    it('should throw error for invalid status', async () => {
      const transactionData = {
        amount: 1000,
        status: 'invalid' as any,
      };

      await expect(
        transactionService.createTransaction(transactionData)
      ).rejects.toThrow('Invalid transaction data');
    });

    it('should create transaction with optional fields', async () => {
      const transactionData = {
        amount: 1000,
        status: 'success' as const,
        stripePaymentIntentId: 'pi_test_123',
        receiptUrl: 'https://example.com/receipt',
      };

      const result = await transactionService.createTransaction(transactionData);

      expect(result.stripePaymentIntentId).toBe('pi_test_123');
      expect(result.receiptUrl).toBe('https://example.com/receipt');
    });
  });

  describe('getTransactions', () => {
    beforeEach(async () => {
      // Create test transactions
      const transactions = [
        createMockTransaction({ amount: 1000, status: 'success' }),
        createMockTransaction({ amount: 2000, status: 'failure' }),
        createMockTransaction({ amount: 3000, status: 'pending' }),
      ];

      for (const txn of transactions) {
        await new Transaction(txn).save();
      }
    });

    it('should return transactions with default pagination', async () => {
      const result = await transactionService.getTransactions();

      expect(result).toHaveLength(3);
      expect(result[0].amount).toBe(3000); // Most recent first
    });

    it('should respect limit parameter', async () => {
      const result = await transactionService.getTransactions(2);

      expect(result).toHaveLength(2);
    });

    it('should respect offset parameter', async () => {
      const result = await transactionService.getTransactions(10, 1);

      expect(result).toHaveLength(2);
      expect(result[0].amount).toBe(2000);
    });

    it('should return empty array when no transactions exist', async () => {
      await Transaction.deleteMany({});
      const result = await transactionService.getTransactions();

      expect(result).toHaveLength(0);
    });
  });

  describe('getTransactionById', () => {
    it('should return transaction by ID', async () => {
      const mockTxn = createMockTransaction();
      const savedTxn = await new Transaction(mockTxn).save();

      const result = await transactionService.getTransactionById(savedTxn._id.toString());

      expect(result).toBeDefined();
      expect(result!._id.toString()).toBe(savedTxn._id.toString());
      expect(result!.amount).toBe(mockTxn.amount);
    });

    it('should return null for non-existent ID', async () => {
      const result = await transactionService.getTransactionById('507f1f77bcf86cd799439011');

      expect(result).toBeNull();
    });

    it('should throw error for invalid ID format', async () => {
      await expect(
        transactionService.getTransactionById('invalid-id')
      ).rejects.toThrow('Failed to fetch transaction');
    });
  });

  describe('updateTransaction', () => {
    it('should update transaction successfully', async () => {
      const mockTxn = createMockTransaction({ status: 'pending' });
      const savedTxn = await new Transaction(mockTxn).save();

      const result = await transactionService.updateTransaction(
        savedTxn._id.toString(),
        { status: 'success' }
      );

      expect(result).toBeDefined();
      expect(result!.status).toBe('success');
      expect(result!.updatedAt).not.toBe(mockTxn.updatedAt);
    });

    it('should return null for non-existent transaction', async () => {
      const result = await transactionService.updateTransaction(
        '507f1f77bcf86cd799439011',
        { status: 'success' }
      );

      expect(result).toBeNull();
    });

    it('should validate updated fields', async () => {
      const mockTxn = createMockTransaction();
      const savedTxn = await new Transaction(mockTxn).save();

      await expect(
        transactionService.updateTransaction(
          savedTxn._id.toString(),
          { amount: -100 } as any
        )
      ).rejects.toThrow('Failed to update transaction');
    });
  });
});
