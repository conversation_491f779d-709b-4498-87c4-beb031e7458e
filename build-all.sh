#!/bin/bash

set -e

echo "🚀 Building PAX A920 Pro POS Terminal - Complete Monorepo"
echo "=========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18 or later."
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check Rust (for WASM)
    if ! command -v rustc &> /dev/null; then
        print_warning "Rust is not installed. Installing Rust..."
        curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
        source ~/.cargo/env
    fi
    
    # Check wasm-pack
    if ! command -v wasm-pack &> /dev/null; then
        print_warning "wasm-pack is not installed. Installing wasm-pack..."
        curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh
    fi
    
    print_success "All dependencies are available"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Root dependencies
    npm install
    
    # Backend dependencies
    cd apps/backend
    npm install
    cd ../..
    
    # Frontend dependencies
    cd apps/frontend
    npm install
    cd ../..
    
    # Mock bank dependencies
    cd apps/mock-bank
    npm install
    cd ../..
    
    print_success "Dependencies installed"
}

# Build WASM module
build_wasm() {
    print_status "Building WASM terminal module..."
    
    cd apps/wasm-terminal
    
    # Make build script executable
    chmod +x build.sh
    
    # Build WASM module
    ./build.sh
    
    cd ../..
    
    print_success "WASM module built successfully"
}

# Build backend
build_backend() {
    print_status "Building backend..."
    
    cd apps/backend
    npm run build
    cd ../..
    
    print_success "Backend built successfully"
}

# Build frontend
build_frontend() {
    print_status "Building frontend..."
    
    cd apps/frontend
    npm run build
    cd ../..
    
    print_success "Frontend built successfully"
}

# Build mock bank
build_mock_bank() {
    print_status "Building mock bank..."
    
    cd apps/mock-bank
    npm run build
    cd ../..
    
    print_success "Mock bank built successfully"
}

# Create production bundle
create_bundle() {
    print_status "Creating production bundle..."
    
    # Create bundle directory
    mkdir -p dist/bundle
    
    # Copy backend
    cp -r apps/backend/dist dist/bundle/backend
    cp apps/backend/package.json dist/bundle/backend/
    
    # Copy frontend
    cp -r apps/frontend/dist dist/bundle/frontend
    
    # Copy mock bank
    cp -r apps/mock-bank/dist dist/bundle/mock-bank
    cp apps/mock-bank/package.json dist/bundle/mock-bank/
    
    # Copy WASM files
    mkdir -p dist/bundle/frontend/wasm
    cp apps/frontend/public/wasm/* dist/bundle/frontend/wasm/ 2>/dev/null || true
    
    # Copy configuration files
    cp package.json dist/bundle/
    cp .env.example dist/bundle/.env
    
    # Create startup script
    cat > dist/bundle/start.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting PAX A920 Pro POS Terminal..."

# Install production dependencies
cd backend && npm install --production && cd ..
cd mock-bank && npm install --production && cd ..

# Start services
echo "Starting backend..."
cd backend && npm start &
BACKEND_PID=$!

echo "Starting mock bank..."
cd ../mock-bank && npm start &
MOCK_BANK_PID=$!

echo "Starting frontend server..."
cd ../frontend && python3 -m http.server 3000 &
FRONTEND_PID=$!

echo "All services started!"
echo "Backend PID: $BACKEND_PID"
echo "Mock Bank PID: $MOCK_BANK_PID"
echo "Frontend PID: $FRONTEND_PID"

echo "Access the POS terminal at: http://localhost:3000"

# Wait for any process to exit
wait
EOF
    
    chmod +x dist/bundle/start.sh
    
    print_success "Production bundle created in dist/bundle/"
}

# Create APK-ready bundle
create_apk_bundle() {
    print_status "Creating APK-ready bundle..."
    
    # Create APK bundle directory
    mkdir -p dist/apk-bundle
    
    # Copy frontend build
    cp -r apps/frontend/dist/* dist/apk-bundle/
    
    # Create Android WebView HTML
    cat > dist/apk-bundle/android.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no">
    <title>PAX A920 Pro POS Terminal</title>
    <link href="./assets/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }
        
        /* Hide scrollbars */
        ::-webkit-scrollbar {
            display: none;
        }
        
        /* Prevent zoom */
        * {
            -webkit-user-select: none;
            -webkit-touch-callout: none;
        }
    </style>
</head>
<body>
    <div id="app"></div>
    <script src="./assets/index.js"></script>
    
    <script>
        // Android WebView integration
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PAX Terminal WebView initialized');
            
            // Check if Android interface is available
            if (typeof Android !== 'undefined') {
                console.log('PAX Terminal hardware interface available');
                window.PAXTerminalAvailable = true;
                
                // Global callbacks for Android
                window.onCardReadComplete = function(cardDataJson) {
                    console.log('Card read complete:', cardDataJson);
                    // This will be handled by the WASM module
                };
                
                window.onCardReadError = function(error) {
                    console.log('Card read error:', error);
                };
            } else {
                console.log('Running in browser mode');
                window.PAXTerminalAvailable = false;
            }
        });
    </script>
</body>
</html>
EOF
    
    # Create manifest for APK
    cat > dist/apk-bundle/manifest.json << 'EOF'
{
    "name": "PAX A920 Pro POS Terminal",
    "short_name": "PAX POS",
    "description": "Professional POS terminal with Stripe integration",
    "start_url": "/android.html",
    "display": "fullscreen",
    "orientation": "portrait",
    "theme_color": "#3B82F6",
    "background_color": "#F8FAFC",
    "icons": [
        {
            "src": "icon-192.png",
            "sizes": "192x192",
            "type": "image/png"
        },
        {
            "src": "icon-512.png",
            "sizes": "512x512",
            "type": "image/png"
        }
    ]
}
EOF
    
    print_success "APK-ready bundle created in dist/apk-bundle/"
}

# Create Docker images
create_docker_images() {
    print_status "Creating Docker images..."
    
    # Create production Dockerfile
    cat > Dockerfile.production << 'EOF'
FROM node:18-alpine AS builder

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache dumb-init

# Create app user
RUN addgroup -g 1001 -S pax-user && \
    adduser -S pax-user -u 1001

WORKDIR /app

# Copy built bundle
COPY dist/bundle/ .

# Install production dependencies
RUN cd backend && npm install --production
RUN cd mock-bank && npm install --production

# Production stage
FROM node:18-alpine AS production

RUN apk update && apk upgrade && apk add --no-cache dumb-init python3

RUN addgroup -g 1001 -S pax-user && \
    adduser -S pax-user -u 1001

WORKDIR /app

# Copy from builder
COPY --from=builder --chown=pax-user:pax-user /app .

# Security: Run as non-root user
USER pax-user

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

EXPOSE 3001 3002 3000

# Start all services
CMD ["sh", "-c", "cd backend && npm start & cd ../mock-bank && npm start & cd ../frontend && python3 -m http.server 3000 & wait"]
EOF
    
    # Build Docker image
    docker build -f Dockerfile.production -t pax-pos:latest .
    
    print_success "Docker image created: pax-pos:latest"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    # Backend tests
    cd apps/backend
    npm test || print_warning "Backend tests failed"
    cd ../..
    
    # Frontend tests
    cd apps/frontend
    npm test -- --run || print_warning "Frontend tests failed"
    cd ../..
    
    print_success "Tests completed"
}

# Main build process
main() {
    echo "Starting build process..."
    
    # Check dependencies
    check_dependencies
    
    # Install dependencies
    install_dependencies
    
    # Build WASM module
    build_wasm
    
    # Build all services
    build_backend
    build_frontend
    build_mock_bank
    
    # Run tests
    run_tests
    
    # Create bundles
    create_bundle
    create_apk_bundle
    
    # Create Docker images (optional)
    if command -v docker &> /dev/null; then
        create_docker_images
    else
        print_warning "Docker not available, skipping Docker image creation"
    fi
    
    echo ""
    echo "🎉 Build completed successfully!"
    echo "=========================================="
    echo "📦 Production bundle: dist/bundle/"
    echo "📱 APK-ready bundle: dist/apk-bundle/"
    echo "🐳 Docker image: pax-pos:latest"
    echo ""
    echo "To start the application:"
    echo "  cd dist/bundle && ./start.sh"
    echo ""
    echo "To create APK:"
    echo "  Use dist/apk-bundle/ with Cordova/PhoneGap"
    echo "  Or bundle with WebView wrapper"
    echo ""
    echo "To run with Docker:"
    echo "  docker run -p 3000:3000 -p 3001:3001 -p 3002:3002 pax-pos:latest"
}

# Run main function
main "$@"
