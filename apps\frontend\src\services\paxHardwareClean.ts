/**
 * Clean PAX Terminal Hardware Service
 * 
 * Simplified hardware interface for PAX A920 Pro
 * Works with the production card reader service
 */

export interface PAXTerminalInfo {
  serialNumber: string;
  model: string;
  firmwareVersion: string;
  batteryLevel: number;
  networkStatus: 'connected' | 'disconnected';
  cardReaderStatus: 'idle' | 'reading' | 'error';
  printerStatus: 'ready' | 'busy' | 'error' | 'paper_low' | 'paper_out';
  nfcEnabled: boolean;
  emvEnabled: boolean;
}

export class PAXHardwareInterface {
  private terminalInfo: PAXTerminalInfo;
  private isConnected: boolean = false;

  constructor() {
    // Initialize with your actual PAX A920 Pro specifications
    this.terminalInfo = {
      serialNumber: '1853944350', // Your actual terminal serial number
      model: 'PAX A920PC9',
      firmwareVersion: 'PayDroid_10.0.0_Sagittarius_V11-1.29_20240913',
      batteryLevel: 76,
      networkStatus: 'connected',
      cardReaderStatus: 'idle',
      printerStatus: 'ready',
      nfcEnabled: true,
      emvEnabled: true
    };
  }

  /**
   * Initialize hardware connection
   */
  public async initialize(): Promise<boolean> {
    try {
      // Check if PAX hardware is available
      if (window.PAXHardwareReader?.isAvailable) {
        this.isConnected = window.PAXHardwareReader.isAvailable();
      } else {
        // Fallback to simulation mode
        this.isConnected = false;
      }

      console.log(`PAX Hardware initialized: ${this.isConnected ? 'Connected' : 'Simulation Mode'}`);
      return this.isConnected;
    } catch (error) {
      console.error('PAX Hardware initialization failed:', error);
      this.isConnected = false;
      return false;
    }
  }

  /**
   * Check if hardware is connected
   */
  public isHardwareConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Get terminal information
   */
  public getTerminalInfo(): PAXTerminalInfo {
    // Update battery level if available
    if (window.PAXHardwareReader?.getTerminalInfo) {
      try {
        const info = JSON.parse(window.PAXHardwareReader.getTerminalInfo());
        this.terminalInfo.batteryLevel = info.batteryLevel || this.terminalInfo.batteryLevel;
      } catch (error) {
        console.warn('Failed to get updated terminal info:', error);
      }
    }

    return { ...this.terminalInfo };
  }

  /**
   * Get card reader status
   */
  public getCardReaderStatus(): 'idle' | 'reading' | 'error' {
    if (window.PAXHardwareReader?.getStatus) {
      try {
        const status = window.PAXHardwareReader.getStatus();
        return status as 'idle' | 'reading' | 'error';
      } catch (error) {
        console.error('Failed to get card reader status:', error);
        return 'error';
      }
    }
    return this.terminalInfo.cardReaderStatus;
  }

  /**
   * Get printer status
   */
  public getPrinterStatus(): 'ready' | 'busy' | 'error' | 'paper_low' | 'paper_out' {
    // In a real implementation, you would check the actual printer status
    return this.terminalInfo.printerStatus;
  }

  /**
   * Check if NFC is enabled
   */
  public isNFCEnabled(): boolean {
    return this.terminalInfo.nfcEnabled;
  }

  /**
   * Check if EMV is enabled
   */
  public isEMVEnabled(): boolean {
    return this.terminalInfo.emvEnabled;
  }

  /**
   * Update terminal status
   */
  public updateStatus(): void {
    // Update various status indicators
    this.terminalInfo.cardReaderStatus = this.getCardReaderStatus();
    this.terminalInfo.printerStatus = this.getPrinterStatus();
    
    // Update network status
    this.terminalInfo.networkStatus = navigator.onLine ? 'connected' : 'disconnected';
  }

  /**
   * Test hardware connectivity
   */
  public async testHardware(): Promise<{
    success: boolean;
    message: string;
    details: any;
  }> {
    try {
      const terminalInfo = this.getTerminalInfo();
      const cardReaderStatus = this.getCardReaderStatus();
      const printerStatus = this.getPrinterStatus();

      const allSystemsOk = 
        cardReaderStatus !== 'error' && 
        printerStatus !== 'error' &&
        terminalInfo.networkStatus === 'connected';

      return {
        success: allSystemsOk,
        message: allSystemsOk ? 'All systems operational' : 'Some systems have issues',
        details: {
          terminalInfo,
          cardReaderStatus,
          printerStatus,
          hardwareConnected: this.isConnected,
          nfcEnabled: this.isNFCEnabled(),
          emvEnabled: this.isEMVEnabled()
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Hardware test failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Get hardware capabilities
   */
  public getCapabilities(): {
    cardReading: string[];
    printing: boolean;
    nfc: boolean;
    emv: boolean;
    pinpad: boolean;
    signature: boolean;
  } {
    return {
      cardReading: ['chip', 'contactless', 'swipe', 'nfc'],
      printing: true,
      nfc: this.terminalInfo.nfcEnabled,
      emv: this.terminalInfo.emvEnabled,
      pinpad: true,
      signature: true
    };
  }

  /**
   * Check connectivity
   */
  public async checkConnectivity(): Promise<{ online: boolean; details?: any }> {
    try {
      const online = navigator.onLine && this.isConnected;
      return {
        online,
        details: {
          navigator_online: navigator.onLine,
          hardware_connected: this.isConnected,
          network_status: this.terminalInfo.networkStatus
        }
      };
    } catch (error) {
      return {
        online: false,
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Print receipt
   */
  public async printReceipt(receiptText: string): Promise<boolean> {
    try {
      if (window.PAXHardwareReader?.printText) {
        return window.PAXHardwareReader.printText(receiptText);
      }

      // Fallback to browser print
      console.log('Printing receipt (simulation):', receiptText);
      return true;
    } catch (error) {
      console.error('Receipt printing failed:', error);
      return false;
    }
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    this.isConnected = false;
    console.log('PAX Hardware interface destroyed');
  }
}

// Create singleton instance
export const paxHardware = new PAXHardwareInterface();

// Global interface declarations for PAX hardware
declare global {
  interface Window {
    PAXHardwareReader?: {
      isAvailable(): boolean;
      getStatus(): string;
      getTerminalInfo(): string;
      startReading(callback: string): void;
      stopReading(): void;
      printText?(text: string): boolean;
    };
  }
}
