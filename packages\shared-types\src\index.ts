// This is the entry point for shared types.
// We'll define more specific types as we build out features.

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code?: string;
    details?: any;
  };
}

export enum TransactionStatus {
  PENDING = 'PENDING',
  AUTHORIZED = 'AUTHORIZED',
  CAPTURED = 'CAPTURED',
  FAILED = 'FAILED',
  VOIDED = 'VOIDED',
  REFUNDED = 'REFUNDED',
  PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED',
  PENDING_STRIPE = 'PENDING_STRIPE', // Waiting for Stripe SDK interaction
}

export interface BaseTransaction {
  transactionId: string;
  merchantId: string;
  terminalId: string;
  amount: number; // In minor units (e.g., cents)
  currency: string; // ISO 4217 currency code (e.g., "USD", "NGN")
  status: TransactionStatus;
  createdAt: string; // ISO 8601 date string
  updatedAt: string; // ISO 8601 date string
}

// Add more shared types here as needed, for example:
// export interface UserProfile { ... }
// export interface TerminalSettings { ... }

// Example of a more specific API payload type
export interface CreateSimulatedPaymentRequest {
  amount: number;
  currency: string;
  simulate: 'success' | 'failure'; // For Phase 1 simulation
  terminalId: string; // Assuming terminalId is known by frontend for simulation
  merchantId: string; // Assuming merchantId is known
}

export interface CreateSimulatedPaymentResponse extends BaseTransaction {
  // Any specific fields for this response
}

export interface CreateStripePaymentIntentRequest {
  amount: number;
  currency: string;
  terminalId: string;
  merchantId: string;
}

export interface CreateStripePaymentIntentResponse {
  clientSecret: string;
  transactionId: string; // The ID of the transaction record created on our backend
}

export interface ConnectionTokenResponse {
  connectionToken: string;
}

// ISO 8583 Style JSON message structure (as discussed)
export interface SimulatedBankMessage {
  protocolEventCode: '101.1' | '101.3' | '101.5' | '101.8';
  mti: string;
  transactionId: string; // Link to our system's transactionId
  stripePaymentIntentId?: string;
  timestamp: string; // ISO 8601
  // Common Data Elements (DEs) - add more as needed from previous spec
  de2_pan?: string; // Masked
  de3_processingCode?: string;
  de4_transactionAmount?: number;
  de11_stan?: string;
  de12_localTime?: string; // HHMMSS
  de13_localDate?: string; // MMDD
  de18_merchantCategoryCode?: string;
  de37_retrievalReferenceNumber?: string;
  de38_approvalCode?: string;
  de39_responseCode?: string; // From bank
  de41_cardAcceptorTerminalId?: string;
  de42_cardAcceptorIdCode?: string;
  de49_currencyCodeTransaction?: string; // Numeric ISO 4217
  de62_additionalData?: Record<string, any>;
  de90_originalDataElements?: Record<string, any>;
  // ... other DEs based on the spec for each 101.x event
}
