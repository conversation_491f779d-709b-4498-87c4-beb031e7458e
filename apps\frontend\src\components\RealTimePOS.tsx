import { useState, useEffect, useRef } from 'react';

type TransactionState = 
  | 'idle'
  | 'amount-entry'
  | 'payment-processing'
  | 'card-reading'
  | 'authorizing'
  | 'approved'
  | 'declined'
  | 'printing'
  | 'complete'
  | 'error';

interface RealTimeTransaction {
  id: string;
  amount: number;
  status: TransactionState;
  paymentIntentId?: string;
  cardLast4?: string;
  authCode?: string;
  receiptData?: string;
  errorMessage?: string;
  timestamp: Date;
}

export function RealTimePOS() {
  const [transaction, setTransaction] = useState<RealTimeTransaction>({
    id: '',
    amount: 0,
    status: 'idle',
    timestamp: new Date()
  });

  const [amountInput, setAmountInput] = useState('');
  const [countdown, setCountdown] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout>();

  // Real-time status updates
  useEffect(() => {
    if (transaction.status === 'payment-processing') {
      setCountdown(30); // 30 second timeout
      intervalRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            setTransaction(prev => ({
              ...prev,
              status: 'error',
              errorMessage: 'Transaction timeout - please try again'
            }));
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [transaction.status]);

  const startNewTransaction = () => {
    const newId = `TXN_${Date.now()}`;
    setTransaction({
      id: newId,
      amount: 0,
      status: 'amount-entry',
      timestamp: new Date()
    });
    setAmountInput('');
  };

  const processPayment = async () => {
    if (!amountInput || parseFloat(amountInput) <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    const amount = Math.round(parseFloat(amountInput) * 100); // Convert to cents
    
    setTransaction(prev => ({
      ...prev,
      amount,
      status: 'payment-processing'
    }));

    try {
      // Step 1: Card Reading Simulation
      setTransaction(prev => ({ ...prev, status: 'card-reading' }));
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Step 2: Authorization
      setTransaction(prev => ({ ...prev, status: 'authorizing' }));
      
      // Real Stripe payment processing
      const response = await fetch('/api/v1/payments/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          currency: 'usd',
          payment_method: 'card',
          confirm: true,
          return_url: window.location.origin
        }),
      });

      const paymentResult = await response.json();

      if (paymentResult.success && paymentResult.data.status === 'succeeded') {
        // Payment approved
        setTransaction(prev => ({
          ...prev,
          status: 'approved',
          paymentIntentId: paymentResult.data.id,
          cardLast4: paymentResult.data.payment_method?.card?.last4 || '****',
          authCode: generateAuthCode()
        }));

        // Auto-generate and print receipt
        setTimeout(async () => {
          await generateReceipt();
        }, 2000);

      } else {
        // Payment declined
        setTransaction(prev => ({
          ...prev,
          status: 'declined',
          errorMessage: paymentResult.error || 'Payment was declined'
        }));
      }

    } catch (error) {
      console.error('Payment processing error:', error);
      setTransaction(prev => ({
        ...prev,
        status: 'error',
        errorMessage: 'Payment processing failed - please try again'
      }));
    }
  };

  const generateReceipt = async () => {
    setTransaction(prev => ({ ...prev, status: 'printing' }));

    try {
      const response = await fetch(`/api/v1/receipts/${transaction.id}`);
      const receiptData = await response.json();

      if (receiptData.success) {
        setTransaction(prev => ({
          ...prev,
          receiptData: receiptData.data.customerReceipt,
          status: 'complete'
        }));

        // Auto-print receipt
        await printReceipt(receiptData.data.customerReceipt);
      }
    } catch (error) {
      console.error('Receipt generation failed:', error);
      setTransaction(prev => ({ ...prev, status: 'complete' }));
    }
  };

  const printReceipt = async (receiptText: string) => {
    try {
      // Send to printer
      await fetch('/api/v1/receipts/print', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId: transaction.id,
          copies: 2, // Customer + merchant copy
          customerCopy: true
        }),
      });

      // Also trigger browser print for backup
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head><title>Receipt</title></head>
            <body style="font-family: monospace; white-space: pre-wrap;">
              ${receiptText}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
      }
    } catch (error) {
      console.error('Printing failed:', error);
    }
  };

  const generateAuthCode = () => {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  };

  const getStatusDisplay = () => {
    switch (transaction.status) {
      case 'idle':
        return { text: 'Ready for Transaction', icon: '💳', color: 'bg-blue-500' };
      case 'amount-entry':
        return { text: 'Enter Amount', icon: '💰', color: 'bg-yellow-500' };
      case 'payment-processing':
        return { text: 'Insert or Tap Card', icon: '📱', color: 'bg-orange-500' };
      case 'card-reading':
        return { text: 'Reading Card...', icon: '📖', color: 'bg-blue-600' };
      case 'authorizing':
        return { text: 'Authorizing Payment...', icon: '⚡', color: 'bg-purple-600' };
      case 'approved':
        return { text: 'Payment Approved!', icon: '✅', color: 'bg-green-500' };
      case 'declined':
        return { text: 'Payment Declined', icon: '❌', color: 'bg-red-500' };
      case 'printing':
        return { text: 'Printing Receipt...', icon: '🖨️', color: 'bg-indigo-500' };
      case 'complete':
        return { text: 'Transaction Complete', icon: '🎉', color: 'bg-green-600' };
      case 'error':
        return { text: 'Transaction Error', icon: '⚠️', color: 'bg-red-600' };
      default:
        return { text: 'Ready', icon: '💳', color: 'bg-gray-500' };
    }
  };

  const status = getStatusDisplay();

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-2xl shadow-2xl border border-slate-200 overflow-hidden">
        {/* Status Header */}
        <div className={`${status.color} p-8 text-white text-center`}>
          <div className="text-6xl mb-4">{status.icon}</div>
          <h1 className="text-3xl font-bold mb-2">{status.text}</h1>
          {transaction.amount > 0 && (
            <div className="text-2xl font-mono">
              ${(transaction.amount / 100).toFixed(2)}
            </div>
          )}
          {countdown > 0 && (
            <div className="text-lg mt-2 opacity-90">
              Timeout in {countdown}s
            </div>
          )}
        </div>

        <div className="p-8">
          {/* Amount Entry */}
          {transaction.status === 'amount-entry' && (
            <div className="text-center space-y-6">
              <div className="max-w-md mx-auto">
                <label className="block text-lg font-semibold text-slate-700 mb-4">
                  Transaction Amount
                </label>
                <div className="relative">
                  <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-500 text-2xl">$</span>
                  <input
                    type="number"
                    value={amountInput}
                    onChange={(e) => setAmountInput(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 border-2 border-slate-300 rounded-2xl focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-transparent text-3xl font-bold text-center"
                    placeholder="0.00"
                    min="0.01"
                    step="0.01"
                    autoFocus
                  />
                </div>
              </div>
              
              <button
                onClick={processPayment}
                disabled={!amountInput || parseFloat(amountInput) <= 0}
                className="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 px-12 rounded-2xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-bold text-xl shadow-lg hover:shadow-xl"
              >
                💳 Process Payment
              </button>
            </div>
          )}

          {/* Processing States */}
          {['payment-processing', 'card-reading', 'authorizing'].includes(transaction.status) && (
            <div className="text-center space-y-6">
              <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto"></div>
              <div className="text-lg text-slate-600">
                {transaction.status === 'payment-processing' && 'Please insert, tap, or swipe your card'}
                {transaction.status === 'card-reading' && 'Do not remove your card...'}
                {transaction.status === 'authorizing' && 'Contacting your bank...'}
              </div>
            </div>
          )}

          {/* Success State */}
          {transaction.status === 'approved' && (
            <div className="text-center space-y-4">
              <div className="text-6xl">✅</div>
              <div className="text-2xl font-bold text-green-600">Payment Approved!</div>
              <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-slate-600">Amount:</span>
                    <div className="text-xl font-bold">${(transaction.amount / 100).toFixed(2)}</div>
                  </div>
                  <div>
                    <span className="font-medium text-slate-600">Card:</span>
                    <div className="font-mono">****{transaction.cardLast4}</div>
                  </div>
                  <div>
                    <span className="font-medium text-slate-600">Auth Code:</span>
                    <div className="font-mono">{transaction.authCode}</div>
                  </div>
                  <div>
                    <span className="font-medium text-slate-600">Transaction ID:</span>
                    <div className="font-mono text-xs">{transaction.id}</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Error States */}
          {['declined', 'error'].includes(transaction.status) && (
            <div className="text-center space-y-6">
              <div className="text-6xl">❌</div>
              <div className="text-2xl font-bold text-red-600">
                {transaction.status === 'declined' ? 'Payment Declined' : 'Transaction Error'}
              </div>
              {transaction.errorMessage && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-4 text-red-700">
                  {transaction.errorMessage}
                </div>
              )}
              <div className="space-x-4">
                <button
                  onClick={() => setTransaction(prev => ({ ...prev, status: 'amount-entry' }))}
                  className="bg-blue-600 text-white py-3 px-6 rounded-xl hover:bg-blue-700 transition-colors font-semibold"
                >
                  🔄 Try Again
                </button>
                <button
                  onClick={startNewTransaction}
                  className="bg-gray-600 text-white py-3 px-6 rounded-xl hover:bg-gray-700 transition-colors font-semibold"
                >
                  ❌ Cancel
                </button>
              </div>
            </div>
          )}

          {/* Complete State */}
          {transaction.status === 'complete' && (
            <div className="text-center space-y-6">
              <div className="text-6xl">🎉</div>
              <div className="text-2xl font-bold text-green-600">Transaction Complete!</div>
              <div className="text-lg text-slate-600">Receipt has been printed</div>
              
              {transaction.receiptData && (
                <div className="max-w-md mx-auto bg-slate-50 rounded-xl p-4">
                  <h3 className="font-bold text-slate-800 mb-2">Receipt Preview</h3>
                  <div className="bg-white p-4 rounded-lg border border-slate-200 max-h-48 overflow-y-auto">
                    <pre className="font-mono text-xs text-slate-800 whitespace-pre-wrap">
                      {transaction.receiptData}
                    </pre>
                  </div>
                </div>
              )}

              <button
                onClick={startNewTransaction}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-8 rounded-2xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-500 transition-all duration-200 font-bold text-xl shadow-lg hover:shadow-xl"
              >
                🆕 New Transaction
              </button>
            </div>
          )}

          {/* Idle State */}
          {transaction.status === 'idle' && (
            <div className="text-center">
              <button
                onClick={startNewTransaction}
                className="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-6 px-12 rounded-2xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-500 transition-all duration-200 font-bold text-2xl shadow-lg hover:shadow-xl"
              >
                🚀 Start New Transaction
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
