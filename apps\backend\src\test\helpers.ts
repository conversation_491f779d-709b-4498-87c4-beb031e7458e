import { FastifyInstance } from 'fastify';
import { createApp } from '../app';
import { vi, expect } from 'vitest';

export async function createTestApp() {
  const app = await createApp();
  return app;
}

export const mockStripeService = {
  createConnectionToken: vi.fn(),
  createPaymentIntent: vi.fn(),
  capturePaymentIntent: vi.fn(),
  retrievePaymentIntent: vi.fn(),
};

export const mockProtocolService = {
  sendProtocolMessage: vi.fn(),
  getProtocolDescription: vi.fn(),
};

export function createMockTransaction(overrides: any = {}) {
  return {
    _id: '507f1f77bcf86cd799439011',
    amount: 1000,
    status: 'success',
    protocolCode: '101.1',
    stripePaymentIntentId: 'pi_test_123456789',
    receiptUrl: 'https://example.com/receipt/123',
    metadata: { test: true },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides,
  };
}

export function createMockProtocolMessage(overrides: any = {}) {
  return {
    protocolEventCode: '101.1',
    mti: '0100',
    transactionId: '507f1f77bcf86cd799439011',
    timestamp: new Date().toISOString(),
    de4_transactionAmount: 1000,
    de11_stan: '123456',
    de12_localTime: '123456',
    de13_localDate: '1225',
    de18_merchantCategoryCode: '5999',
    de37_retrievalReferenceNumber: '123456789012',
    de41_cardAcceptorTerminalId: 'PAX920PRO',
    de42_cardAcceptorIdCode: 'MERCHANT001',
    ...overrides,
  };
}

export function createMockStripePaymentIntent(overrides: any = {}) {
  return {
    id: 'pi_test_123456789',
    object: 'payment_intent',
    amount: 1000,
    currency: 'usd',
    status: 'requires_payment_method',
    client_secret: 'pi_test_123456789_secret_test',
    created: Math.floor(Date.now() / 1000),
    ...overrides,
  };
}

export function createMockConnectionToken(overrides: any = {}) {
  return {
    id: 'ctoken_test_123456789',
    object: 'terminal.connection_token',
    secret: 'pst_test_123456789_secret',
    created: Math.floor(Date.now() / 1000),
    ...overrides,
  };
}

export async function makeRequest(
  app: FastifyInstance,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  url: string,
  payload?: any,
  headers?: Record<string, string>
) {
  const response = await app.inject({
    method,
    url,
    payload,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  });

  return {
    statusCode: response.statusCode,
    body: JSON.parse(response.body),
    headers: response.headers,
  };
}

export function expectSuccessResponse(response: any, expectedData?: any) {
  expect(response.statusCode).toBe(200);
  expect(response.body.success).toBe(true);
  if (expectedData) {
    expect(response.body.data).toMatchObject(expectedData);
  }
}

export function expectErrorResponse(
  response: any,
  expectedStatusCode: number,
  expectedError?: string
) {
  expect(response.statusCode).toBe(expectedStatusCode);
  expect(response.body.success).toBe(false);
  if (expectedError) {
    expect(response.body.error).toBe(expectedError);
  }
}

export function expectValidationError(response: any) {
  expectErrorResponse(response, 400, 'VALIDATION_ERROR');
  expect(response.body.details).toBeDefined();
}
