// WASM Loader with fallback for development
export interface WASMTerminal {
  is_hardware_available(): boolean;
  get_terminal_info(): Promise<any>;
  start_card_reading(callback: (data: any) => void): Promise<void>;
  stop_card_reading(): void;
  print_receipt(text: string): Promise<boolean>;
  beep(duration: number): void;
  vibrate(duration: number): void;
  format_card_number(pan: string): string;
  mask_card_number(pan: string): string;
}

// Fallback implementation for when WASM is not available
class FallbackTerminal implements WASMTerminal {
  is_hardware_available(): boolean {
    return false;
  }

  async get_terminal_info(): Promise<any> {
    return {
      serial_number: 'FALLBACK_001',
      model: 'PAX A920 Pro (Fallback)',
      firmware_version: '1.0.0-fallback',
      battery_level: 100,
      is_hardware_available: false
    };
  }

  async start_card_reading(callback: (data: any) => void): Promise<void> {
    console.log('Fallback: Simulating card reading...');
    
    // Simulate card reading delay
    setTimeout(() => {
      const simulatedCardData = {
        pan: '****************',
        expiry_date: '1225',
        cardholder_name: 'TEST CARDHOLDER',
        card_type: 'chip',
        track2: '****************=25121010000000000000'
      };
      
      callback(simulatedCardData);
    }, 3000);
  }

  stop_card_reading(): void {
    console.log('Fallback: Stopping card reading');
  }

  async print_receipt(text: string): Promise<boolean> {
    console.log('Fallback: Printing receipt:', text);
    
    // Fallback to browser print
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Receipt</title>
            <style>
              body {
                font-family: 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.2;
                margin: 0;
                padding: 10px;
                white-space: pre-wrap;
              }
              @media print {
                body { margin: 0; padding: 5px; }
              }
            </style>
          </head>
          <body>${text}</body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();
    }
    
    return true;
  }

  beep(duration: number): void {
    console.log(`Fallback: Beep for ${duration}ms`);
    
    // Browser beep fallback
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.value = 800;
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration / 1000);
    } catch (error) {
      console.warn('Audio not available:', error);
    }
  }

  vibrate(duration: number): void {
    console.log(`Fallback: Vibrate for ${duration}ms`);
    
    // Browser vibration API
    if ('vibrate' in navigator) {
      navigator.vibrate(duration);
    }
  }

  format_card_number(pan: string): string {
    const cleaned = pan.replace(/\D/g, '');
    return cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
  }

  mask_card_number(pan: string): string {
    const cleaned = pan.replace(/\D/g, '');
    if (cleaned.length < 4) return '****';
    return '**** **** **** ' + cleaned.slice(-4);
  }
}

// WASM loader with fallback
export class WASMLoader {
  private static terminal: WASMTerminal | null = null;
  private static isLoading = false;

  static async loadTerminal(): Promise<WASMTerminal> {
    if (this.terminal) {
      return this.terminal;
    }

    if (this.isLoading) {
      // Wait for loading to complete
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.terminal!;
    }

    this.isLoading = true;

    try {
      console.log('Attempting to load WASM module...');
      
      // Try to load WASM module
      const wasmModule = await import(/* @vite-ignore */ '/wasm/pax_terminal_wasm.js');
      await wasmModule.default();
      
      // Create WASM terminal instance
      const { PAXTerminal } = wasmModule;
      this.terminal = new PAXTerminal();
      
      console.log('WASM module loaded successfully');
      
    } catch (error) {
      console.warn('WASM module not available, using fallback:', error);
      
      // Use fallback implementation
      this.terminal = new FallbackTerminal();
    }

    this.isLoading = false;
    return this.terminal;
  }

  static isWASMAvailable(): boolean {
    return this.terminal instanceof FallbackTerminal === false;
  }

  static getTerminal(): WASMTerminal | null {
    return this.terminal;
  }
}

// Export singleton loader
export default WASMLoader;
