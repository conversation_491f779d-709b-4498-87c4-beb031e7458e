import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';

interface ProtocolMessage {
  protocolEventCode: '101.1' | '101.3' | '101.5' | '101.8';
  mti: string;
  transactionId: string;
  stripePaymentIntentId?: string;
  timestamp: string;
  de2_pan?: string;
  de3_processingCode?: string;
  de4_transactionAmount?: number;
  de11_stan?: string;
  de12_localTime?: string;
  de13_localDate?: string;
  de18_merchantCategoryCode?: string;
  de37_retrievalReferenceNumber?: string;
  de38_approvalCode?: string;
  de39_responseCode?: string;
  de41_cardAcceptorTerminalId?: string;
  de42_cardAcceptorIdCode?: string;
}

interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code?: string;
    details?: any;
  };
}

const app = express();
const PORT = process.env.PORT || 3002;

app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Middleware to log all requests
app.use((req: Request, _res: Response, next: NextFunction) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] MockBank: Received ${req.method} ${req.url}`);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log(`[${timestamp}] MockBank: Body:`, JSON.stringify(req.body, null, 2));
  }
  // Log headers if needed for debugging specific issues
  // console.log(`[${timestamp}] MockBank: Headers:`, JSON.stringify(req.headers, null, 2));
  next();
});

class MockBankService {
  private generateResponseCode(protocolCode: string, amount: number): string {
    if (amount <= 0) return '05';
    if (amount > 100000) return '61';

    switch (protocolCode) {
      case '101.1':
        return Math.random() > 0.1 ? '00' : '05';
      case '101.3':
        return Math.random() > 0.05 ? '00' : '05';
      case '101.5':
        return '00';
      case '101.8':
        return '00';
      default:
        return '05';
    }
  }

  private generateApprovalCode(): string {
    return Math.floor(Math.random() * 999999).toString().padStart(6, '0');
  }

  private getProtocolDescription(code: string): string {
    switch (code) {
      case '101.1': return 'Authorization Request';
      case '101.3': return 'Capture Request';
      case '101.5': return 'Reversal Request';
      case '101.8': return 'Settlement Request';
      default: return 'Unknown Protocol';
    }
  }

  private validateMessage(message: any): { isValid: boolean; error?: string } {
    if (!message) {
      return { isValid: false, error: 'Message body is required' };
    }

    if (!message.protocolEventCode) {
      return { isValid: false, error: 'Protocol event code is required' };
    }

    if (!['101.1', '101.3', '101.5', '101.8'].includes(message.protocolEventCode)) {
      return { isValid: false, error: 'Invalid protocol event code' };
    }

    if (!message.mti) {
      return { isValid: false, error: 'MTI is required' };
    }

    if (!message.transactionId) {
      return { isValid: false, error: 'Transaction ID is required' };
    }

    if (typeof message.de4_transactionAmount !== 'number' || message.de4_transactionAmount <= 0) {
      return { isValid: false, error: 'Valid transaction amount is required' };
    }

    return { isValid: true };
  }

  processMessage(message: ProtocolMessage): ApiResponse<any> {
    const timestamp = new Date().toISOString();

    console.log(`[${timestamp}] MockBank: Processing ${message.protocolEventCode} - ${this.getProtocolDescription(message.protocolEventCode)}`);
    console.log(`[${timestamp}] MockBank: Transaction ID: ${message.transactionId}, Amount: ${message.de4_transactionAmount}`);

    const validation = this.validateMessage(message);
    if (!validation.isValid) {
      console.warn(`[${timestamp}] MockBank: Validation failed - ${validation.error}`);
      return {
        success: false,
        message: 'Message validation failed',
        error: { code: 'VALIDATION_ERROR', details: validation.error },
      };
    }

    const responseCode = this.generateResponseCode(message.protocolEventCode, message.de4_transactionAmount || 0);
    const isSuccess = responseCode === '00';

    const bankResponseMessage = isSuccess
      ? `${this.getProtocolDescription(message.protocolEventCode)} approved`
      : `${this.getProtocolDescription(message.protocolEventCode)} declined`;

    const responseData = {
      bankResponseCode: responseCode,
      bankResponseMessage,
      approvalCode: isSuccess ? this.generateApprovalCode() : undefined,
      receivedMessage: message,
      processedAt: timestamp,
      protocolDescription: this.getProtocolDescription(message.protocolEventCode),
    };

    console.log(`[${timestamp}] MockBank: Response - ${responseCode} (${bankResponseMessage})`);

    return {
      success: true,
      message: 'Message processed successfully by Mock Bank',
      data: responseData,
    };
  }
}

const mockBank = new MockBankService();

app.get('/health', (_req: Request, res: Response) => {
  res.status(200).json({
    status: 'healthy',
    service: 'Mock Bank Service',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

app.post('/financial-message', (req: Request, res: Response) => {
  try {
    const message = req.body as ProtocolMessage;
    const response = mockBank.processMessage(message);

    const statusCode = response.success ? 200 : 400;
    res.status(statusCode).json(response);
  } catch (error) {
    console.error(`[${new Date().toISOString()}] MockBank: Unexpected error:`, error);
    const response: ApiResponse<null> = {
      success: false,
      message: 'An unexpected error occurred in Mock Bank Service',
      error: { code: 'INTERNAL_SERVER_ERROR', details: error instanceof Error ? error.message : 'Unknown error' },
    };
    res.status(500).json(response);
  }
});

// Catch-all for other routes
app.use((_req: Request, res: Response) => {
  const response: ApiResponse<null> = {
    success: false,
    message: 'Endpoint not found in Mock Bank Service.',
    error: { code: 'NOT_FOUND' },
  };
  res.status(404).json(response);
});

// Global error handler (very basic)
app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
  console.error(`[${new Date().toISOString()}] MockBank: Unhandled Error:`, err);
  const response: ApiResponse<null> = {
    success: false,
    message: 'An unexpected error occurred in Mock Bank Service.',
    error: { code: 'INTERNAL_SERVER_ERROR', details: err.message },
  };
  res.status(500).json(response);
});


app.listen(PORT, () => {
  console.log(`Mock Bank Service listening on port ${PORT}`);
  console.log(`Accepting POST requests on /financial-message`);
});
