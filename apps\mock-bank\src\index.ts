import express, { Request, Response, NextFunction } from 'express';
import bodyParser from 'body-parser';
import { SimulatedBankMessage, ApiResponse } from 'shared-types'; // Assuming shared-types is set up

const app = express();
const PORT = process.env.PORT || 3002; // Mock bank runs on a different port

app.use(bodyParser.json());

// Middleware to log all requests
app.use((req: Request, res: Response, next: NextFunction) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] MockBank: Received ${req.method} ${req.url}`);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log(`[${timestamp}] MockBank: Body:`, JSON.stringify(req.body, null, 2));
  }
  // Log headers if needed for debugging specific issues
  // console.log(`[${timestamp}] MockBank: Headers:`, JSON.stringify(req.headers, null, 2));
  next();
});

app.post('/financial-message', (req: Request, res: Response) => {
  const timestamp = new Date().toISOString();
  const message = req.body as SimulatedBankMessage;

  // Basic validation (can be more extensive if needed)
  if (!message || !message.mti || !message.protocolEventCode) {
    console.warn(`[${timestamp}] MockBank: Received invalid or incomplete message format.`);
    const response: ApiResponse<null> = {
      success: false,
      message: 'Invalid message format received by Mock Bank.',
      error: { code: 'INVALID_MESSAGE_FORMAT' },
    };
    return res.status(400).json(response);
  }

  console.log(`[${timestamp}] MockBank: "Processing" event ${message.protocolEventCode} (MTI: ${message.mti}) for transaction ${message.transactionId || 'N/A'}`);

  // Simulate a generic successful bank response
  // This can be made more sophisticated to return different responses based on message content
  let bankResponseCode = '00'; // '00' often means Approved
  let bankResponseMessage = 'Approved or Completed Successfully';

  // Example: Simulate a decline for a specific amount or card (very basic)
  if (message.de4_transactionAmount && message.de4_transactionAmount === 1111) {
    bankResponseCode = '05'; // '05' often means Do Not Honor
    bankResponseMessage = 'Do Not Honor - Simulated Decline';
    console.log(`[${timestamp}] MockBank: Simulating a decline (response code ${bankResponseCode}) for transaction ${message.transactionId}.`);
  } else if (message.mti === '0400') { // Reversal request
    bankResponseCode = '00';
    bankResponseMessage = 'Reversal Processed Successfully';
  }


  const response: ApiResponse<{ bankResponseCode: string; bankResponseMessage: string, receivedMessage: SimulatedBankMessage }> = {
    success: true,
    message: 'Message acknowledged by Mock Bank.',
    data: {
      bankResponseCode: bankResponseCode,
      bankResponseMessage: bankResponseMessage,
      receivedMessage: message, // Echo back the received message for easier debugging
    },
  };

  res.status(200).json(response);
});

// Catch-all for other routes
app.use((req: Request, res: Response) => {
  const response: ApiResponse<null> = {
    success: false,
    message: 'Endpoint not found in Mock Bank Service.',
    error: { code: 'NOT_FOUND' },
  };
  res.status(404).json(response);
});

// Global error handler (very basic)
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error(`[${new Date().toISOString()}] MockBank: Unhandled Error:`, err);
  const response: ApiResponse<null> = {
    success: false,
    message: 'An unexpected error occurred in Mock Bank Service.',
    error: { code: 'INTERNAL_SERVER_ERROR', details: err.message },
  };
  res.status(500).json(response);
});


app.listen(PORT, () => {
  console.log(`Mock Bank Service listening on port ${PORT}`);
  console.log(`Accepting POST requests on /financial-message`);
});
