{"name": "pax-pos-system-monorepo", "private": true, "scripts": {"build": "turbo run build", "build:all": "npm run build:shared && npm run build:backend && npm run build:frontend && npm run build:mock-bank", "build:shared": "npm run build --workspace=shared-types", "build:backend": "npm run build --workspace=backend", "build:frontend": "npm run build --workspace=frontend", "build:mock-bank": "npm run build --workspace=mock-bank", "dev": "turbo run dev --parallel", "dev:all": "concurrently --names \"BACKEND,FRONTEND,MOCK-BANK\" --prefix-colors \"blue,green,yellow\" \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:mock-bank\"", "dev:backend": "npm run dev --workspace=backend", "dev:frontend": "npm run dev --workspace=frontend", "dev:mock-bank": "npm run dev --workspace=mock-bank", "start": "concurrently --names \"BACKEND,FRONTEND,MOCK-BANK\" --prefix-colors \"blue,green,yellow\" \"npm run start:backend\" \"npm run start:frontend\" \"npm run start:mock-bank\"", "start:backend": "npm run start --workspace=backend", "start:frontend": "npm run start --workspace=frontend", "start:mock-bank": "npm run start --workspace=mock-bank", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "test": "turbo run test", "test:coverage": "turbo run test:coverage"}, "devDependencies": {"concurrently": "^8.2.2", "eslint": "^8.57.0", "prettier": "^3.2.5", "turbo": "^1.13.3", "typescript": "^5.4.5"}, "packageManager": "npm@10.5.0", "workspaces": ["apps/*", "packages/*"], "dependencies": {"lucide-react": "^0.525.0"}}