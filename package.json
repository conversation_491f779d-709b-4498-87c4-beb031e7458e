{
  "name": "pax-pos-system-monorepo",
  "private": true,
  "scripts": {
    "build": "turbo run build",
    "dev": "turbo run dev --parallel",
    "lint": "turbo run lint",
    "format": "prettier --write \"**/*.{ts,tsx,md}\"",
    "test": "turbo run test"
  },
  "devDependencies": {
    "turbo": "^1.13.3", // Use a recent version of Turborepo
    "typescript": "^5.4.5",
    "eslint": "^8.57.0",
    "prettier": "^3.2.5"
  },
  "packageManager": "npm@10.5.0", // Specify npm version or yarn/pnpm
  "workspaces": [
    "apps/*",
    "packages/*"
  ]
}
