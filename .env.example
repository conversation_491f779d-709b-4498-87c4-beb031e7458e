# Environment Configuration
NODE_ENV=production

# Database Configuration
MONGO_ROOT_PASSWORD=your_secure_mongo_password_here
MONGO_URI=*********************************************************************************************

# Stripe Configuration (Required)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# Security Configuration
JWT_SECRET=your_very_secure_jwt_secret_at_least_32_characters_long

# CORS Configuration
CORS_ORIGIN=http://localhost

# Rate Limiting Configuration
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# Logging Configuration
LOG_LEVEL=info

# Service URLs (for development)
MOCK_BANK_URL=http://localhost:3002/financial-message

# Port Configuration (optional, defaults provided)
BACKEND_PORT=3001
FRONTEND_PORT=80
MOCK_BANK_PORT=3002
