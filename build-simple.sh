#!/bin/bash

set -e

echo "🚀 Building PAX A920 Pro POS Terminal - Simple Build"
echo "===================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Install dependencies
print_status "Installing dependencies..."
npm install

# Install backend dependencies
cd apps/backend
npm install
cd ../..

# Install frontend dependencies
cd apps/frontend
npm install
cd ../..

# Install mock bank dependencies
cd apps/mock-bank
npm install
cd ../..

print_success "Dependencies installed"

# Try to build WASM module (optional)
print_status "Attempting to build WASM module..."

cd apps/wasm-terminal

# Check if Rust is available
if command -v rustc &> /dev/null; then
    print_status "Rust found, building WASM module..."
    
    # Check if wasm-pack is available
    if command -v wasm-pack &> /dev/null; then
        # Build WASM module
        wasm-pack build --target web --out-dir pkg --release --out-name pax_terminal_wasm
        
        # Check if build was successful
        if [ -f "pkg/pax_terminal_wasm.js" ]; then
            # Copy WASM files to frontend
            mkdir -p ../frontend/public/wasm
            mkdir -p ../frontend/src/types
            
            cp pkg/pax_terminal_wasm.js ../frontend/public/wasm/
            cp pkg/pax_terminal_wasm_bg.wasm ../frontend/public/wasm/
            cp pkg/pax_terminal_wasm.d.ts ../frontend/src/types/
            
            print_success "WASM module built and copied successfully"
        else
            print_warning "WASM build failed, but continuing with fallback mode"
        fi
    else
        print_warning "wasm-pack not found, skipping WASM build"
        print_warning "Install wasm-pack: curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh"
    fi
else
    print_warning "Rust not found, skipping WASM build"
    print_warning "Install Rust: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
fi

cd ../..

# Build backend
print_status "Building backend..."

cd apps/backend
npm run build
cd ../..

print_success "Backend built successfully"

# Build frontend
print_status "Building frontend..."

cd apps/frontend
npm run build
cd ../..

print_success "Frontend built successfully"

# Build mock bank
print_status "Building mock bank..."

cd apps/mock-bank
npm run build
cd ../..

print_success "Mock bank built successfully"

# Create simple production bundle
print_status "Creating production bundle..."

mkdir -p dist/simple

# Copy backend
cp -r apps/backend/dist dist/simple/backend
cp apps/backend/package.json dist/simple/backend/

# Copy frontend
cp -r apps/frontend/dist dist/simple/frontend

# Copy mock bank
cp -r apps/mock-bank/dist dist/simple/mock-bank
cp apps/mock-bank/package.json dist/simple/mock-bank/

# Copy WASM files if they exist
if [ -d "apps/frontend/public/wasm" ]; then
    mkdir -p dist/simple/frontend/wasm
    cp -r apps/frontend/public/wasm/* dist/simple/frontend/wasm/ 2>/dev/null || true
    print_success "WASM files included in bundle"
else
    print_warning "No WASM files found - frontend will use fallback mode"
fi

# Create simple startup script
cat > dist/simple/start.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting PAX A920 Pro POS Terminal"

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18 or later."
    exit 1
fi

# Install production dependencies
echo "📦 Installing production dependencies..."

cd backend && npm install --production --silent && cd ..
cd mock-bank && npm install --production --silent && cd ..

echo "✅ Dependencies installed"

# Start services
echo "🔄 Starting services..."

# Start backend
echo "Starting backend on port 3001..."
cd backend && npm start &
BACKEND_PID=$!
cd ..

# Start mock bank
echo "Starting mock bank on port 3002..."
cd mock-bank && npm start &
MOCK_BANK_PID=$!
cd ..

# Start frontend server
echo "Starting frontend on port 3000..."
cd frontend

# Try different methods to serve static files
if command -v python3 &> /dev/null; then
    python3 -m http.server 3000 &
    FRONTEND_PID=$!
elif command -v python &> /dev/null; then
    python -m http.server 3000 &
    FRONTEND_PID=$!
elif command -v npx &> /dev/null; then
    npx serve -s . -l 3000 &
    FRONTEND_PID=$!
else
    echo "❌ No static file server available. Please install Python or Node.js serve package."
    kill $BACKEND_PID $MOCK_BANK_PID
    exit 1
fi

cd ..

echo ""
echo "🎉 All services started successfully!"
echo "=================================="
echo "Frontend:  http://localhost:3000"
echo "Backend:   http://localhost:3001"
echo "Mock Bank: http://localhost:3002"
echo ""
echo "Process IDs:"
echo "Backend:   $BACKEND_PID"
echo "Mock Bank: $MOCK_BANK_PID"
echo "Frontend:  $FRONTEND_PID"
echo ""
echo "To stop all services:"
echo "kill $BACKEND_PID $MOCK_BANK_PID $FRONTEND_PID"
echo ""
echo "Press Ctrl+C to stop all services"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping all services..."
    kill $BACKEND_PID $MOCK_BANK_PID $FRONTEND_PID 2>/dev/null
    echo "✅ All services stopped"
    exit 0
}

# Set trap to cleanup on exit
trap cleanup SIGINT SIGTERM

# Wait for any process to exit
wait
EOF

chmod +x dist/simple/start.sh

# Create environment template
cat > dist/simple/.env.example << 'EOF'
# PAX A920 Pro POS Terminal Configuration
NODE_ENV=production

# Database
MONGO_URI=mongodb://localhost:27017/pax_pos

# Stripe Configuration (REPLACE WITH YOUR KEYS)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Security
JWT_SECRET=your_super_secure_jwt_secret_at_least_32_characters
CORS_ORIGIN=http://localhost:3000

# Terminal Configuration
TERMINAL_ID=PAX_A920_001
MERCHANT_ID=your_merchant_id
DEFAULT_CURRENCY=usd
PAYMENT_TIMEOUT=30000
MIN_AMOUNT=50
MAX_AMOUNT=********

# Features
CARD_READER_ENABLED=true
PRINTER_ENABLED=true
AUDIO_ENABLED=true
VIBRATION_ENABLED=true
MANUAL_ENTRY_ENABLED=true

# Receipt
RECEIPT_ENABLED=true
RECEIPT_COPIES=2
RECEIPT_SIGNATURE=true
RECEIPT_FOOTER=Thank you for your business!

# UI
UI_THEME=auto
UI_LANGUAGE=en
UI_FONT_SIZE=medium
UI_ORIENTATION=portrait

# Logging
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000
EOF

print_success "Production bundle created in dist/simple/"

echo ""
echo "🎉 Simple build completed successfully!"
echo "======================================"
echo ""
echo "📦 Build Output: dist/simple/"
echo ""
echo "🚀 To start the application:"
echo "   cd dist/simple"
echo "   ./start.sh"
echo ""
echo "🔧 Configuration:"
echo "   1. Copy .env.example to .env"
echo "   2. Configure your Stripe keys"
echo "   3. Set up MongoDB connection"
echo ""
echo "📱 For APK creation:"
echo "   bash create-simple-apk.sh"
echo ""
echo "⚠️  Notes:"
if [ ! -d "apps/frontend/public/wasm" ]; then
    echo "   - WASM module not built (Rust/wasm-pack not available)"
    echo "   - Frontend will use fallback mode for card simulation"
    echo "   - Install Rust and wasm-pack for hardware integration"
else
    echo "   - WASM module included for hardware integration"
    echo "   - Frontend supports both real hardware and fallback mode"
fi
echo ""
print_success "Ready for deployment! 🚀"
