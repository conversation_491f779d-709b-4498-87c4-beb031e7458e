version: '3.8'

services:
  # MongoDB for development
  mongodb:
    image: mongo:7.0
    container_name: pax-pos-mongodb-dev
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
      MONGO_INITDB_DATABASE: pax_pos_system_dev
    ports:
      - "27017:27017"
    volumes:
      - mongodb_dev_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - pax-pos-dev-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for development (optional, for caching)
  redis:
    image: redis:7-alpine
    container_name: pax-pos-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - pax-pos-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Development database admin interface
  mongo-express:
    image: mongo-express:latest
    container_name: pax-pos-mongo-express-dev
    restart: unless-stopped
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: admin123
      ME_CONFIG_MONGODB_URL: **************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    ports:
      - "8081:8081"
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - pax-pos-dev-network

volumes:
  mongodb_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  pax-pos-dev-network:
    driver: bridge
