/**
 * Enhanced Payment Service
 * 
 * Integrates PAX terminal payments with Stripe and webhook processing
 * Handles the complete payment lifecycle from card reading to confirmation
 */

import Stripe from 'stripe';
import { SystemLog } from '../models/SystemLog';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-04-10',
});

export interface PaymentRequest {
  amount: number;
  currency?: string;
  description?: string;
  metadata?: Record<string, string>;
  terminalId: string;
  merchantName: string;
  cardData?: {
    encryptedPan?: string;
    maskedPan?: string;
    expiryDate?: string;
    cardholderName?: string;
    cardBrand?: string;
    cardType?: string;
    emvData?: any;
    ksn?: string;
  };
}

export interface PaymentResult {
  success: boolean;
  paymentIntent?: Stripe.PaymentIntent;
  paymentMethod?: Stripe.PaymentMethod;
  error?: string;
  transactionId?: string;
  receiptData?: ReceiptData;
}

export interface ReceiptData {
  transactionId: string;
  amount: number;
  currency: string;
  cardBrand: string;
  cardLast4: string;
  cardType: string;
  timestamp: string;
  merchantName: string;
  terminalId: string;
  approvalCode?: string;
  authCode?: string;
  rrn?: string;
  status: 'approved' | 'declined' | 'pending';
}

export class EnhancedPaymentService {
  
  /**
   * Process payment with encrypted card data from PAX terminal
   */
  async processTerminalPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      // Log payment initiation
      await this.logPaymentEvent('payment_initiated', {
        amount: request.amount,
        currency: request.currency || 'usd',
        terminalId: request.terminalId,
        merchantName: request.merchantName
      });

      // Step 1: Create payment method from encrypted card data
      const paymentMethod = await this.createPaymentMethodFromEncryptedData(request);
      
      if (!paymentMethod) {
        throw new Error('Failed to create payment method from card data');
      }

      // Step 2: Create payment intent
      const paymentIntent = await this.createPaymentIntent(request, paymentMethod.id);

      // Step 3: Generate receipt data
      const receiptData = this.generateReceiptData(paymentIntent, request, paymentMethod);

      // Log successful payment creation
      await this.logPaymentEvent('payment_created', {
        paymentIntentId: paymentIntent.id,
        paymentMethodId: paymentMethod.id,
        amount: request.amount,
        terminalId: request.terminalId,
        status: paymentIntent.status
      });

      return {
        success: true,
        paymentIntent,
        paymentMethod,
        transactionId: paymentIntent.id,
        receiptData
      };

    } catch (error) {
      await this.logPaymentEvent('payment_error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        terminalId: request.terminalId,
        amount: request.amount
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment processing failed'
      };
    }
  }

  /**
   * Create payment method from encrypted card data
   */
  private async createPaymentMethodFromEncryptedData(request: PaymentRequest): Promise<Stripe.PaymentMethod | null> {
    try {
      if (!request.cardData?.encryptedPan) {
        throw new Error('Encrypted card data is required');
      }

      // Decrypt card data (implement your decryption logic)
      const decryptedData = await this.decryptCardData(
        request.cardData.encryptedPan,
        request.cardData.ksn
      );

      if (!decryptedData.success) {
        throw new Error('Failed to decrypt card data');
      }

      // Create Stripe payment method
      const paymentMethod = await stripe.paymentMethods.create({
        type: 'card',
        card: {
          number: decryptedData.pan,
          exp_month: parseInt((decryptedData.expiryDate || '1225').substring(0, 2)),
          exp_year: parseInt('20' + (decryptedData.expiryDate || '1225').substring(2, 4)),
          cvc: decryptedData.cvc || '000'
        },
        billing_details: {
          name: request.cardData.cardholderName || 'Cardholder'
        },
        metadata: {
          source: 'pax_terminal',
          terminal_id: request.terminalId,
          card_type: request.cardData.cardType || 'unknown',
          card_brand: request.cardData.cardBrand || 'unknown',
          encrypted_source: 'true'
        }
      });

      return paymentMethod;

    } catch (error) {
      console.error('Failed to create payment method:', error);
      return null;
    }
  }

  /**
   * Create payment intent
   */
  private async createPaymentIntent(
    request: PaymentRequest, 
    paymentMethodId: string
  ): Promise<Stripe.PaymentIntent> {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: request.amount,
      currency: request.currency || 'usd',
      description: request.description || `PAX Terminal Payment - ${request.terminalId}`,
      payment_method: paymentMethodId,
      confirmation_method: 'manual',
      confirm: true,
      metadata: {
        terminal_id: request.terminalId,
        merchant_name: request.merchantName,
        source: 'pax_terminal',
        card_type: request.cardData?.cardType || 'unknown',
        card_brand: request.cardData?.cardBrand || 'unknown',
        emv_aid: request.cardData?.emvData?.aid || '',
        ...request.metadata
      }
    });

    return paymentIntent;
  }

  /**
   * Generate receipt data
   */
  private generateReceiptData(
    paymentIntent: Stripe.PaymentIntent,
    request: PaymentRequest,
    paymentMethod: Stripe.PaymentMethod
  ): ReceiptData {
    const charges = (paymentIntent as any).charges?.data || [];
    const charge = charges[0];
    
    return {
      transactionId: paymentIntent.id,
      amount: request.amount / 100, // Convert from cents
      currency: (request.currency || 'usd').toUpperCase(),
      cardBrand: paymentMethod.card?.brand || request.cardData?.cardBrand || 'unknown',
      cardLast4: paymentMethod.card?.last4 || request.cardData?.maskedPan?.slice(-4) || '****',
      cardType: request.cardData?.cardType || 'unknown',
      timestamp: new Date().toISOString(),
      merchantName: request.merchantName,
      terminalId: request.terminalId,
      approvalCode: charge?.outcome?.seller_message,
      authCode: charge?.authorization_code,
      rrn: charge?.balance_transaction as string,
      status: paymentIntent.status === 'succeeded' ? 'approved' : 'pending'
    };
  }

  /**
   * Decrypt card data (placeholder - implement actual decryption)
   */
  private async decryptCardData(
    _encryptedPan: string,
    _ksn?: string
  ): Promise<{
    success: boolean;
    pan?: string;
    expiryDate?: string;
    cvc?: string;
    error?: string;
  }> {
    try {
      // IMPORTANT: Implement actual decryption here
      // This is a placeholder for demonstration
      
      // For PAX terminals, you would typically use:
      // - DUKPT (Derived Unique Key Per Transaction) decryption
      // - Your encryption keys from PAX
      // - Proper cryptographic libraries
      
      // Placeholder decryption (DO NOT USE IN PRODUCTION)
      console.log('Decrypting card data with KSN:', _ksn);
      
      // Simulate decryption
      return {
        success: true,
        pan: '****************', // This should come from actual decryption
        expiryDate: '1225',
        cvc: undefined // CVC typically not available from card readers
      };

    } catch (error) {
      console.error('Decryption failed:', error);
      return {
        success: false,
        error: 'Decryption failed'
      };
    }
  }

  /**
   * Handle webhook confirmation of payment
   */
  async handleWebhookConfirmation(
    paymentIntentId: string,
    status: 'succeeded' | 'failed' | 'canceled'
  ): Promise<void> {
    try {
      await this.logPaymentEvent('webhook_confirmation', {
        paymentIntentId,
        status,
        confirmedAt: new Date().toISOString()
      });

      // Here you could:
      // 1. Update your database with final payment status
      // 2. Send confirmation notifications
      // 3. Update inventory
      // 4. Trigger receipt printing
      // 5. Update analytics

      console.log(`Payment ${paymentIntentId} confirmed with status: ${status}`);

    } catch (error) {
      console.error('Failed to handle webhook confirmation:', error);
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(paymentIntentId: string): Promise<{
    status: string;
    amount?: number;
    currency?: string;
    created?: number;
    metadata?: Stripe.Metadata;
  }> {
    try {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      
      return {
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        created: paymentIntent.created,
        metadata: paymentIntent.metadata
      };

    } catch (error) {
      console.error('Failed to get payment status:', error);
      throw error;
    }
  }

  /**
   * Cancel payment
   */
  async cancelPayment(paymentIntentId: string): Promise<boolean> {
    try {
      const paymentIntent = await stripe.paymentIntents.cancel(paymentIntentId);
      
      await this.logPaymentEvent('payment_canceled', {
        paymentIntentId,
        canceledAt: new Date().toISOString(),
        status: paymentIntent.status
      });

      return paymentIntent.status === 'canceled';

    } catch (error) {
      console.error('Failed to cancel payment:', error);
      return false;
    }
  }

  /**
   * Log payment events
   */
  private async logPaymentEvent(eventType: string, data: any): Promise<void> {
    try {
      const systemLog = new SystemLog({
        level: eventType.includes('error') || eventType.includes('failed') ? 'error' : 'info',
        category: 'payment',
        message: `Payment event: ${eventType}`,
        details: {
          eventType,
          ...data,
          timestamp: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      });

      await systemLog.save();
    } catch (error) {
      console.error('Failed to log payment event:', error);
    }
  }
}

// Export singleton instance
export const enhancedPaymentService = new EnhancedPaymentService();
