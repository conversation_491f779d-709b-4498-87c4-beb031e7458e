import mongoose, { Schema, Document } from 'mongoose';

export interface ITransaction extends Document {
  amount: number;
  status: 'success' | 'failure';
  protocolCode?: string;
  stripePaymentIntentId?: string;
  receiptUrl?: string;
  createdAt: string;
  updatedAt: string;
}

const TransactionSchema: Schema = new Schema({
  amount: { type: Number, required: true },
  status: { type: String, enum: ['success', 'failure'], required: true },
  protocolCode: String,
  stripePaymentIntentId: String,
  receiptUrl: String,
  createdAt: { type: String, required: true },
  updatedAt: { type: String, required: true },
});

export default mongoose.model<ITransaction>('Transaction', TransactionSchema);
