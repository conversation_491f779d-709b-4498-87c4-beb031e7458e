import mongoose, { Schema, Document } from 'mongoose';

export interface ITransaction extends Document {
  amount: number;
  status: 'success' | 'failure' | 'pending' | 'failed' | 'refunded' | 'cancelled';
  protocolCode?: string;
  stripePaymentIntentId?: string;
  receiptUrl?: string;
  metadata?: Record<string, any>;
  currency?: string;
  paymentMethod?: string;
  createdAt: string;
  updatedAt: string;
}

const TransactionSchema: Schema = new Schema({
  amount: {
    type: Number,
    required: true,
    min: [0.01, 'Amount must be greater than 0']
  },
  status: {
    type: String,
    enum: {
      values: ['success', 'failure', 'pending', 'failed', 'refunded', 'cancelled'],
      message: 'Status must be success, failure, pending, failed, refunded, or cancelled'
    },
    required: true,
    default: 'pending'
  },
  currency: {
    type: String,
    default: 'usd',
    match: [/^[a-z]{3}$/, 'Currency must be a 3-letter lowercase code']
  },
  paymentMethod: {
    type: String,
    enum: ['card', 'cash', 'other'],
    default: 'card'
  },
  protocolCode: {
    type: String,
    match: [/^101\.[1358]$/, 'Protocol code must be 101.1, 101.3, 101.5, or 101.8']
  },
  stripePaymentIntentId: {
    type: String,
    match: [/^pi_[a-zA-Z0-9_]+$/, 'Invalid Stripe payment intent ID format']
  },
  receiptUrl: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/.+/.test(v);
      },
      message: 'Receipt URL must be a valid HTTP/HTTPS URL'
    }
  },
  metadata: {
    type: Schema.Types.Mixed,
    default: {}
  },
  createdAt: {
    type: String,
    required: true,
    default: () => new Date().toISOString()
  },
  updatedAt: {
    type: String,
    required: true,
    default: () => new Date().toISOString()
  },
}, {
  timestamps: false,
  versionKey: false,
});

TransactionSchema.index({ createdAt: -1 });
TransactionSchema.index({ status: 1 });
TransactionSchema.index({ stripePaymentIntentId: 1 }, { sparse: true });

TransactionSchema.pre('save', function(next) {
  this.updatedAt = new Date().toISOString();
  next();
});

export default mongoose.model<ITransaction>('Transaction', TransactionSchema);
