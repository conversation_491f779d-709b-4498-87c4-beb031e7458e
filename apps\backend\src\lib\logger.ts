import pino from 'pino';
import { env } from '../config/env';

const pinoOptions: pino.LoggerOptions = {
  level: env.LOG_LEVEL || 'info',
};

// Use pino-pretty for local development for more readable logs
if (env.NODE_ENV !== 'production' && env.NODE_ENV !== 'test') {
  pinoOptions.transport = {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard', // More readable timestamp
      ignore: 'pid,hostname', // Exclude pid and hostname from pretty print
    },
  };
}

export const logger = pino(pinoOptions);

// Example of creating a child logger for a specific module/context
// export const transactionLogger = logger.child({ module: 'transactions' });

// Make sure to export the main logger instance
export default logger;
