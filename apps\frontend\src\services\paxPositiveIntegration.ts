/**
 * PAX POSitive Integration Service
 * 
 * This service handles integration with PAX POSitive payment application
 * using IPC (Inter-Process Communication) via BroadcastReceiver pattern.
 * 
 * Based on PAX POSitive documentation and demo app patterns.
 */

export interface POSitiveTransactionRequest {
  transactionType: 'SALE' | 'REFUND' | 'REVERSAL' | 'QUERY';
  amount: number; // in minor units (cents)
  cashbackAmount?: number;
  gratuityAmount?: number;
  receiptNumber?: string;
  disablePrinting?: boolean;
  cancelTimeout?: number;
}

export interface POSitiveTransactionResult {
  success: boolean;
  transResponse?: boolean;
  transDetails?: boolean;
  cardType?: 'EMV' | 'MSR' | 'CTLS' | 'MANUAL';
  
  // Standard Response Details (List One)
  uti?: string; // Unique Transaction Identifier
  amountTrans?: number;
  amountGratuity?: number;
  amountCashback?: number;
  transApproved?: boolean;
  transCancelled?: boolean;
  cvmSigRequired?: boolean;
  cvmPinVerified?: boolean;
  transCurrencyCode?: string;
  terminalId?: string;
  merchantId?: string;
  softwareVersion?: string;
  
  // Transaction Details (List Two)
  retrievalReferenceNumber?: string;
  responseCode?: string;
  stan?: string;
  authorisationCode?: string;
  merchantTokenId?: string;
  cardPan?: string;
  cardExpiryDate?: string;
  cardStartDate?: string;
  cardScheme?: string;
  cardPanSequenceNumber?: string;
  
  // EMV Details (List Three)
  emvAid?: string;
  emvTsi?: string;
  emvCardholderName?: string;
  emvCryptogram?: string;
  emvCryptogramType?: string;
  
  // Error Details (List Four)
  error?: string;
  errorText?: string;
}

export interface POSitiveStatusEvent {
  event: string;
  timestamp: string;
}

// Declare global interfaces for POSitive integration
declare global {
  interface Window {
    // POSitive Android interface
    POSitive?: {
      executeTransaction(
        transactionType: string,
        args: Record<string, string>,
        callback: string
      ): void;
      
      executeReversal(
        args: Record<string, string>,
        callback: string
      ): void;
      
      executeReport(
        reportType: string,
        args: Record<string, string>,
        callback: string
      ): void;
      
      queryTransaction(
        args: Record<string, string>,
        callback: string
      ): void;
    };
    
    // Global callback functions
    positiveTransactionCallback?: (result: string) => void;
    positiveStatusCallback?: (status: string) => void;
    positiveErrorCallback?: (error: string) => void;
  }
}

export class PAXPositiveIntegration {
  private isInitialized = false;
  private currentTransactionId: string | null = null;
  private statusEventListeners: ((event: POSitiveStatusEvent) => void)[] = [];

  constructor() {
    this.setupGlobalCallbacks();
  }

  /**
   * Initialize POSitive integration
   */
  async initialize(): Promise<boolean> {
    try {
      // Check if POSitive is available
      if (window.POSitive) {
        this.isInitialized = true;
        console.log('POSitive integration initialized successfully');
        return true;
      } else {
        console.warn('POSitive not available - running in simulation mode');
        return false;
      }
    } catch (error) {
      console.error('Failed to initialize POSitive integration:', error);
      return false;
    }
  }

  /**
   * Setup global callback functions for POSitive communication
   */
  private setupGlobalCallbacks(): void {
    // Transaction result callback
    window.positiveTransactionCallback = (resultStr: string) => {
      try {
        const result = JSON.parse(resultStr);
        this.handleTransactionResult(result);
      } catch (error) {
        console.error('Failed to parse transaction result:', error);
      }
    };

    // Status event callback
    window.positiveStatusCallback = (statusStr: string) => {
      try {
        const status = JSON.parse(statusStr);
        this.handleStatusEvent(status);
      } catch (error) {
        console.error('Failed to parse status event:', error);
      }
    };

    // Error callback
    window.positiveErrorCallback = (errorStr: string) => {
      console.error('POSitive error:', errorStr);
    };
  }

  /**
   * Execute a sale transaction
   */
  async executeSale(
    amount: number,
    options: Partial<POSitiveTransactionRequest> = {}
  ): Promise<POSitiveTransactionResult> {
    return this.executeTransaction({
      transactionType: 'SALE',
      amount,
      ...options
    });
  }

  /**
   * Execute a refund transaction
   */
  async executeRefund(
    amount: number,
    options: Partial<POSitiveTransactionRequest> = {}
  ): Promise<POSitiveTransactionResult> {
    return this.executeTransaction({
      transactionType: 'REFUND',
      amount,
      ...options
    });
  }

  /**
   * Execute a reversal transaction
   */
  async executeReversal(
    receiptNumber: string = '0',
    options: Partial<POSitiveTransactionRequest> = {}
  ): Promise<POSitiveTransactionResult> {
    if (!this.isInitialized || !window.POSitive) {
      return this.simulateTransaction('REVERSAL', 0);
    }

    return new Promise((resolve) => {
      this.currentTransactionId = `REV_${Date.now()}`;
      
      const args = {
        CT_RECEIPT_NUMBER: receiptNumber,
        CT_DISABLE_PRINTING: options.disablePrinting ? 'TRUE' : 'FALSE'
      };

      // Store resolver for callback
      (window as any)[`resolve_${this.currentTransactionId}`] = resolve;

      window.POSitive!.executeReversal(args, `resolve_${this.currentTransactionId}`);
    });
  }

  /**
   * Execute a transaction
   */
  private async executeTransaction(
    request: POSitiveTransactionRequest
  ): Promise<POSitiveTransactionResult> {
    if (!this.isInitialized || !window.POSitive) {
      return this.simulateTransaction(request.transactionType, request.amount);
    }

    return new Promise((resolve) => {
      this.currentTransactionId = `TXN_${Date.now()}`;
      
      const args: Record<string, string> = {
        CT_AMOUNT: request.amount.toString()
      };

      if (request.cashbackAmount) {
        args.CT_CASHBACK_AMOUNT = request.cashbackAmount.toString();
      }

      if (request.gratuityAmount) {
        args.CT_GRATUITY_AMOUNT = request.gratuityAmount.toString();
      }

      if (request.disablePrinting) {
        args.CT_DISABLE_PRINTING = 'TRUE';
      }

      if (request.cancelTimeout) {
        args.CT_CANCELLED_TIMEOUT = request.cancelTimeout.toString();
      }

      // Store resolver for callback
      (window as any)[`resolve_${this.currentTransactionId}`] = resolve;

      window.POSitive!.executeTransaction(
        `TRANSACTION_TYPE_${request.transactionType}`,
        args,
        `resolve_${this.currentTransactionId}`
      );
    });
  }

  /**
   * Handle transaction result from POSitive
   */
  private handleTransactionResult(result: any): void {
    console.log('POSitive transaction result:', result);
    
    // Find and call the appropriate resolver
    if (this.currentTransactionId) {
      const resolver = (window as any)[`resolve_${this.currentTransactionId}`];
      if (resolver) {
        resolver(this.parseTransactionResult(result));
        delete (window as any)[`resolve_${this.currentTransactionId}`];
      }
    }
  }

  /**
   * Parse POSitive transaction result
   */
  private parseTransactionResult(result: any): POSitiveTransactionResult {
    return {
      success: result.transApproved || false,
      transResponse: result.transResponse,
      transDetails: result.transDetails,
      cardType: result.cardType,
      
      // Standard Response Details
      uti: result.uti,
      amountTrans: result.amountTrans,
      amountGratuity: result.amountGratuity,
      amountCashback: result.amountCashback,
      transApproved: result.transApproved,
      transCancelled: result.transCancelled,
      cvmSigRequired: result.cvmSigRequired,
      cvmPinVerified: result.cvmPinVerified,
      transCurrencyCode: result.transCurrencyCode,
      terminalId: result.terminalId,
      merchantId: result.merchantId,
      softwareVersion: result.softwareVersion,
      
      // Transaction Details
      retrievalReferenceNumber: result.retrievalReferenceNumber,
      responseCode: result.responseCode,
      stan: result.stan,
      authorisationCode: result.authorisationCode,
      merchantTokenId: result.merchantTokenId,
      cardPan: result.cardPan,
      cardExpiryDate: result.cardExpiryDate,
      cardStartDate: result.cardStartDate,
      cardScheme: result.cardScheme,
      cardPanSequenceNumber: result.cardPanSequenceNumber,
      
      // EMV Details
      emvAid: result.emvAid,
      emvTsi: result.emvTsi,
      emvCardholderName: result.emvCardholderName,
      emvCryptogram: result.emvCryptogram,
      emvCryptogramType: result.emvCryptogramType,
      
      // Error Details
      error: result.error,
      errorText: result.errorText
    };
  }

  /**
   * Handle status events from POSitive
   */
  private handleStatusEvent(status: any): void {
    const event: POSitiveStatusEvent = {
      event: status.StatusEvent || status.event,
      timestamp: new Date().toISOString()
    };

    console.log('POSitive status event:', event);

    // Notify all listeners
    this.statusEventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in status event listener:', error);
      }
    });
  }

  /**
   * Add status event listener
   */
  addStatusEventListener(listener: (event: POSitiveStatusEvent) => void): void {
    this.statusEventListeners.push(listener);
  }

  /**
   * Remove status event listener
   */
  removeStatusEventListener(listener: (event: POSitiveStatusEvent) => void): void {
    const index = this.statusEventListeners.indexOf(listener);
    if (index > -1) {
      this.statusEventListeners.splice(index, 1);
    }
  }

  /**
   * Simulate transaction for development/testing
   */
  private simulateTransaction(
    _type: string,
    amount: number
  ): POSitiveTransactionResult {
    const isApproved = Math.random() > 0.1; // 90% approval rate

    return {
      success: isApproved,
      transResponse: true,
      transDetails: true,
      cardType: 'EMV',
      uti: `SIM_${Date.now()}`,
      amountTrans: amount,
      transApproved: isApproved,
      transCancelled: false,
      cvmSigRequired: false,
      cvmPinVerified: true,
      transCurrencyCode: 'USD',
      terminalId: 'SIM_TERMINAL',
      merchantId: 'SIM_MERCHANT',
      retrievalReferenceNumber: Math.random().toString(36).substring(7),
      responseCode: isApproved ? '00' : '05',
      stan: Math.floor(Math.random() * 999999).toString().padStart(6, '0'),
      authorisationCode: isApproved ? Math.random().toString(36).substring(2, 8).toUpperCase() : undefined,
      cardPan: '4111********1111',
      cardExpiryDate: '2512',
      cardScheme: 'VISA'
    };
  }

  /**
   * Check if POSitive is available
   */
  isAvailable(): boolean {
    return this.isInitialized && !!window.POSitive;
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.statusEventListeners = [];
    this.currentTransactionId = null;
    
    // Clean up global callbacks
    delete window.positiveTransactionCallback;
    delete window.positiveStatusCallback;
    delete window.positiveErrorCallback;
  }
}
