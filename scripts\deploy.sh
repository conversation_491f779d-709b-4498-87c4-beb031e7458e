#!/bin/bash

# PAX POS System Deployment Script
# This script builds and deploys the entire PAX POS system using Docker

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to check environment variables
check_environment() {
    print_status "Checking environment configuration..."
    
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from template..."
        cp .env.example .env
        print_warning "Please edit .env file with your actual configuration before running again."
        exit 1
    fi
    
    # Source the .env file
    source .env
    
    # Check required variables
    required_vars=("STRIPE_SECRET_KEY" "STRIPE_PUBLISHABLE_KEY" "STRIPE_WEBHOOK_SECRET" "JWT_SECRET")
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ] || [[ "${!var}" == *"your_"* ]]; then
            print_error "Required environment variable $var is not set or contains placeholder value"
            print_error "Please update your .env file with actual values"
            exit 1
        fi
    done
    
    print_success "Environment configuration check passed"
}

# Function to build and start services
deploy_services() {
    print_status "Building and starting PAX POS services..."
    
    # Stop any existing containers
    print_status "Stopping existing containers..."
    docker-compose down --remove-orphans
    
    # Build and start services
    print_status "Building Docker images..."
    docker-compose build --no-cache
    
    print_status "Starting services..."
    docker-compose up -d
    
    print_success "Services started successfully"
}

# Function to wait for services to be healthy
wait_for_services() {
    print_status "Waiting for services to be healthy..."
    
    services=("mongodb" "mock-bank" "backend" "frontend")
    max_attempts=30
    
    for service in "${services[@]}"; do
        print_status "Waiting for $service to be healthy..."
        attempt=1
        
        while [ $attempt -le $max_attempts ]; do
            if docker-compose ps $service | grep -q "healthy"; then
                print_success "$service is healthy"
                break
            elif [ $attempt -eq $max_attempts ]; then
                print_error "$service failed to become healthy after $max_attempts attempts"
                docker-compose logs $service
                exit 1
            else
                print_status "Attempt $attempt/$max_attempts - waiting for $service..."
                sleep 10
                ((attempt++))
            fi
        done
    done
    
    print_success "All services are healthy"
}

# Function to display service status
show_status() {
    print_status "Service Status:"
    docker-compose ps
    
    echo ""
    print_status "Service URLs:"
    echo "Frontend: http://localhost"
    echo "Backend API: http://localhost:3001"
    echo "Mock Bank: http://localhost:3002"
    echo "MongoDB: localhost:27017"
    
    echo ""
    print_status "Health Check URLs:"
    echo "Frontend Health: http://localhost/health"
    echo "Backend Health: http://localhost:3001/health"
    echo "Mock Bank Health: http://localhost:3002/health"
}

# Function to show logs
show_logs() {
    print_status "Recent logs from all services:"
    docker-compose logs --tail=50
}

# Main deployment function
main() {
    print_status "Starting PAX POS System deployment..."
    
    check_prerequisites
    check_environment
    deploy_services
    wait_for_services
    show_status
    
    print_success "PAX POS System deployed successfully!"
    print_status "You can now access the application at http://localhost"
    
    # Ask if user wants to see logs
    read -p "Would you like to see the recent logs? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        show_logs
    fi
}

# Handle script arguments
case "${1:-}" in
    "logs")
        docker-compose logs -f
        ;;
    "status")
        show_status
        ;;
    "stop")
        print_status "Stopping PAX POS services..."
        docker-compose down
        print_success "Services stopped"
        ;;
    "restart")
        print_status "Restarting PAX POS services..."
        docker-compose restart
        print_success "Services restarted"
        ;;
    "clean")
        print_status "Cleaning up PAX POS deployment..."
        docker-compose down --volumes --remove-orphans
        docker system prune -f
        print_success "Cleanup completed"
        ;;
    *)
        main
        ;;
esac
