import { FastifyRequest, FastifyReply } from 'fastify';
import ProtocolMessage from '../models/ProtocolMessage.mongo';

export const createProtocolMessage = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  const { protocol, mti, amount, pan, stan, authCode, origStan, timestamp } = request.body as any;

  if (!protocol || !mti || !amount || !stan || !timestamp) {
    return reply.status(400).send({
      status: false,
      message: 'INVALID_INPUT',
      meta: {
        error: 'Missing required protocol message fields.',
        suggestions: ['Provide all required fields and try again.'],
      },
    });
  }

  try {
    const message = new ProtocolMessage({ protocol, mti, amount, pan, stan, authCode, origStan, timestamp });
    await message.save();
    return reply.status(201).send({ status: true, message: 'Protocol message created', data: message });
  } catch (err: any) {
    return reply.status(500).send({
      status: false,
      message: 'DB_ERROR',
      meta: { error: err.message },
    });
  }
};
