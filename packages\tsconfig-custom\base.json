{
  "$schema": "https://json.schemastore.org/tsconfig",
  "display": "Default Base",
  "compilerOptions": {
    "composite": false, // Typically true for buildable packages, false for base configs not directly built
    "declaration": true,
    "declarationMap": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "inlineSources": false,
    "isolatedModules": true, // Important for transpilers like Babel/SWC, Vite uses esbuild
    "moduleResolution": "node",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "preserveWatchOutput": true,
    "skipLibCheck": true,
    "strict": true,
    "resolveJsonModule": true
  },
  "exclude": ["node_modules", "dist", ".turbo"]
}
