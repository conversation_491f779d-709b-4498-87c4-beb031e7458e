{"$schema": "https://json.schemastore.org/tsconfig", "display": "Default Base", "compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "node", "noUnusedLocals": true, "noUnusedParameters": true, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "resolveJsonModule": true}, "exclude": ["node_modules", "dist", ".turbo"]}