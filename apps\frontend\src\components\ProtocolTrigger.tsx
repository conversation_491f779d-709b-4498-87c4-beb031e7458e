import React, { useState } from 'react';
import { useAppStore } from '../store/useAppStore';

const protocolOptions = [
  { code: '101.1', label: 'Authorize' },
  { code: '101.3', label: 'Capture' },
  { code: '101.5', label: 'Refund' },
  { code: '101.8', label: 'Reversal' },
];

export const ProtocolTrigger: React.FC = () => {
  const [protocol, setProtocol] = useState('101.1');
  const [transactionId, setTransactionId] = useState('');
  const [amount, setAmount] = useState(1000);

  const { triggerProtocol, isLoading, error, transactions } = useAppStore();

  const handleTrigger = async () => {
    if (!transactionId.trim()) {
      alert('Please enter a transaction ID');
      return;
    }

    await triggerProtocol(protocol, transactionId, amount);
  };

  const handleUseLatestTransaction = () => {
    if (transactions.length > 0) {
      const latest = transactions[0];
      setTransactionId(latest._id);
      setAmount(latest.amount);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">Protocol Trigger</h2>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Protocol Code
          </label>
          <select
            value={protocol}
            onChange={e => setProtocol(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {protocolOptions.map(opt => (
              <option key={opt.code} value={opt.code}>{opt.label}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Transaction ID
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              value={transactionId}
              onChange={e => setTransactionId(e.target.value)}
              placeholder="Enter transaction ID"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              type="button"
              onClick={handleUseLatestTransaction}
              disabled={transactions.length === 0}
              className="px-3 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              Use Latest
            </button>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Amount (cents)
          </label>
          <input
            type="number"
            value={amount}
            onChange={e => setAmount(Number(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            min="1"
            step="1"
          />
        </div>

        <button
          onClick={handleTrigger}
          disabled={isLoading || !transactionId.trim()}
          className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isLoading ? 'Triggering...' : 'Trigger Protocol'}
        </button>
      </div>

      {transactions.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Recent Transactions</h3>
          <div className="max-h-32 overflow-y-auto">
            {transactions.slice(0, 5).map(transaction => (
              <div
                key={transaction._id}
                className="flex justify-between items-center p-2 border-b border-gray-200 text-sm cursor-pointer hover:bg-gray-50"
                onClick={() => {
                  setTransactionId(transaction._id);
                  setAmount(transaction.amount);
                }}
              >
                <span className="font-mono text-xs">{transaction._id.slice(-8)}</span>
                <span>${(transaction.amount / 100).toFixed(2)}</span>
                <span className={`px-2 py-1 rounded text-xs ${
                  transaction.status === 'success'
                    ? 'bg-green-100 text-green-800'
                    : transaction.status === 'failure'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {transaction.status}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
