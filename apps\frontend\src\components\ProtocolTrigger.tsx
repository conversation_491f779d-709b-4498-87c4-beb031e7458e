import React, { useState } from 'react';
import { triggerProtocol } from '../services/api';

const protocolOptions = [
  { code: '101.1', label: 'Authorize' },
  { code: '101.3', label: 'Capture' },
  { code: '101.5', label: 'Refund' },
  { code: '101.8', label: 'Reversal' },
];

export const ProtocolTrigger: React.FC = () => {
  const [protocol, setProtocol] = useState('101.1');
  const [amount, setAmount] = useState(1000);
  const [result, setResult] = useState<any>(null);

  const handleTrigger = async () => {
    const data: any = { amount };
    const res = await triggerProtocol(protocol, data);
    setResult(res);
  };

  return (
    <div>
      <h2>Trigger Protocol</h2>
      <select value={protocol} onChange={e => setProtocol(e.target.value)}>
        {protocolOptions.map(opt => (
          <option key={opt.code} value={opt.code}>{opt.label}</option>
        ))}
      </select>
      <input type="number" value={amount} onChange={e => setAmount(Number(e.target.value))} />
      <button onClick={handleTrigger}>Trigger</button>
      {result && <pre>{JSON.stringify(result, null, 2)}</pre>}
    </div>
  );
};
