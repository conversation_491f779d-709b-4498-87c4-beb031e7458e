#!/bin/bash

set -e

echo "📱 Creating Simple APK for PAX A920 Pro (No Java Required)"
echo "=========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if production build exists
if [ ! -d "dist/apk" ]; then
    print_error "APK bundle not found. Please run 'bash build-production.sh' first."
    exit 1
fi

print_status "Using Cordova for APK creation (No Java/Android Studio required)"

# Check if Cordova is installed
if ! command -v cordova &> /dev/null; then
    print_warning "Cordova not found. Installing Cordova..."
    npm install -g cordova
fi

# Create Cordova project
print_status "Creating Cordova project..."

# Remove existing cordova project
rm -rf cordova-app

# Create new Cordova project
cordova create cordova-app com.paxpos.terminal "PAX POS Terminal"

cd cordova-app

# Add Android platform
print_status "Adding Android platform..."
cordova platform add android

# Copy web assets
print_status "Copying web assets..."
rm -rf www/*
cp -r ../dist/apk/* www/

# Rename android.html to index.html for Cordova
if [ -f "www/android.html" ]; then
    mv www/android.html www/index.html
fi

# Configure Cordova
print_status "Configuring Cordova..."

# Update config.xml
cat > config.xml << 'EOF'
<?xml version='1.0' encoding='utf-8'?>
<widget id="com.paxpos.terminal" version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>PAX POS Terminal</name>
    <description>Professional POS terminal with Stripe integration</description>
    <author email="<EMAIL>" href="https://paxpos.com">PAX POS Team</author>
    
    <content src="index.html" />
    
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    
    <platform name="android">
        <allow-intent href="market:*" />
        
        <!-- Android permissions -->
        <uses-permission android:name="android.permission.INTERNET" />
        <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
        <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
        <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
        <uses-permission android:name="android.permission.WAKE_LOCK" />
        <uses-permission android:name="android.permission.VIBRATE" />
        <uses-permission android:name="android.permission.CAMERA" />
        <uses-permission android:name="android.permission.NFC" />
        <uses-permission android:name="android.permission.BLUETOOTH" />
        <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
        
        <!-- App configuration -->
        <preference name="Orientation" value="portrait" />
        <preference name="Fullscreen" value="true" />
        <preference name="KeepRunning" value="true" />
        <preference name="DisallowOverscroll" value="true" />
        <preference name="BackgroundColor" value="0xff3B82F6" />
        
        <!-- Security -->
        <preference name="AllowInlineMediaPlayback" value="true" />
        <preference name="MediaPlaybackRequiresUserAction" value="false" />
        <preference name="AllowUntrustedCerts" value="false" />
        <preference name="DisallowOverscroll" value="true" />
        
        <!-- Performance -->
        <preference name="LoadingDialog" value="PAX POS Terminal,Loading..." />
        <preference name="LoadingPageDialog" value="PAX POS Terminal,Loading..." />
        <preference name="ErrorUrl" value="" />
        <preference name="ShowTitle" value="false" />
        <preference name="LogLevel" value="ERROR" />
        
        <!-- WebView settings -->
        <preference name="WebViewBounce" value="false" />
        <preference name="UIWebViewBounce" value="false" />
        <preference name="DisallowOverscroll" value="true" />
        <preference name="SuppressesIncrementalRendering" value="false" />
        <preference name="KeyboardDisplayRequiresUserAction" value="true" />
        <preference name="HideKeyboardFormAccessoryBar" value="true" />
        <preference name="KeyboardShrinksView" value="false" />
        <preference name="GapBetweenPages" value="0" />
        <preference name="PageLength" value="0" />
        <preference name="PaginationBreakingMode" value="page" />
        <preference name="PaginationMode" value="unpaginated" />
        
        <!-- Icons and splash screens -->
        <icon density="ldpi" src="www/icon-36.png" />
        <icon density="mdpi" src="www/icon-48.png" />
        <icon density="hdpi" src="www/icon-72.png" />
        <icon density="xhdpi" src="www/icon-96.png" />
        <icon density="xxhdpi" src="www/icon-144.png" />
        <icon density="xxxhdpi" src="www/icon-192.png" />
    </platform>
    
    <!-- Global preferences -->
    <preference name="DisallowOverscroll" value="true" />
    <preference name="BackgroundColor" value="0xff3B82F6" />
    <preference name="HideKeyboardFormAccessoryBar" value="true" />
    <preference name="Orientation" value="portrait" />
    <preference name="Fullscreen" value="true" />
</widget>
EOF

# Create simple icons (using text-based approach)
print_status "Creating app icons..."

# Create a simple icon using ImageMagick if available, otherwise use placeholder
if command -v convert &> /dev/null; then
    # Create icons with ImageMagick
    convert -size 192x192 xc:"#3B82F6" -gravity center -pointsize 60 -fill white -annotate +0+0 "PAX" www/icon-192.png
    convert -size 144x144 xc:"#3B82F6" -gravity center -pointsize 45 -fill white -annotate +0+0 "PAX" www/icon-144.png
    convert -size 96x96 xc:"#3B82F6" -gravity center -pointsize 30 -fill white -annotate +0+0 "PAX" www/icon-96.png
    convert -size 72x72 xc:"#3B82F6" -gravity center -pointsize 22 -fill white -annotate +0+0 "PAX" www/icon-72.png
    convert -size 48x48 xc:"#3B82F6" -gravity center -pointsize 15 -fill white -annotate +0+0 "PAX" www/icon-48.png
    convert -size 36x36 xc:"#3B82F6" -gravity center -pointsize 11 -fill white -annotate +0+0 "PAX" www/icon-36.png
    print_success "Icons created with ImageMagick"
else
    # Create placeholder icon files
    touch www/icon-{36,48,72,96,144,192}.png
    print_warning "ImageMagick not found. Created placeholder icons."
    print_warning "For better icons, install ImageMagick or add custom icon files to www/"
fi

# Add PAX terminal bridge plugin
print_status "Adding PAX terminal bridge..."

# Create custom plugin for PAX integration
mkdir -p plugins/pax-terminal-bridge/src/android

cat > plugins/pax-terminal-bridge/plugin.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<plugin xmlns="http://apache.org/cordova/ns/plugins/1.0"
        id="pax-terminal-bridge"
        version="1.0.0">
    
    <name>PAX Terminal Bridge</name>
    <description>Bridge for PAX A920 Pro terminal hardware</description>
    
    <js-module src="www/pax-terminal.js" name="PAXTerminal">
        <clobbers target="PAXTerminal" />
    </js-module>
    
    <platform name="android">
        <config-file target="res/xml/config.xml" parent="/*">
            <feature name="PAXTerminal">
                <param name="android-package" value="com.paxpos.terminal.PAXTerminalPlugin"/>
            </feature>
        </config-file>
        
        <source-file src="src/android/PAXTerminalPlugin.java" target-dir="src/com/paxpos/terminal" />
    </platform>
</plugin>
EOF

# Create JavaScript interface
mkdir -p plugins/pax-terminal-bridge/www
cat > plugins/pax-terminal-bridge/www/pax-terminal.js << 'EOF'
var exec = require('cordova/exec');

var PAXTerminal = {
    readCard: function(success, error, options) {
        exec(success, error, "PAXTerminal", "readCard", [options || {}]);
    },
    
    printReceipt: function(success, error, text) {
        exec(success, error, "PAXTerminal", "printReceipt", [text]);
    },
    
    getTerminalInfo: function(success, error) {
        exec(success, error, "PAXTerminal", "getTerminalInfo", []);
    },
    
    beep: function(success, error, duration) {
        exec(success, error, "PAXTerminal", "beep", [duration || 200]);
    },
    
    vibrate: function(success, error, duration) {
        exec(success, error, "PAXTerminal", "vibrate", [duration || 200]);
    }
};

module.exports = PAXTerminal;
EOF

# Create Android plugin
cat > plugins/pax-terminal-bridge/src/android/PAXTerminalPlugin.java << 'EOF'
package com.paxpos.terminal;

import org.apache.cordova.CordovaPlugin;
import org.apache.cordova.CallbackContext;
import org.apache.cordova.PluginResult;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import android.content.Context;
import android.os.Vibrator;
import android.media.ToneGenerator;
import android.media.AudioManager;

public class PAXTerminalPlugin extends CordovaPlugin {
    
    @Override
    public boolean execute(String action, JSONArray args, CallbackContext callbackContext) throws JSONException {
        
        if ("readCard".equals(action)) {
            this.readCard(callbackContext);
            return true;
        }
        
        if ("printReceipt".equals(action)) {
            String text = args.getString(0);
            this.printReceipt(text, callbackContext);
            return true;
        }
        
        if ("getTerminalInfo".equals(action)) {
            this.getTerminalInfo(callbackContext);
            return true;
        }
        
        if ("beep".equals(action)) {
            int duration = args.getInt(0);
            this.beep(duration, callbackContext);
            return true;
        }
        
        if ("vibrate".equals(action)) {
            int duration = args.getInt(0);
            this.vibrate(duration, callbackContext);
            return true;
        }
        
        return false;
    }
    
    private void readCard(CallbackContext callbackContext) {
        // Simulate card reading for now
        // In production, integrate with actual PAX SDK
        cordova.getThreadPool().execute(new Runnable() {
            public void run() {
                try {
                    Thread.sleep(3000); // Simulate reading delay
                    
                    JSONObject cardData = new JSONObject();
                    cardData.put("pan", "****************");
                    cardData.put("expiry_date", "1225");
                    cardData.put("cardholder_name", "TEST CARDHOLDER");
                    cardData.put("card_type", "chip");
                    
                    callbackContext.success(cardData);
                } catch (Exception e) {
                    callbackContext.error("Card reading failed: " + e.getMessage());
                }
            }
        });
    }
    
    private void printReceipt(String text, CallbackContext callbackContext) {
        // Simulate printing
        // In production, integrate with actual PAX printer
        try {
            // Add printing logic here
            callbackContext.success("Receipt printed successfully");
        } catch (Exception e) {
            callbackContext.error("Printing failed: " + e.getMessage());
        }
    }
    
    private void getTerminalInfo(CallbackContext callbackContext) {
        try {
            JSONObject info = new JSONObject();
            info.put("serial_number", "CORDOVA_001");
            info.put("model", "PAX A920 Pro (Cordova)");
            info.put("firmware_version", "1.0.0-cordova");
            info.put("battery_level", 85);
            info.put("is_hardware_available", true);
            
            callbackContext.success(info);
        } catch (JSONException e) {
            callbackContext.error("Failed to get terminal info");
        }
    }
    
    private void beep(int duration, CallbackContext callbackContext) {
        try {
            ToneGenerator toneGen = new ToneGenerator(AudioManager.STREAM_ALARM, 100);
            toneGen.startTone(ToneGenerator.TONE_CDMA_ALERT_CALL_GUARD, duration);
            callbackContext.success("Beep completed");
        } catch (Exception e) {
            callbackContext.error("Beep failed: " + e.getMessage());
        }
    }
    
    private void vibrate(int duration, CallbackContext callbackContext) {
        try {
            Vibrator vibrator = (Vibrator) cordova.getActivity().getSystemService(Context.VIBRATOR_SERVICE);
            if (vibrator != null) {
                vibrator.vibrate(duration);
            }
            callbackContext.success("Vibration completed");
        } catch (Exception e) {
            callbackContext.error("Vibration failed: " + e.getMessage());
        }
    }
}
EOF

# Install the plugin
cordova plugin add ./plugins/pax-terminal-bridge

# Build APK
print_status "Building APK..."

# Build for Android
cordova build android --release

# Copy APK to root directory
if [ -f "platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk" ]; then
    cp platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk ../pax-pos-terminal-unsigned.apk
    print_success "APK created: pax-pos-terminal-unsigned.apk"
else
    print_error "APK build failed"
    exit 1
fi

cd ..

# Create installation script
print_status "Creating installation script..."

cat > install-simple-apk.sh << 'EOF'
#!/bin/bash

echo "📱 Installing PAX POS Terminal APK (Simple Version)"

APK_FILE="pax-pos-terminal-unsigned.apk"

# Check if APK exists
if [ ! -f "$APK_FILE" ]; then
    echo "❌ APK file not found: $APK_FILE"
    echo "Please run 'bash create-simple-apk.sh' first."
    exit 1
fi

# Check if ADB is available
if ! command -v adb &> /dev/null; then
    echo "❌ ADB is not installed."
    echo "Please install Android SDK Platform Tools or enable USB debugging and install manually."
    echo ""
    echo "Manual installation:"
    echo "1. Copy $APK_FILE to your PAX A920 Pro"
    echo "2. Enable 'Unknown sources' in Settings > Security"
    echo "3. Open the APK file and install"
    exit 1
fi

# Check if device is connected
if ! adb devices | grep -q "device$"; then
    echo "❌ No Android device connected."
    echo "Please connect your PAX A920 Pro via USB and enable USB debugging."
    echo ""
    echo "Alternative: Manual installation"
    echo "1. Copy $APK_FILE to your device"
    echo "2. Install using file manager"
    exit 1
fi

# Install APK
echo "📦 Installing APK..."
adb install -r "$APK_FILE"

if [ $? -eq 0 ]; then
    echo "✅ Installation successful!"
    echo ""
    echo "🚀 Launching PAX POS Terminal..."
    adb shell am start -n com.paxpos.terminal/.MainActivity
    echo ""
    echo "The PAX POS Terminal is now installed and running on your device."
else
    echo "❌ Installation failed."
    echo "Try manual installation or check device permissions."
fi
EOF

chmod +x install-simple-apk.sh

print_success "Installation script created: install-simple-apk.sh"

echo ""
echo "🎉 Simple APK creation completed!"
echo "================================="
echo ""
echo "📱 APK File: pax-pos-terminal-unsigned.apk"
echo "📋 Installer: install-simple-apk.sh"
echo ""
echo "🔧 Installation Options:"
echo ""
echo "1. 🔌 USB Installation (ADB):"
echo "   ./install-simple-apk.sh"
echo ""
echo "2. 📁 Manual Installation:"
echo "   - Copy pax-pos-terminal-unsigned.apk to your PAX A920 Pro"
echo "   - Enable 'Unknown sources' in Settings > Security"
echo "   - Open the APK file and install"
echo ""
echo "3. 🌐 Web Installation:"
echo "   - Upload APK to a web server"
echo "   - Download and install on device"
echo ""
echo "⚠️  Note: This APK uses simulated hardware functions."
echo "   For real PAX SDK integration, use the full Android development setup."
echo ""
print_success "Ready to install on PAX A920 Pro! 📱"
