#!/bin/bash

echo "🦀 Building PAX Terminal WASM module..."

# Install wasm-pack if not available
if ! command -v wasm-pack &> /dev/null; then
    echo "Installing wasm-pack..."
    curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh
fi

# Build WASM module
wasm-pack build --target web --out-dir pkg --release

# Copy WASM files to frontend
echo "📦 Copying WASM files to frontend..."
mkdir -p ../frontend/public/wasm
cp pkg/pax_terminal_wasm.js ../frontend/public/wasm/
cp pkg/pax_terminal_wasm_bg.wasm ../frontend/public/wasm/
cp pkg/pax_terminal_wasm.d.ts ../frontend/src/types/

echo "✅ WASM module built successfully!"
echo "📁 Files copied to:"
echo "   - ../frontend/public/wasm/pax_terminal_wasm.js"
echo "   - ../frontend/public/wasm/pax_terminal_wasm_bg.wasm"
echo "   - ../frontend/src/types/pax_terminal_wasm.d.ts"
