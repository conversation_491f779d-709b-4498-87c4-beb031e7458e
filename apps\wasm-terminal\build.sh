#!/bin/bash

set -e

echo "🦀 Building PAX Terminal WASM module..."

# Check if we're in the right directory
if [ ! -f "Cargo.toml" ]; then
    echo "❌ Error: Cargo.toml not found. Please run from apps/wasm-terminal directory"
    exit 1
fi

# Install wasm-pack if not available
if ! command -v wasm-pack &> /dev/null; then
    echo "Installing wasm-pack..."
    curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh
    # Add to PATH for current session
    export PATH="$HOME/.cargo/bin:$PATH"
fi

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf pkg/
rm -rf target/

# Build WASM module with correct naming
echo "🔨 Building WASM module..."
wasm-pack build --target web --out-dir pkg --release --out-name pax_terminal_wasm

# Check if build was successful
if [ ! -f "pkg/pax_terminal_wasm.js" ]; then
    echo "❌ WASM build failed - files not generated"
    echo "Checking pkg directory contents:"
    ls -la pkg/ || echo "pkg directory doesn't exist"
    exit 1
fi

# Create frontend directories
echo "📦 Copying WASM files to frontend..."
mkdir -p ../frontend/public/wasm
mkdir -p ../frontend/src/types

# Copy WASM files to frontend
if [ -f "pkg/pax_terminal_wasm.js" ]; then
    cp pkg/pax_terminal_wasm.js ../frontend/public/wasm/
    echo "✅ Copied pax_terminal_wasm.js"
else
    echo "❌ pax_terminal_wasm.js not found"
fi

if [ -f "pkg/pax_terminal_wasm_bg.wasm" ]; then
    cp pkg/pax_terminal_wasm_bg.wasm ../frontend/public/wasm/
    echo "✅ Copied pax_terminal_wasm_bg.wasm"
else
    echo "❌ pax_terminal_wasm_bg.wasm not found"
fi

if [ -f "pkg/pax_terminal_wasm.d.ts" ]; then
    cp pkg/pax_terminal_wasm.d.ts ../frontend/src/types/
    echo "✅ Copied pax_terminal_wasm.d.ts"
else
    echo "❌ pax_terminal_wasm.d.ts not found"
fi

# List what was actually generated
echo "📁 Generated files in pkg/:"
ls -la pkg/

echo "✅ WASM module built successfully!"
echo "📁 Files should be copied to:"
echo "   - ../frontend/public/wasm/pax_terminal_wasm.js"
echo "   - ../frontend/public/wasm/pax_terminal_wasm_bg.wasm"
echo "   - ../frontend/src/types/pax_terminal_wasm.d.ts"
