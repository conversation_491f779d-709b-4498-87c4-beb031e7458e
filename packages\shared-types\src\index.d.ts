export interface ApiResponse<T = unknown> {
    success: boolean;
    data?: T;
    message?: string;
    error?: {
        code?: string;
        details?: any;
    };
}
export interface PaginationParams {
    limit?: number;
    offset?: number;
}
export interface PaginationResponse {
    limit: number;
    offset: number;
    count: number;
    total?: number;
}
export declare enum TransactionStatus {
    PENDING = "pending",
    SUCCESS = "success",
    FAILURE = "failure"
}
export declare enum ProtocolEventCode {
    AUTHORIZATION = "101.1",
    CAPTURE = "101.3",
    REVERSAL = "101.5",
    SETTLEMENT = "101.8"
}
export interface Transaction {
    _id: string;
    amount: number;
    status: TransactionStatus;
    protocolCode?: ProtocolEventCode;
    stripePaymentIntentId?: string;
    receiptUrl?: string;
    metadata?: Record<string, any>;
    createdAt: string;
    updatedAt: string;
}
export interface CreateTransactionRequest {
    amount: number;
    status: TransactionStatus;
    protocolCode?: ProtocolEventCode;
    stripePaymentIntentId?: string;
    receiptUrl?: string;
    metadata?: Record<string, any>;
}
export interface SimulatePaymentRequest {
    amount: number;
    status: 'success' | 'failure';
    protocolCode?: ProtocolEventCode;
    metadata?: Record<string, any>;
}
export interface CreateStripePaymentIntentRequest {
    amount: number;
    currency: string;
    metadata?: Record<string, any>;
}
export interface CreateStripePaymentIntentResponse {
    client_secret: string;
    id: string;
    amount: number;
    currency: string;
    status: string;
}
export interface CapturePaymentIntentRequest {
    paymentIntentId: string;
    amountToCapture?: number;
}
export interface CapturePaymentIntentResponse {
    id: string;
    status: string;
    amount_received: number;
}
export interface ConnectionTokenResponse {
    secret: string;
}
export interface ProtocolMessage {
    protocolEventCode: ProtocolEventCode;
    mti: string;
    transactionId: string;
    stripePaymentIntentId?: string;
    timestamp: string;
    de2_pan?: string;
    de3_processingCode?: string;
    de4_transactionAmount?: number;
    de11_stan?: string;
    de12_localTime?: string;
    de13_localDate?: string;
    de18_merchantCategoryCode?: string;
    de37_retrievalReferenceNumber?: string;
    de38_approvalCode?: string;
    de39_responseCode?: string;
    de41_cardAcceptorTerminalId?: string;
    de42_cardAcceptorIdCode?: string;
    de49_currencyCodeTransaction?: string;
    de62_additionalData?: Record<string, any>;
    de90_originalDataElements?: Record<string, any>;
}
export interface TriggerProtocolRequest {
    protocolEventCode: ProtocolEventCode;
    transactionId: string;
    stripePaymentIntentId?: string;
    amount: number;
    metadata?: Record<string, any>;
}
export interface BankResponse {
    success: boolean;
    message: string;
    data?: {
        bankResponseCode: string;
        bankResponseMessage: string;
        receivedMessage: ProtocolMessage;
    };
    error?: {
        code: string;
        details?: any;
    };
}
export interface ReceiptData {
    transactionId: string;
    amount: number;
    status: TransactionStatus;
    timestamp: string;
    merchantInfo?: {
        name: string;
        address: string;
        phone: string;
    };
    terminalInfo?: {
        id: string;
        model: string;
    };
}
export interface ErrorResponse {
    success: false;
    error: string;
    message: string;
    details?: any;
}
//# sourceMappingURL=index.d.ts.map