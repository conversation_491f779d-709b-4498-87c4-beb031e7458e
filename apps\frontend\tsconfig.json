{"extends": "../../packages/tsconfig-custom/react-library.json", "compilerOptions": {"baseUrl": "./src", "paths": {"@/*": ["*"]}, "types": ["vite/client"], "noEmit": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "vite.config.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "src/test/**/*", "src/**/__tests__/**/*"]}