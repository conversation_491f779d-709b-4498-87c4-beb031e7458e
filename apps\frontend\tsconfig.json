{
  "extends": "tsconfig-custom/react-library.json",
  "compilerOptions": {
    "baseUrl": "./src", // Allows for cleaner imports like "@/components/..."
    "paths": {
      "@/*": ["*"]
    },
    // Vite specific options if needed, but react-library should cover most
    "types": ["vite/client"], // Important for Vite specific env vars and features
    "noEmit": true // Vite handles transpilation, TypeScript is for type checking
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "vite.config.ts"],
  "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]
}
