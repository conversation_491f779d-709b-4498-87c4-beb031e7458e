import axios, { AxiosResponse } from 'axios';

const API_BASE = '/api/v1';

const api = axios.create({
  baseURL: API_BASE,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  details?: any;
}

export interface Transaction {
  _id: string;
  amount: number;
  status: 'success' | 'failure' | 'pending';
  protocolCode?: string;
  stripePaymentIntentId?: string;
  receiptUrl?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface SimulatePaymentRequest {
  amount: number;
  status: 'success' | 'failure';
  protocolCode?: '101.1' | '101.3' | '101.5' | '101.8';
  metadata?: Record<string, any>;
}

export interface TriggerProtocolRequest {
  protocolEventCode: '101.1' | '101.3' | '101.5' | '101.8';
  transactionId: string;
  stripePaymentIntentId?: string;
  amount: number;
  metadata?: Record<string, any>;
}

export interface CreatePaymentIntentRequest {
  amount: number;
  currency: string;
  metadata?: Record<string, any>;
}

export interface CapturePaymentIntentRequest {
  paymentIntentId: string;
  amountToCapture?: number;
}

export class ApiService {
  async simulatePayment(request: SimulatePaymentRequest): Promise<ApiResponse<{ transaction: Transaction }>> {
    const response: AxiosResponse<ApiResponse<{ transaction: Transaction }>> = await api.post('/payment/simulate', request);
    return response.data;
  }

  async triggerProtocol(request: TriggerProtocolRequest): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await api.post('/protocol/trigger', request);
    return response.data;
  }

  async getConnectionToken(): Promise<ApiResponse<{ secret: string }>> {
    const response: AxiosResponse<ApiResponse<{ secret: string }>> = await api.post('/stripe/connection_token');
    return response.data;
  }

  async createPaymentIntent(request: CreatePaymentIntentRequest): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await api.post('/stripe/payment_intent', request);
    return response.data;
  }

  async capturePaymentIntent(request: CapturePaymentIntentRequest): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await api.post('/stripe/capture_intent', request);
    return response.data;
  }

  async getTransactions(limit?: number, offset?: number): Promise<ApiResponse<{ transactions: Transaction[]; pagination: any }>> {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (offset) params.append('offset', offset.toString());

    const response: AxiosResponse<ApiResponse<{ transactions: Transaction[]; pagination: any }>> =
      await api.get(`/transaction?${params.toString()}`);
    return response.data;
  }

  async getTransactionById(id: string): Promise<ApiResponse<Transaction>> {
    const response: AxiosResponse<ApiResponse<Transaction>> = await api.get(`/transaction/${id}`);
    return response.data;
  }

  async createTransaction(request: Partial<Transaction>): Promise<ApiResponse<{ transaction: Transaction; receipt: string }>> {
    const response: AxiosResponse<ApiResponse<{ transaction: Transaction; receipt: string }>> =
      await api.post('/transaction', request);
    return response.data;
  }
}

export const apiService = new ApiService();
