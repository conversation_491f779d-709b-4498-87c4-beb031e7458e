const API_BASE = '/api/v1';

export async function simulatePayment(amount: number, status: 'success' | 'failure') {
  const res = await fetch(`${API_BASE}/payment/simulate`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ amount, status }),
  });
  return res.json();
}

export async function triggerProtocol(protocol: string, data: any) {
  const res = await fetch(`${API_BASE}/protocol/trigger`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ protocol, data }),
  });
  return res.json();
}

export async function fetchLogs() {
  const res = await fetch(`${API_BASE}/logs`);
  return res.json();
}
