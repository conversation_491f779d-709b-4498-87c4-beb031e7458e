module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "turbo",
    "prettier",
  ],
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: "latest",
    sourceType: "module",
  },
  plugins: ["@typescript-eslint", "react", "react-hooks"],
  rules: {
    "react/react-in-jsx-scope": "off", // Not needed with React 17+ new JSX transform
    "react/prop-types": "off", // Prefer TypeScript for prop types
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": "warn", // Warn instead of error for 'any'
    "@typescript-eslint/no-unused-vars": ["warn", { "argsIgnorePattern": "^_" }],
    "no-console": ["warn", { "allow": ["warn", "error"] }], // Allow console.warn and console.error
  },
  settings: {
    react: {
      version: "detect", // Automatically detect the React version
    },
  },
  ignorePatterns: [
    "node_modules/",
    "dist/",
    ".turbo/",
    ".eslintrc.js", // Or .cjs if you use that extension for ESLint config itself
    "vite.config.ts",
    "vitest.config.ts",
    "coverage/"
  ],
};
