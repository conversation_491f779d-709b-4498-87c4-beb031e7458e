import { config } from 'dotenv';
import { z } from 'zod';
import path from 'path';

// Load environment files in order of precedence
// 1. Load .env.development (or .env.production/.env.test based on NODE_ENV)
// 2. Load .env as fallback
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' :
                process.env.NODE_ENV === 'test' ? '.env.test' : '.env.development';

// Try to load from project root (monorepo root)
config({ path: path.resolve(process.cwd(), '../../', envFile) });
config({ path: path.resolve(process.cwd(), '../../', '.env'), override: false });

// Also try to load from current directory as fallback
config({ path: path.resolve(process.cwd(), envFile), override: false });
config({ path: path.resolve(process.cwd(), '.env'), override: false });

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(65535)).default('3001'),
  MONGO_URI: z.string().default('mongodb://localhost:27017/pax_pos_system'),
  STRIPE_SECRET_KEY: z.string().default('sk_test_development_key_placeholder'),
  STRIPE_PUBLISHABLE_KEY: z.string().default('pk_test_development_key_placeholder'),
  STRIPE_WEBHOOK_SECRET: z.string().default('whsec_development_secret_placeholder'),
  MOCK_BANK_URL: z.string().url().default('http://localhost:3002/financial-message'),
  LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),
  JWT_SECRET: z.string().default('development_jwt_secret_at_least_32_characters_long_for_dev'),
  CORS_ORIGIN: z.string().default('http://localhost:3000'),
  RATE_LIMIT_MAX: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('100'),
  RATE_LIMIT_WINDOW: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('900000'),

  // Terminal Configuration
  TERMINAL_ID: z.string().default('PAX_A920_001'),
  MERCHANT_ID: z.string().default('MERCHANT_001'),
  DEFAULT_CURRENCY: z.string().default('usd'),
  PAYMENT_TIMEOUT: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('30000'),
  RETRY_ATTEMPTS: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('3'),
  MIN_AMOUNT: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('50'),
  MAX_AMOUNT: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('99999999'),

  // Feature Flags
  CARD_READER_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  PRINTER_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  AUDIO_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  VIBRATION_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  MANUAL_ENTRY_ENABLED: z.string().transform(val => val !== 'false').default('true'),

  // Receipt Configuration
  RECEIPT_ENABLED: z.string().transform(val => val !== 'false').default('true'),
  RECEIPT_COPIES: z.string().transform(val => parseInt(val, 10)).pipe(z.number().positive()).default('2'),
  RECEIPT_SIGNATURE: z.string().transform(val => val !== 'false').default('true'),
  RECEIPT_FOOTER: z.string().default('Thank you for your business!'),

  // UI Configuration
  UI_THEME: z.enum(['light', 'dark', 'auto']).default('auto'),
  UI_LANGUAGE: z.string().default('en'),
  UI_FONT_SIZE: z.enum(['small', 'medium', 'large']).default('medium'),
  UI_ORIENTATION: z.enum(['portrait', 'landscape', 'auto']).default('portrait'),
});

function validateEnv() {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join('\n');
      throw new Error(`Environment validation failed:\n${missingVars}`);
    }
    throw error;
  }
}

export const env = validateEnv();
export type Environment = typeof env;
