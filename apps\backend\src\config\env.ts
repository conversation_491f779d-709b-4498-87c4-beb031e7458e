import dotenv from 'dotenv';
import path from 'path';

// Determine the environment and load the appropriate .env file
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' :
                process.env.NODE_ENV === 'test' ? '.env.test' : '.env.development';

// Load environment variables from the determined .env file
// Fallback to .env if specific one is not found, then to system env vars
export function loadEnv() {
  dotenv.config({ path: path.resolve(process.cwd(), envFile) }); // process.cwd() should be project root
  dotenv.config({ path: path.resolve(process.cwd(), '.env'), override: false }); // Load .env if specific not found, don't override
}

// Export typed environment variables for better DX
// Use Zod or similar for validation if desired for stricter env var checking

interface EnvironmentVariables {
  NODE_ENV: string;
  PORT: number;
  MONGO_URI: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_PUBLISHABLE_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  MOCK_BANK_URL: string;
  LOG_LEVEL: string;
  JWT_SECRET: string; // For user authentication later
  // Add other environment variables as needed
}

// Initial load during module import (e.g. when app starts)
loadEnv();

// Provide defaults and parse types
export const env: EnvironmentVariables = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: parseInt(process.env.PORT || '3001', 10), // Backend port
  MONGO_URI: process.env.MONGO_URI || 'mongodb://localhost:27017/pax_pos_system',
  STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY || 'sk_test_YOUR_STRIPE_SECRET_KEY',
  STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY || 'pk_test_YOUR_STRIPE_PUBLISHABLE_KEY',
  STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET || 'whsec_YOUR_STRIPE_WEBHOOK_SECRET',
  MOCK_BANK_URL: process.env.MOCK_BANK_URL || 'http://localhost:3002/financial-message', // Assuming mock bank on 3002
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
  JWT_SECRET: process.env.JWT_SECRET || 'a-very-strong-jwt-secret-for-dev',
};

// Validate essential variables (optional, but good practice)
if (!env.STRIPE_SECRET_KEY.startsWith('sk_test_') && !env.STRIPE_SECRET_KEY.startsWith('sk_live_')) {
  if (process.env.NODE_ENV !== 'test') { // Don't log this during tests if key is dummy
    console.warn('Warning: STRIPE_SECRET_KEY does not look like a valid Stripe secret key.');
  }
}
if (!env.MONGO_URI) {
  throw new Error('FATAL ERROR: MONGO_URI is not defined.');
}

// Log the environment being used (but not sensitive values directly here)
if (process.env.NODE_ENV !== 'test') {
    console.log(`Backend environment: ${env.NODE_ENV}`);
}
