import { FastifyRequest, FastifyReply } from 'fastify';
import * as stripeService from '../services/stripeService';

export const getConnectionToken = async (_request: FastifyRequest, reply: FastifyReply) => {
  try {
    const token = await stripeService.createConnectionToken();
    return reply.send({ secret: token.secret });
  } catch (err: any) {
    return reply.status(500).send({ error: 'STRIPE_ERROR', message: err.message });
  }
};

export const createIntent = async (request: FastifyRequest, reply: FastifyReply) => {
  const { amount, currency } = request.body as any;
  if (!amount || !currency) {
    return reply.status(400).send({ error: 'INVALID_INPUT' });
  }
  try {
    const intent = await stripeService.createPaymentIntent(amount, currency);
    return reply.send({ client_secret: intent.client_secret, id: intent.id });
  } catch (err: any) {
    return reply.status(500).send({ error: 'STRIPE_ERROR', message: err.message });
  }
};

export const captureIntent = async (request: FastifyRequest, reply: FastifyReply) => {
  const { paymentIntentId } = request.body as any;
  if (!paymentIntentId) {
    return reply.status(400).send({ error: 'INVALID_INPUT' });
  }
  try {
    const intent = await stripeService.capturePaymentIntent(paymentIntentId);
    return reply.send({ status: intent.status });
  } catch (err: any) {
    return reply.status(500).send({ error: 'STRIPE_ERROR', message: err.message });
  }
};
