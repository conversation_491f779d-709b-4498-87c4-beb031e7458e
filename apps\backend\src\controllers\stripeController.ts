import { FastifyRequest, FastifyReply } from 'fastify';
import { stripeService } from '../services/stripeService';
import { z } from 'zod';
import { logger } from '../config/logger';

const controllerLogger = logger.child({ module: 'stripe-controller' });

const createIntentSchema = z.object({
  amount: z.number().positive().int(),
  currency: z.string().length(3),
  metadata: z.record(z.string()).optional(),
});

const captureIntentSchema = z.object({
  paymentIntentId: z.string().min(1),
  amountToCapture: z.number().positive().int().optional(),
});

export const getConnectionToken = async (_request: FastifyRequest, reply: FastifyReply) => {
  try {
    controllerLogger.info('Processing connection token request');
    const token = await stripeService.createConnectionToken();
    return reply.send({
      success: true,
      data: { secret: token.secret }
    });
  } catch (error) {
    controllerLogger.error({ error }, 'Failed to get connection token');
    return reply.status(500).send({
      success: false,
      error: 'STRIPE_CONNECTION_TOKEN_ERROR',
      message: 'Failed to create connection token'
    });
  }
};

export const createIntent = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const validatedBody = createIntentSchema.parse(request.body);
    controllerLogger.info({ amount: validatedBody.amount, currency: validatedBody.currency }, 'Creating payment intent');

    const intent = await stripeService.createPaymentIntent(validatedBody);

    return reply.send({
      success: true,
      data: {
        client_secret: intent.client_secret,
        id: intent.id,
        amount: intent.amount,
        currency: intent.currency,
        status: intent.status
      }
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      controllerLogger.warn({ error: error.errors }, 'Invalid payment intent request');
      return reply.status(400).send({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid request parameters',
        details: error.errors
      });
    }

    controllerLogger.error({ error }, 'Failed to create payment intent');
    return reply.status(500).send({
      success: false,
      error: 'STRIPE_PAYMENT_INTENT_ERROR',
      message: 'Failed to create payment intent'
    });
  }
};

export const captureIntent = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const validatedBody = captureIntentSchema.parse(request.body);
    controllerLogger.info({ paymentIntentId: validatedBody.paymentIntentId }, 'Capturing payment intent');

    const intent = await stripeService.capturePaymentIntent(validatedBody);

    return reply.send({
      success: true,
      data: {
        id: intent.id,
        status: intent.status,
        amount_received: intent.amount_received
      }
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      controllerLogger.warn({ error: error.errors }, 'Invalid capture request');
      return reply.status(400).send({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid request parameters',
        details: error.errors
      });
    }

    controllerLogger.error({ error }, 'Failed to capture payment intent');
    return reply.status(500).send({
      success: false,
      error: 'STRIPE_CAPTURE_ERROR',
      message: 'Failed to capture payment intent'
    });
  }
};
