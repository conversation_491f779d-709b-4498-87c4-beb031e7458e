import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { PaymentSimulator } from '../PaymentSimulator';
import { useAppStore } from '../../store/useAppStore';

// Mock the store
vi.mock('../../store/useAppStore');

const mockUseAppStore = vi.mocked(useAppStore);

describe('PaymentSimulator', () => {
  const mockSimulatePayment = vi.fn();
  const mockStore = {
    simulatePayment: mockSimulatePayment,
    isLoading: false,
    error: null,
    currentTransaction: null,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseAppStore.mockReturnValue(mockStore as any);
  });

  it('should render payment simulator form', () => {
    render(<PaymentSimulator />);

    expect(screen.getByText('Payment Simulator')).toBeInTheDocument();
    expect(screen.getByLabelText(/amount/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/status/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/protocol code/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /simulate payment/i })).toBeInTheDocument();
  });

  it('should have default values', () => {
    render(<PaymentSimulator />);

    const amountInput = screen.getByLabelText(/amount/i) as HTMLInputElement;
    const statusSelect = screen.getByLabelText(/status/i) as HTMLSelectElement;
    const protocolSelect = screen.getByLabelText(/protocol code/i) as HTMLSelectElement;

    expect(amountInput.value).toBe('1000');
    expect(statusSelect.value).toBe('success');
    expect(protocolSelect.value).toBe('');
  });

  it('should update form values when user interacts', async () => {
    const user = userEvent.setup();
    render(<PaymentSimulator />);

    const amountInput = screen.getByLabelText(/amount/i);
    const statusSelect = screen.getByLabelText(/status/i);
    const protocolSelect = screen.getByLabelText(/protocol code/i);

    await user.clear(amountInput);
    await user.type(amountInput, '2000');
    await user.selectOptions(statusSelect, 'failure');
    await user.selectOptions(protocolSelect, '101.3');

    expect(amountInput).toHaveValue(2000);
    expect(statusSelect).toHaveValue('failure');
    expect(protocolSelect).toHaveValue('101.3');
  });

  it('should call simulatePayment when form is submitted', async () => {
    const user = userEvent.setup();
    render(<PaymentSimulator />);

    const submitButton = screen.getByRole('button', { name: /simulate payment/i });
    await user.click(submitButton);

    expect(mockSimulatePayment).toHaveBeenCalledWith(1000, 'success', undefined);
  });

  it('should call simulatePayment with protocol code when selected', async () => {
    const user = userEvent.setup();
    render(<PaymentSimulator />);

    const protocolSelect = screen.getByLabelText(/protocol code/i);
    await user.selectOptions(protocolSelect, '101.1');

    const submitButton = screen.getByRole('button', { name: /simulate payment/i });
    await user.click(submitButton);

    expect(mockSimulatePayment).toHaveBeenCalledWith(1000, 'success', '101.1');
  });

  it('should show loading state', () => {
    mockUseAppStore.mockReturnValue({
      ...mockStore,
      isLoading: true,
    } as any);

    render(<PaymentSimulator />);

    const submitButton = screen.getByRole('button', { name: /simulating/i });
    expect(submitButton).toBeDisabled();
  });

  it('should display error message', () => {
    const errorMessage = 'Payment simulation failed';
    mockUseAppStore.mockReturnValue({
      ...mockStore,
      error: errorMessage,
    } as any);

    render(<PaymentSimulator />);

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('should display current transaction', () => {
    const mockTransaction = {
      _id: '123',
      amount: 1500,
      status: 'success',
      protocolCode: '101.1',
      createdAt: '2024-01-01T00:00:00.000Z',
    };

    mockUseAppStore.mockReturnValue({
      ...mockStore,
      currentTransaction: mockTransaction,
    } as any);

    render(<PaymentSimulator />);

    expect(screen.getByText('Last Transaction')).toBeInTheDocument();
    expect(screen.getByText('123')).toBeInTheDocument();
    expect(screen.getByText('$15.00')).toBeInTheDocument();
    expect(screen.getByText('success')).toBeInTheDocument();
    expect(screen.getByText('101.1')).toBeInTheDocument();
  });

  it('should validate amount input', async () => {
    const user = userEvent.setup();
    render(<PaymentSimulator />);

    const amountInput = screen.getByLabelText(/amount/i);
    
    await user.clear(amountInput);
    await user.type(amountInput, '0');

    // The input should have min="1" attribute
    expect(amountInput).toHaveAttribute('min', '1');
  });

  it('should have all protocol options', () => {
    render(<PaymentSimulator />);

    const protocolSelect = screen.getByLabelText(/protocol code/i);
    const options = Array.from(protocolSelect.querySelectorAll('option')).map(
      option => option.textContent
    );

    expect(options).toEqual([
      'No Protocol',
      '101.1 - Authorization',
      '101.3 - Capture',
      '101.5 - Reversal',
      '101.8 - Settlement',
    ]);
  });
});
