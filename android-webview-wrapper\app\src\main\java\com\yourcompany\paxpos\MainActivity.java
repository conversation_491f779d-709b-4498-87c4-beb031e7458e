package com.yourcompany.paxpos;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.view.WindowManager;

import androidx.appcompat.app.AppCompatActivity;

/**
 * Main Activity that hosts the WebView for the React POS application
 */
public class MainActivity extends AppCompatActivity {
    private WebView webView;
    private PAXHardwareBridge hardwareBridge;
    
    // Your React web app URL - change this to your actual URL
    private static final String WEB_APP_URL = "http://localhost:5173"; // Vite dev server
    // For production: private static final String WEB_APP_URL = "https://your-domain.com";
    
    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // Keep screen on for POS usage
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        
        // Initialize WebView
        webView = findViewById(R.id.webview);
        setupWebView();
        
        // Initialize PAX Hardware Bridge
        hardwareBridge = new PAXHardwareBridge(this, this);
        
        // Add JavaScript interface
        webView.addJavascriptInterface(hardwareBridge, "Android");
        
        // Load the React web app
        webView.loadUrl(WEB_APP_URL);
    }
    
    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        
        // Enable JavaScript
        webSettings.setJavaScriptEnabled(true);
        
        // Enable DOM storage
        webSettings.setDomStorageEnabled(true);
        
        // Enable local storage
        webSettings.setDatabaseEnabled(true);
        
        // Allow file access
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        
        // Enable mixed content (HTTP/HTTPS)
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        
        // Disable zoom controls
        webSettings.setBuiltInZoomControls(false);
        webSettings.setDisplayZoomControls(false);
        
        // Set user agent
        webSettings.setUserAgentString(webSettings.getUserAgentString() + " PAXTerminal/1.0");
        
        // Set WebView client to handle navigation
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                // Allow navigation within the same domain
                if (url.startsWith(WEB_APP_URL) || url.startsWith("http://localhost") || url.startsWith("https://localhost")) {
                    return false; // Let WebView handle it
                }
                // Block external navigation for security
                return true;
            }
            
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                
                // Inject PAX hardware detection script
                String jsCode = 
                    "window.PAXTerminal = true; " +
                    "window.PAXModel = 'A920 Pro'; " +
                    "console.log('PAX Terminal WebView loaded');";
                
                view.evaluateJavascript(jsCode, null);
            }
        });
        
        // Set WebChrome client for console logs and alerts
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public boolean onConsoleMessage(android.webkit.ConsoleMessage consoleMessage) {
                android.util.Log.d("WebView Console", 
                    consoleMessage.message() + " -- From line " + 
                    consoleMessage.lineNumber() + " of " + 
                    consoleMessage.sourceId());
                return true;
            }
        });
    }
    
    public WebView getWebView() {
        return webView;
    }
    
    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
    
    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        if (webView != null) {
            webView.onPause();
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        if (webView != null) {
            webView.onResume();
        }
    }
}
