import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { FastifyInstance } from 'fastify';
import { createTestApp, makeRequest, expectSuccessResponse, expectErrorResponse, expectValidationError, createMockTransaction } from '../../test/helpers';
import Transaction from '../../models/Transaction.mongo';

describe('Transaction Controller', () => {
  let app: FastifyInstance;

  beforeEach(async () => {
    app = await createTestApp();
    await app.ready();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('POST /api/v1/transaction', () => {
    it('should create transaction successfully', async () => {
      const transactionData = {
        amount: 1000,
        status: 'success',
        protocolCode: '101.1',
        metadata: { test: true },
      };

      const response = await makeRequest(app, 'POST', '/api/v1/transaction', transactionData);

      expectSuccessResponse(response);
      expect(response.body.data.transaction).toBeDefined();
      expect(response.body.data.transaction.amount).toBe(1000);
      expect(response.body.data.transaction.status).toBe('success');
      expect(response.body.data.receipt).toBeDefined();
    });

    it('should return validation error for invalid data', async () => {
      const invalidData = {
        amount: -100,
        status: 'invalid',
      };

      const response = await makeRequest(app, 'POST', '/api/v1/transaction', invalidData);

      expectValidationError(response);
    });

    it('should return validation error for missing required fields', async () => {
      const incompleteData = {
        amount: 1000,
      };

      const response = await makeRequest(app, 'POST', '/api/v1/transaction', incompleteData);

      expectValidationError(response);
    });
  });

  describe('GET /api/v1/transaction', () => {
    beforeEach(async () => {
      // Create test transactions
      const transactions = [
        createMockTransaction({ amount: 1000, status: 'success' }),
        createMockTransaction({ amount: 2000, status: 'failure' }),
        createMockTransaction({ amount: 3000, status: 'pending' }),
      ];

      for (const txn of transactions) {
        await new Transaction(txn).save();
      }
    });

    it('should return transactions with default pagination', async () => {
      const response = await makeRequest(app, 'GET', '/api/v1/transaction');

      expectSuccessResponse(response);
      expect(response.body.data.transactions).toHaveLength(3);
      expect(response.body.data.pagination).toBeDefined();
      expect(response.body.data.pagination.limit).toBe(50);
      expect(response.body.data.pagination.offset).toBe(0);
    });

    it('should respect limit parameter', async () => {
      const response = await makeRequest(app, 'GET', '/api/v1/transaction?limit=2');

      expectSuccessResponse(response);
      expect(response.body.data.transactions).toHaveLength(2);
      expect(response.body.data.pagination.limit).toBe(2);
    });

    it('should respect offset parameter', async () => {
      const response = await makeRequest(app, 'GET', '/api/v1/transaction?offset=1');

      expectSuccessResponse(response);
      expect(response.body.data.transactions).toHaveLength(2);
      expect(response.body.data.pagination.offset).toBe(1);
    });

    it('should return validation error for invalid limit', async () => {
      const response = await makeRequest(app, 'GET', '/api/v1/transaction?limit=invalid');

      expectValidationError(response);
    });

    it('should return validation error for negative offset', async () => {
      const response = await makeRequest(app, 'GET', '/api/v1/transaction?offset=-1');

      expectValidationError(response);
    });
  });

  describe('GET /api/v1/transaction/:id', () => {
    it('should return transaction by ID', async () => {
      const mockTxn = createMockTransaction();
      const savedTxn = await new Transaction(mockTxn).save();

      const response = await makeRequest(
        app,
        'GET',
        `/api/v1/transaction/${savedTxn._id.toString()}`
      );

      expectSuccessResponse(response);
      expect(response.body.data._id).toBe(savedTxn._id.toString());
      expect(response.body.data.amount).toBe(mockTxn.amount);
    });

    it('should return 404 for non-existent transaction', async () => {
      const response = await makeRequest(
        app,
        'GET',
        '/api/v1/transaction/507f1f77bcf86cd799439011'
      );

      expectErrorResponse(response, 404, 'NOT_FOUND');
    });

    it('should return validation error for invalid ID format', async () => {
      const response = await makeRequest(
        app,
        'GET',
        '/api/v1/transaction/invalid-id'
      );

      expectErrorResponse(response, 500, 'TRANSACTION_FETCH_ERROR');
    });

    it('should return validation error for missing ID', async () => {
      const response = await makeRequest(app, 'GET', '/api/v1/transaction/');

      expectErrorResponse(response, 404, 'NOT_FOUND');
    });
  });
});
