import { FastifyRequest, FastifyReply } from 'fastify';
import { transactionService } from '../services/transactionService';
import { z } from 'zod';
import { logger } from '../config/logger';
import axios from 'axios';
import { env } from '../config/env';

const controllerLogger = logger.child({ module: 'payment-controller' });

const simulatePaymentSchema = z.object({
  amount: z.number().positive().int(),
  status: z.enum(['success', 'failure']),
  protocolCode: z.enum(['101.1', '101.3', '101.5', '101.8']).optional(),
  metadata: z.record(z.any()).optional(),
});

export const simulatePayment = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const validatedBody = simulatePaymentSchema.parse(request.body);

    controllerLogger.info({
      amount: validatedBody.amount,
      status: validatedBody.status,
      protocolCode: validatedBody.protocolCode
    }, 'Simulating payment');

    const transaction = await transactionService.createTransaction({
      amount: validatedBody.amount,
      status: validatedBody.status,
      protocolCode: validatedBody.protocolCode,
      metadata: validatedBody.metadata,
    });

    if (validatedBody.protocolCode) {
      try {
        const bankMessage = {
          protocolEventCode: validatedBody.protocolCode,
          mti: '0200',
          transactionId: transaction._id.toString(),
          timestamp: new Date().toISOString(),
          de4_transactionAmount: validatedBody.amount,
          de39_responseCode: validatedBody.status === 'success' ? '00' : '05',
        };

        controllerLogger.info({ protocolCode: validatedBody.protocolCode }, 'Sending message to mock bank');

        const bankResponse = await axios.post(env.MOCK_BANK_URL, bankMessage, {
          timeout: 5000,
          headers: { 'Content-Type': 'application/json' }
        });

        controllerLogger.info({
          bankResponseCode: bankResponse.data?.data?.bankResponseCode
        }, 'Received response from mock bank');

      } catch (bankError) {
        controllerLogger.warn({ error: bankError }, 'Failed to communicate with mock bank');
      }
    }

    return reply.status(201).send({
      success: true,
      data: {
        transaction,
        message: `Payment ${validatedBody.status} simulation completed`
      }
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      controllerLogger.warn({ error: error.errors }, 'Invalid payment simulation request');
      return reply.status(400).send({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid request parameters',
        details: error.errors
      });
    }

    controllerLogger.error({ error }, 'Failed to simulate payment');
    return reply.status(500).send({
      success: false,
      error: 'PAYMENT_SIMULATION_ERROR',
      message: 'Failed to simulate payment'
    });
  }
};
