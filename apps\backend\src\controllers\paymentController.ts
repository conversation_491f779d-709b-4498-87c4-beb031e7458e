import { FastifyRequest, FastifyReply } from 'fastify';
import { createTransaction } from '../services/transactionService';
import { addLog } from '../services/logService';

export const simulatePayment = async (request: FastifyRequest, reply: FastifyReply) => {
  const { amount, status } = request.body as any;
  if (typeof amount !== 'number' || !['success', 'failure'].includes(status)) {
    return reply.status(400).send({ error: 'Invalid input' });
  }
  const txn = createTransaction(amount, status);
  addLog('stripe', `Simulated payment: ${status} for amount ${amount}`);
  return reply.send(txn);
};
