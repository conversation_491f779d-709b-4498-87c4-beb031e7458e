{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest run"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "zustand": "^4.5.2", "axios": "^1.6.8", "zod": "^3.22.4", "react-hook-form": "^7.51.2", "@hookform/resolvers": "^3.3.4"}, "devDependencies": {"typescript": "^5.4.5", "vite": "^5.2.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "vitest": "^1.5.0", "tailwindcss": "^3.4.3", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "@types/node": "^20.12.7", "@testing-library/react": "^14.2.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/user-event": "^14.5.2", "jsdom": "^24.0.0"}}