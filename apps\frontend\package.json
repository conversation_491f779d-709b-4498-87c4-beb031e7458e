{
  "name": "frontend",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview",
    "test": "vitest run"
  },
  "dependencies": {
    // To be added: react, react-dom, react-router-dom, zustand, axios, etc.
    "shared-types": "workspace:*"
  },
  "devDependencies": {
    "typescript": "^5.4.5",
    "vite": "^5.2.0",
    "@vitejs/plugin-react": "^4.2.1",
    "eslint": "^8.57.0",
    "eslint-config-custom": "workspace:*",
    "tsconfig-custom": "workspace:*",
    "@types/react": "^18.2.66",
    "@types/react-dom": "^18.2.22",
    "vitest": "^1.5.0", // For testing
    "tailwindcss": "^3.4.3" // Assuming Tailwind CSS
  }
}
