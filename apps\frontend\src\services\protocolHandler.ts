// PAX A920 Pro Protocol Handler
// Supports Active Protocols 101.1, 101.3, 101.5, 101.8
// Supports Offline Protocols 200, 201

export type ActiveProtocol = '101.1' | '101.3' | '101.5' | '101.8';
export type OfflineProtocol = '200' | '201';
export type Protocol = ActiveProtocol | OfflineProtocol;

export interface ProtocolConfig {
  code: Protocol;
  name: string;
  description: string;
  isOnline: boolean;
  supportedPaymentMethods: PaymentMethod[];
  requiresSignature: boolean;
  maxAmount?: number;
  minAmount?: number;
}

export type PaymentMethod = 
  | 'chip_and_pin'
  | 'chip_and_signature' 
  | 'contactless_chip'
  | 'contactless_mobile'
  | 'magnetic_stripe'
  | 'manual_entry'
  | 'nfc_tap'
  | 'mobile_wallet';

export interface ProtocolTransaction {
  id: string;
  protocol: Protocol;
  amount: number;
  paymentMethod: PaymentMethod;
  cardData: {
    maskedPan: string;
    cardType: string;
    entryMode: string;
    authMethod: string;
  };
  authCode: string;
  timestamp: Date;
  terminalId: string;
  merchantId: string;
  isOffline: boolean;
  needsUpload?: boolean;
}

// Protocol Configurations for PAX A920 Pro
export const PROTOCOL_CONFIGS: Record<Protocol, ProtocolConfig> = {
  // Active Online Protocols
  '101.1': {
    code: '101.1',
    name: 'Chip & PIN Authorization',
    description: 'EMV chip card with PIN verification',
    isOnline: true,
    supportedPaymentMethods: ['chip_and_pin'],
    requiresSignature: false,
    minAmount: 0,
    maxAmount: 999999
  },
  '101.3': {
    code: '101.3', 
    name: 'Contactless Authorization',
    description: 'NFC/Contactless payment authorization',
    isOnline: true,
    supportedPaymentMethods: ['contactless_chip', 'contactless_mobile', 'nfc_tap', 'mobile_wallet'],
    requiresSignature: false,
    minAmount: 0,
    maxAmount: 10000 // €100 contactless limit
  },
  '101.5': {
    code: '101.5',
    name: 'Chip & Signature Authorization', 
    description: 'EMV chip card with signature verification',
    isOnline: true,
    supportedPaymentMethods: ['chip_and_signature'],
    requiresSignature: true,
    minAmount: 0,
    maxAmount: 999999
  },
  '101.8': {
    code: '101.8',
    name: 'Magnetic Stripe Authorization',
    description: 'Traditional magnetic stripe card processing',
    isOnline: true,
    supportedPaymentMethods: ['magnetic_stripe', 'manual_entry'],
    requiresSignature: true,
    minAmount: 0,
    maxAmount: 999999
  },
  
  // Offline Protocols
  '200': {
    code: '200',
    name: 'Offline Chip Transaction',
    description: 'EMV offline transaction (stored for later upload)',
    isOnline: false,
    supportedPaymentMethods: ['chip_and_pin', 'chip_and_signature'],
    requiresSignature: false,
    minAmount: 0,
    maxAmount: 5000 // €50 offline limit
  },
  '201': {
    code: '201',
    name: 'Offline Contactless Transaction',
    description: 'Contactless offline transaction (stored for later upload)',
    isOnline: false,
    supportedPaymentMethods: ['contactless_chip', 'nfc_tap'],
    requiresSignature: false,
    minAmount: 0,
    maxAmount: 2500 // €25 offline contactless limit
  }
};

export class ProtocolHandler {
  private offlineTransactions: ProtocolTransaction[] = [];
  
  // Determine appropriate protocol based on payment method and amount
  public selectProtocol(
    paymentMethod: PaymentMethod, 
    amount: number,
    isOnlineAvailable: boolean = true
  ): Protocol {
    // If offline, use offline protocols
    if (!isOnlineAvailable) {
      if (['contactless_chip', 'nfc_tap'].includes(paymentMethod) && amount <= 2500) {
        return '201';
      }
      if (['chip_and_pin', 'chip_and_signature'].includes(paymentMethod) && amount <= 5000) {
        return '200';
      }
      // Fallback to online protocols even if offline
    }
    
    // Online protocol selection
    switch (paymentMethod) {
      case 'chip_and_pin':
        return '101.1';
      case 'contactless_chip':
      case 'contactless_mobile':
      case 'nfc_tap':
      case 'mobile_wallet':
        return amount <= 10000 ? '101.3' : '101.1'; // Above contactless limit, use chip & PIN
      case 'chip_and_signature':
        return '101.5';
      case 'magnetic_stripe':
      case 'manual_entry':
        return '101.8';
      default:
        return '101.1'; // Default to chip & PIN
    }
  }
  
  // Validate transaction against protocol rules
  public validateTransaction(
    protocol: Protocol,
    paymentMethod: PaymentMethod,
    amount: number
  ): { valid: boolean; error?: string } {
    const config = PROTOCOL_CONFIGS[protocol];
    
    if (!config.supportedPaymentMethods.includes(paymentMethod)) {
      return {
        valid: false,
        error: `Payment method ${paymentMethod} not supported by protocol ${protocol}`
      };
    }
    
    if (config.minAmount && amount < config.minAmount) {
      return {
        valid: false,
        error: `Amount below minimum for protocol ${protocol}`
      };
    }
    
    if (config.maxAmount && amount > config.maxAmount) {
      return {
        valid: false,
        error: `Amount exceeds maximum for protocol ${protocol}`
      };
    }
    
    return { valid: true };
  }
  
  // Process transaction with protocol
  public async processTransaction(
    protocol: Protocol,
    amount: number,
    paymentMethod: PaymentMethod,
    cardData: any
  ): Promise<ProtocolTransaction> {
    const config = PROTOCOL_CONFIGS[protocol];
    
    // Validate transaction
    const validation = this.validateTransaction(protocol, paymentMethod, amount);
    if (!validation.valid) {
      throw new Error(validation.error);
    }
    
    const transaction: ProtocolTransaction = {
      id: this.generateTransactionId(),
      protocol,
      amount,
      paymentMethod,
      cardData: {
        maskedPan: this.maskCardNumber(cardData.pan || ''),
        cardType: cardData.cardType || 'UNKNOWN',
        entryMode: this.getEntryMode(paymentMethod),
        authMethod: this.getAuthMethod(protocol)
      },
      authCode: this.generateAuthCode(),
      timestamp: new Date(),
      terminalId: '1853944350', // Your actual terminal serial
      merchantId: 'MERCHANT_001',
      isOffline: !config.isOnline,
      needsUpload: !config.isOnline
    };
    
    // Store offline transactions
    if (!config.isOnline) {
      this.offlineTransactions.push(transaction);
    }
    
    return transaction;
  }
  
  // Get offline transactions for upload
  public getOfflineTransactions(): ProtocolTransaction[] {
    return this.offlineTransactions.filter(t => t.needsUpload);
  }
  
  // Mark offline transactions as uploaded
  public markTransactionsUploaded(transactionIds: string[]): void {
    this.offlineTransactions.forEach(t => {
      if (transactionIds.includes(t.id)) {
        t.needsUpload = false;
      }
    });
  }
  
  // Generate receipt text with protocol information
  public generateProtocolReceipt(transaction: ProtocolTransaction): string {
    const config = PROTOCOL_CONFIGS[transaction.protocol];
    
    return `
================================
       PAX A920 Pro Terminal
================================

Date: ${transaction.timestamp.toLocaleDateString()}
Time: ${transaction.timestamp.toLocaleTimeString()}
Terminal SN: ${transaction.terminalId}
Merchant ID: ${transaction.merchantId}

--------------------------------
TRANSACTION DETAILS
--------------------------------

Amount: $${(transaction.amount / 100).toFixed(2)}
Card: ${transaction.cardData.maskedPan}
Card Type: ${transaction.cardData.cardType}
Entry Mode: ${transaction.cardData.entryMode}

--------------------------------
PROTOCOL INFORMATION
--------------------------------

Protocol: ${transaction.protocol}
Name: ${config.name}
Auth Method: ${transaction.cardData.authMethod}
Auth Code: ${transaction.authCode}
Transaction ID: ${transaction.id}

Status: ${transaction.isOffline ? 'OFFLINE APPROVED' : 'APPROVED'}
${transaction.isOffline ? 'Will be uploaded when online' : 'Real-time authorization'}

${config.requiresSignature ? 'SIGNATURE REQUIRED' : ''}

--------------------------------
Thank you for your business!
--------------------------------

${transaction.isOffline ? 'Offline' : 'Customer'} Copy
`;
  }
  
  private generateTransactionId(): string {
    return `PAX_${Date.now()}_${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
  }
  
  private generateAuthCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }
  
  private maskCardNumber(pan: string): string {
    if (pan.length < 8) return '****';
    return `****${pan.slice(-4)}`;
  }
  
  private getEntryMode(paymentMethod: PaymentMethod): string {
    switch (paymentMethod) {
      case 'chip_and_pin':
      case 'chip_and_signature':
        return 'CHIP';
      case 'contactless_chip':
      case 'contactless_mobile':
      case 'nfc_tap':
        return 'CONTACTLESS';
      case 'mobile_wallet':
        return 'MOBILE_WALLET';
      case 'magnetic_stripe':
        return 'SWIPE';
      case 'manual_entry':
        return 'MANUAL';
      default:
        return 'UNKNOWN';
    }
  }
  
  private getAuthMethod(protocol: Protocol): string {
    switch (protocol) {
      case '101.1':
        return 'PIN';
      case '101.3':
        return 'CONTACTLESS';
      case '101.5':
        return 'SIGNATURE';
      case '101.8':
        return 'SIGNATURE';
      case '200':
        return 'OFFLINE_PIN';
      case '201':
        return 'OFFLINE_CONTACTLESS';
      default:
        return 'UNKNOWN';
    }
  }
}

// Export singleton instance
export const protocolHandler = new ProtocolHandler();
