# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.14.5":
  "integrity" "sha512-9pzDqyc6OLDaqe+zbACgFkb6fKMNG6CObKpnYXChRsvYGyEdc7CA2BaqeOM+vOtCS5ndmJicPJhKAwYRI6UfFw=="
  "resolved" "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/highlight" "^7.14.5"

"@babel/code-frame@7.12.11":
  "integrity" "sha512-Zt1yodBx1UcyiePMSkWnU4hPqhwq7hGi2nFL1LeA3EUl+q2LQx16MISgJ0+z7dnmgvP9QtIleuETGOiOH1RcIw=="
  "resolved" "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.12.11.tgz"
  "version" "7.12.11"
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.15.0":
  "integrity" "sha512-0NqAC1IJE0S0+lL1SWFMxMkz1pKCNCjI4tr2Zx4LJSXxCLAdr6KyArnY+sno5m3yH9g737ygOyPABDsnXkpxiA=="
  "resolved" "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.15.0.tgz"
  "version" "7.15.0"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.1.0", "@babel/core@^7.1.6", "@babel/core@^7.12.0", "@babel/core@^7.13.0", "@babel/core@^7.14.0", "@babel/core@^7.15.5", "@babel/core@^7.4.0-0", "@babel/core@^7.7.2", "@babel/core@^7.7.5", "@babel/core@^7.8.0":
  "integrity" "sha512-pYgXxiwAgQpgM1bNkZsDEq85f0ggXMA5L7c+o3tskGMh2BunCI9QUwB9Z4jpvXUOuMdyGKiGKQiRe11VS6Jzvg=="
  "resolved" "https://registry.npmjs.org/@babel/core/-/core-7.15.5.tgz"
  "version" "7.15.5"
  dependencies:
    "@babel/code-frame" "^7.14.5"
    "@babel/generator" "^7.15.4"
    "@babel/helper-compilation-targets" "^7.15.4"
    "@babel/helper-module-transforms" "^7.15.4"
    "@babel/helpers" "^7.15.4"
    "@babel/parser" "^7.15.5"
    "@babel/template" "^7.15.4"
    "@babel/traverse" "^7.15.4"
    "@babel/types" "^7.15.4"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.1.2"
    "semver" "^6.3.0"
    "source-map" "^0.5.0"

"@babel/generator@^7.14.0", "@babel/generator@^7.15.4", "@babel/generator@^7.7.2":
  "integrity" "sha512-d3itta0tu+UayjEORPNz6e1T3FtvWlP5N4V5M+lhp/CxT4oAA7/NcScnpRyspUMLK6tu9MNHmQHxRykuN2R7hw=="
  "resolved" "https://registry.npmjs.org/@babel/generator/-/generator-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/types" "^7.15.4"
    "jsesc" "^2.5.1"
    "source-map" "^0.5.0"

"@babel/helper-annotate-as-pure@^7.14.5", "@babel/helper-annotate-as-pure@^7.15.4":
  "integrity" "sha512-QwrtdNvUNsPCj2lfNQacsGSQvGX8ee1ttrBrcozUP2Sv/jylewBP/8QFe6ZkBsC8T/GYWonNAWJV4aRR9AL2DA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/types" "^7.15.4"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.14.5":
  "integrity" "sha512-P8o7JP2Mzi0SdC6eWr1zF+AEYvrsZa7GSY1lTayjF5XJhVH0kjLYUZPvTMflP7tBgZoe9gIhTa60QwFpqh/E0Q=="
  "resolved" "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.15.4"
    "@babel/types" "^7.15.4"

"@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.15.4":
  "integrity" "sha512-rMWPCirulnPSe4d+gwdWXLfAXTTBj8M3guAf5xFQJ0nvFY7tfNAFnWdqaHegHlgDZOCT4qvhF3BYlSJag8yhqQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/compat-data" "^7.15.0"
    "@babel/helper-validator-option" "^7.14.5"
    "browserslist" "^4.16.6"
    "semver" "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.14.5", "@babel/helper-create-class-features-plugin@^7.15.4":
  "integrity" "sha512-7ZmzFi+DwJx6A7mHRwbuucEYpyBwmh2Ca0RvI6z2+WLZYCqV0JOaLb+u0zbtmDicebgKBZgqbYfLaKNqSgv5Pw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.15.4"
    "@babel/helper-function-name" "^7.15.4"
    "@babel/helper-member-expression-to-functions" "^7.15.4"
    "@babel/helper-optimise-call-expression" "^7.15.4"
    "@babel/helper-replace-supers" "^7.15.4"
    "@babel/helper-split-export-declaration" "^7.15.4"

"@babel/helper-create-regexp-features-plugin@^7.14.5":
  "integrity" "sha512-TLawwqpOErY2HhWbGJ2nZT5wSkR192QpN+nBg1THfBfftrlvOh+WbhrxXCH4q4xJ9Gl16BGPR/48JA+Ryiho/A=="
  "resolved" "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.14.5"
    "regexpu-core" "^4.7.1"

"@babel/helper-define-polyfill-provider@^0.2.2":
  "integrity" "sha512-RH3QDAfRMzj7+0Nqu5oqgO5q9mFtQEVvCRsi8qCEfzLR9p2BHfn5FzhSB2oj1fF7I2+DcTORkYaQ6aTR9Cofew=="
  "resolved" "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.2.3.tgz"
  "version" "0.2.3"
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"
    "semver" "^6.1.2"

"@babel/helper-explode-assignable-expression@^7.15.4":
  "integrity" "sha512-J14f/vq8+hdC2KoWLIQSsGrC9EFBKE4NFts8pfMpymfApds+fPqR30AOUWc4tyr56h9l/GA1Sxv2q3dLZWbQ/g=="
  "resolved" "https://registry.npmjs.org/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/types" "^7.15.4"

"@babel/helper-function-name@^7.14.5", "@babel/helper-function-name@^7.15.4":
  "integrity" "sha512-Z91cOMM4DseLIGOnog+Z8OI6YseR9bua+HpvLAQ2XayUGU+neTtX+97caALaLdyu53I/fjhbeCnWnRH1O3jFOw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-get-function-arity" "^7.15.4"
    "@babel/template" "^7.15.4"
    "@babel/types" "^7.15.4"

"@babel/helper-get-function-arity@^7.15.4":
  "integrity" "sha512-1/AlxSF92CmGZzHnC515hm4SirTxtpDnLEJ0UyEMgTMZN+6bxXKg04dKhiRx5Enel+SUA1G1t5Ed/yQia0efrA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/types" "^7.15.4"

"@babel/helper-hoist-variables@^7.15.4":
  "integrity" "sha512-VTy085egb3jUGVK9ycIxQiPbquesq0HUQ+tPO0uv5mPEBZipk+5FkRKiWq5apuyTE9FUrjENB0rCf8y+n+UuhA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/types" "^7.15.4"

"@babel/helper-member-expression-to-functions@^7.15.4":
  "integrity" "sha512-cokOMkxC/BTyNP1AlY25HuBWM32iCEsLPI4BHDpJCHHm1FU2E7dKWWIXJgQgSFiu4lp8q3bL1BIKwqkSUviqtA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/types" "^7.15.4"

"@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.14.5", "@babel/helper-module-imports@^7.15.4":
  "integrity" "sha512-jeAHZbzUwdW/xHgHQ3QmWR4Jg6j15q4w/gCfwZvtqOxoo5DKtLHk8Bsf4c5RZRC7NmLEs+ohkdq8jFefuvIxAA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/types" "^7.15.4"

"@babel/helper-module-transforms@^7.14.5", "@babel/helper-module-transforms@^7.15.4":
  "integrity" "sha512-9fHHSGE9zTC++KuXLZcB5FKgvlV83Ox+NLUmQTawovwlJ85+QMhk1CnVk406CQVj97LaWod6KVjl2Sfgw9Aktw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-module-imports" "^7.15.4"
    "@babel/helper-replace-supers" "^7.15.4"
    "@babel/helper-simple-access" "^7.15.4"
    "@babel/helper-split-export-declaration" "^7.15.4"
    "@babel/helper-validator-identifier" "^7.14.9"
    "@babel/template" "^7.15.4"
    "@babel/traverse" "^7.15.4"
    "@babel/types" "^7.15.4"

"@babel/helper-optimise-call-expression@^7.15.4":
  "integrity" "sha512-E/z9rfbAOt1vDW1DR7k4SzhzotVV5+qMciWV6LaG1g4jeFrkDlJedjtV4h0i4Q/ITnUu+Pk08M7fczsB9GXBDw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/types" "^7.15.4"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "integrity" "sha512-/37qQCE3K0vvZKwoK4XU/irIJQdIfCJuhU5eKnNxpFDsOkgFaUAwbv+RYw6eYgsC0E4hS7r5KqGULUogqui0fQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz"
  "version" "7.14.5"

"@babel/helper-remap-async-to-generator@^7.14.5", "@babel/helper-remap-async-to-generator@^7.15.4":
  "integrity" "sha512-v53MxgvMK/HCwckJ1bZrq6dNKlmwlyRNYM6ypaRTdXWGOE2c1/SCa6dL/HimhPulGhZKw9W0QhREM583F/t0vQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.15.4"
    "@babel/helper-wrap-function" "^7.15.4"
    "@babel/types" "^7.15.4"

"@babel/helper-replace-supers@^7.14.5", "@babel/helper-replace-supers@^7.15.4":
  "integrity" "sha512-/ztT6khaXF37MS47fufrKvIsiQkx1LBRvSJNzRqmbyeZnTwU9qBxXYLaaT/6KaxfKhjs2Wy8kG8ZdsFUuWBjzw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.15.4"
    "@babel/helper-optimise-call-expression" "^7.15.4"
    "@babel/traverse" "^7.15.4"
    "@babel/types" "^7.15.4"

"@babel/helper-simple-access@^7.15.4":
  "integrity" "sha512-UzazrDoIVOZZcTeHHEPYrr1MvTR/K+wgLg6MY6e1CJyaRhbibftF6fR2KU2sFRtI/nERUZR9fBd6aKgBlIBaPg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/types" "^7.15.4"

"@babel/helper-skip-transparent-expression-wrappers@^7.14.5", "@babel/helper-skip-transparent-expression-wrappers@^7.15.4":
  "integrity" "sha512-BMRLsdh+D1/aap19TycS4eD1qELGrCBJwzaY9IE8LrpJtJb+H7rQkPIdsfgnMtLBA6DJls7X9z93Z4U8h7xw0A=="
  "resolved" "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/types" "^7.15.4"

"@babel/helper-split-export-declaration@^7.15.4":
  "integrity" "sha512-HsFqhLDZ08DxCpBdEVtKmywj6PQbwnF6HHybur0MAnkAKnlS6uHkwnmRIkElB2Owpfb4xL4NwDmDLFubueDXsw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/types" "^7.15.4"

"@babel/helper-validator-identifier@^7.14.5", "@babel/helper-validator-identifier@^7.14.9":
  "integrity" "sha512-pQYxPY0UP6IHISRitNe8bsijHex4TWZXi2HwKVsjPiltzlhse2znVcm9Ace510VT1kxIHjGJCZZQBX2gJDbo0g=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.14.9.tgz"
  "version" "7.14.9"

"@babel/helper-validator-option@^7.14.5":
  "integrity" "sha512-OX8D5eeX4XwcroVW45NMvoYaIuFI+GQpA2a8Gi+X/U/cDUIRsV37qQfF905F0htTRCREQIB4KqPeaveRJUl3Ow=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.14.5.tgz"
  "version" "7.14.5"

"@babel/helper-wrap-function@^7.15.4":
  "integrity" "sha512-Y2o+H/hRV5W8QhIfTpRIBwl57y8PrZt6JM3V8FOo5qarjshHItyH5lXlpMfBfmBefOqSCpKZs/6Dxqp0E/U+uw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-function-name" "^7.15.4"
    "@babel/template" "^7.15.4"
    "@babel/traverse" "^7.15.4"
    "@babel/types" "^7.15.4"

"@babel/helpers@^7.15.4":
  "integrity" "sha512-V45u6dqEJ3w2rlryYYXf6i9rQ5YMNu4FLS6ngs8ikblhu2VdR1AqAd6aJjBzmf2Qzh6KOLqKHxEN9+TFbAkAVQ=="
  "resolved" "https://registry.npmjs.org/@babel/helpers/-/helpers-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/template" "^7.15.4"
    "@babel/traverse" "^7.15.4"
    "@babel/types" "^7.15.4"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.14.5":
  "integrity" "sha512-qf9u2WFWVV0MppaL877j2dBtQIDgmidgjGk5VIMw3OadXvYaXn66U1BFlH2t4+t3i+8PhedppRv+i40ABzd+gg=="
  "resolved" "https://registry.npmjs.org/@babel/highlight/-/highlight-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.5"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.1.6", "@babel/parser@^7.14.0", "@babel/parser@^7.15.4", "@babel/parser@^7.15.5", "@babel/parser@^7.7.0", "@babel/parser@^7.7.2":
  "integrity" "sha512-S/TSCcsRuCkmpUuoWijua0Snt+f3ewU/8spLo+4AXJCZfT0bVCzLD5MuOKdrx0mlAptbKzn5AdgEIIKXxXkz9Q=="
  "resolved" "https://registry.npmjs.org/@babel/parser/-/parser-7.15.6.tgz"
  "version" "7.15.6"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.15.4":
  "integrity" "sha512-eBnpsl9tlhPhpI10kU06JHnrYXwg3+V6CaP2idsCXNef0aeslpqyITXQ74Vfk5uHgY7IG7XP0yIH8b42KSzHog=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.15.4"
    "@babel/plugin-proposal-optional-chaining" "^7.14.5"

"@babel/plugin-proposal-async-generator-functions@^7.15.4":
  "integrity" "sha512-2zt2g5vTXpMC3OmK6uyjvdXptbhBXfA77XGrd3gh93zwG8lZYBLOBImiGBEG0RANu3JqKEACCz5CGk73OJROBw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-remap-async-to-generator" "^7.15.4"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.0.0", "@babel/plugin-proposal-class-properties@^7.1.0", "@babel/plugin-proposal-class-properties@^7.14.5":
  "integrity" "sha512-q/PLpv5Ko4dVc1LYMpCY7RVAAO4uk55qPwrIuJ5QJ8c6cVuAmhu7I/49JOppXL6gXf7ZHzpRVEUZdYoPLM04Gg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-proposal-class-static-block@^7.15.4":
  "integrity" "sha512-M682XWrrLNk3chXCjoPUQWOyYsB93B9z3mRyjtqqYJWDf2mfCdIYgDrA11cgNVhAQieaq6F2fn2f3wI0U4aTjA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.15.4"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-dynamic-import@^7.14.5":
  "integrity" "sha512-ExjiNYc3HDN5PXJx+bwC50GIx/KKanX2HiggnIUAYedbARdImiCU4RhhHfdf0Kd7JNXGpsBBBCOm+bBVy3Gb0g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-default-from@^7.0.0":
  "integrity" "sha512-T8KZ5abXvKMjF6JcoXjgac3ElmXf0AWzJwi2O/42Jk+HmCky3D9+i1B7NPP1FblyceqTevKeV/9szeikFoaMDg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-export-default-from/-/plugin-proposal-export-default-from-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-export-default-from" "^7.14.5"

"@babel/plugin-proposal-export-namespace-from@^7.14.5":
  "integrity" "sha512-g5POA32bXPMmSBu5Dx/iZGLGnKmKPc5AiY7qfZgurzrCYgIztDlHFbznSNCoQuv57YQLnQfaDi7dxCtLDIdXdA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.14.5":
  "integrity" "sha512-NSq2fczJYKVRIsUJyNxrVUMhB27zb7N7pOFGQOhBKJrChbGcgEAqyZrmZswkPk18VMurEeJAaICbfm57vUeTbQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.14.5":
  "integrity" "sha512-YGn2AvZAo9TwyhlLvCCWxD90Xq8xJ4aSgaX3G5D/8DW94L8aaT+dS5cSP+Z06+rCJERGSr9GxMBZ601xoc2taw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator@^7.1.0", "@babel/plugin-proposal-nullish-coalescing-operator@^7.14.5":
  "integrity" "sha512-gun/SOnMqjSb98Nkaq2rTKMwervfdAoz6NphdY0vTfuzMfryj+tDGb2n6UkDKwez+Y8PZDhE3D143v6Gepp4Hg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.14.5":
  "integrity" "sha512-yiclALKe0vyZRZE0pS6RXgjUOt87GWv6FYa5zqj15PvhOGFO69R5DusPlgK/1K5dVnCtegTiWu9UaBSrLLJJBg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.0.0", "@babel/plugin-proposal-object-rest-spread@^7.15.6":
  "integrity" "sha512-qtOHo7A1Vt+O23qEAX+GdBpqaIuD3i9VRrWgCJeq7WO6H2d14EK3q11urj5Te2MAeK97nMiIdRpwd/ST4JFbNg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.15.6.tgz"
  "version" "7.15.6"
  dependencies:
    "@babel/compat-data" "^7.15.0"
    "@babel/helper-compilation-targets" "^7.15.4"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.15.4"

"@babel/plugin-proposal-optional-catch-binding@^7.0.0", "@babel/plugin-proposal-optional-catch-binding@^7.14.5":
  "integrity" "sha512-3Oyiixm0ur7bzO5ybNcZFlmVsygSIQgdOa7cTfOYCMY+wEPAYhZAJxi3mixKFCTCKUhQXuCTtQ1MzrpL3WT8ZQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.0.0", "@babel/plugin-proposal-optional-chaining@^7.1.0", "@babel/plugin-proposal-optional-chaining@^7.14.5":
  "integrity" "sha512-ycz+VOzo2UbWNI1rQXxIuMOzrDdHGrI23fRiz/Si2R4kv2XZQ1BK8ccdHwehMKBlcH/joGW/tzrUmo67gbJHlQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.14.5"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.14.5":
  "integrity" "sha512-838DkdUA1u+QTCplatfq4B7+1lnDa/+QMI89x5WZHBcnNv+47N8QEj2k9I2MUU9xIv8XJ4XvPCviM/Dj7Uwt9g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-proposal-private-property-in-object@^7.15.4":
  "integrity" "sha512-X0UTixkLf0PCCffxgu5/1RQyGGbgZuKoI+vXP4iSbJSYwPb7hu06omsFGBvQ9lJEvwgrxHdS8B5nbfcd8GyUNA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.15.4"
    "@babel/helper-create-class-features-plugin" "^7.15.4"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.14.5", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  "integrity" "sha512-6axIeOU5LnY471KenAB9vI8I5j7NQ2d652hIYwVyRfgaZT5UpiqFKCuVXCDMSrU+3VFafnu2c5m3lrWIlr6A5Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  "integrity" "sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.0.0", "@babel/plugin-syntax-class-properties@^7.12.13", "@babel/plugin-syntax-class-properties@^7.8.3":
  "integrity" "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  "integrity" "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.0.0", "@babel/plugin-syntax-dynamic-import@^7.8.3":
  "integrity" "sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-default-from@^7.0.0", "@babel/plugin-syntax-export-default-from@^7.14.5":
  "integrity" "sha512-snWDxjuaPEobRBnhpqEfZ8RMxDbHt8+87fiEioGuE+Uc0xAKgSD8QiuL3lF93hPVQfZFAcYwrrf+H5qUhike3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-export-default-from/-/plugin-syntax-export-default-from-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  "integrity" "sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-flow@^7.0.0", "@babel/plugin-syntax-flow@^7.14.5", "@babel/plugin-syntax-flow@^7.2.0":
  "integrity" "sha512-9WK5ZwKCdWHxVuU13XNT6X73FGmutAXeor5lGFq6qhOFtMFUF4jkbijuyUdZZlpYq6E2hZeZf/u3959X9wsv0Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-import-meta@^7.8.3":
  "integrity" "sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.14.5":
  "integrity" "sha512-ohuFIsOMXJnbOMRfX7/w7LocdR6R7whhuRD4ax8IipLcLPlZGJKkBxgHp++U4N/vKyU16/YDQr2f5seajD3jIw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4", "@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  "integrity" "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.0.0", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4", "@babel/plugin-syntax-numeric-separator@^7.8.3":
  "integrity" "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.0.0", "@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.0.0", "@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  "integrity" "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5", "@babel/plugin-syntax-top-level-await@^7.8.3":
  "integrity" "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.14.5", "@babel/plugin-syntax-typescript@^7.7.2":
  "integrity" "sha512-u6OXzDaIXjEstBRRoBCQ/uKQKlbuaeE5in0RvWdA4pN6AhqxTIwUsnHPU1CFZA/amYObMsuWhYfRl3Ch90HD0Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-arrow-functions@^7.0.0", "@babel/plugin-transform-arrow-functions@^7.14.5":
  "integrity" "sha512-KOnO0l4+tD5IfOdi4x8C1XmEIRWUjNRV8wc6K2vz/3e8yAOoZZvsRXRRIF/yo/MAOFb4QjtAw9xSxMXbSMRy8A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-async-to-generator@^7.0.0", "@babel/plugin-transform-async-to-generator@^7.14.5":
  "integrity" "sha512-szkbzQ0mNk0rpu76fzDdqSyPu0MuvpXgC+6rz5rpMb5OIRxdmHfQxrktL8CYolL2d8luMCZTR0DpIMIdL27IjA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-module-imports" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-remap-async-to-generator" "^7.14.5"

"@babel/plugin-transform-block-scoped-functions@^7.0.0", "@babel/plugin-transform-block-scoped-functions@^7.14.5":
  "integrity" "sha512-dtqWqdWZ5NqBX3KzsVCWfQI3A53Ft5pWFCT2eCVUftWZgjc5DpDponbIF1+c+7cSGk2wN0YK7HGL/ezfRbpKBQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-block-scoping@^7.0.0", "@babel/plugin-transform-block-scoping@^7.15.3":
  "integrity" "sha512-nBAzfZwZb4DkaGtOes1Up1nOAp9TDRRFw4XBzBBSG9QK7KVFmYzgj9o9sbPv7TX5ofL4Auq4wZnxCoPnI/lz2Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.15.3.tgz"
  "version" "7.15.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-classes@^7.0.0", "@babel/plugin-transform-classes@^7.15.4":
  "integrity" "sha512-Yjvhex8GzBmmPQUvpXRPWQ9WnxXgAFuZSrqOK/eJlOGIXwvv8H3UEdUigl1gb/bnjTrln+e8bkZUYCBt/xYlBg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.15.4"
    "@babel/helper-function-name" "^7.15.4"
    "@babel/helper-optimise-call-expression" "^7.15.4"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-replace-supers" "^7.15.4"
    "@babel/helper-split-export-declaration" "^7.15.4"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.0.0", "@babel/plugin-transform-computed-properties@^7.14.5":
  "integrity" "sha512-pWM+E4283UxaVzLb8UBXv4EIxMovU4zxT1OPnpHJcmnvyY9QbPPTKZfEj31EUvG3/EQRbYAGaYEUZ4yWOBC2xg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-destructuring@^7.0.0", "@babel/plugin-transform-destructuring@^7.14.7":
  "integrity" "sha512-0mDE99nK+kVh3xlc5vKwB6wnP9ecuSj+zQCa/n0voENtP/zymdT4HH6QEb65wjjcbqr1Jb/7z9Qp7TF5FtwYGw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.14.7.tgz"
  "version" "7.14.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-dotall-regex@^7.14.5", "@babel/plugin-transform-dotall-regex@^7.4.4":
  "integrity" "sha512-loGlnBdj02MDsFaHhAIJzh7euK89lBrGIdM9EAtHFo6xKygCUGuuWe07o1oZVk287amtW1n0808sQM99aZt3gw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-duplicate-keys@^7.14.5":
  "integrity" "sha512-iJjbI53huKbPDAsJ8EmVmvCKeeq21bAze4fu9GBQtSLqfvzj2oRuHVx4ZkDwEhg1htQ+5OBZh/Ab0XDf5iBZ7A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-exponentiation-operator@^7.0.0", "@babel/plugin-transform-exponentiation-operator@^7.14.5":
  "integrity" "sha512-jFazJhMBc9D27o9jDnIE5ZErI0R0m7PbKXVq77FFvqFbzvTMuv8jaAwLZ5PviOLSFttqKIW0/wxNSDbjLk0tYA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-flow-strip-types@^7.0.0", "@babel/plugin-transform-flow-strip-types@^7.14.5":
  "integrity" "sha512-KhcolBKfXbvjwI3TV7r7TkYm8oNXHNBqGOy6JDVwtecFaRoKYsUUqJdS10q0YDKW1c6aZQgO+Ys3LfGkox8pXA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-flow" "^7.14.5"

"@babel/plugin-transform-for-of@^7.0.0", "@babel/plugin-transform-for-of@^7.15.4":
  "integrity" "sha512-DRTY9fA751AFBDh2oxydvVm4SYevs5ILTWLs6xKXps4Re/KG5nfUkr+TdHCrRWB8C69TlzVgA9b3RmGWmgN9LA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-function-name@^7.0.0", "@babel/plugin-transform-function-name@^7.14.5":
  "integrity" "sha512-vbO6kv0fIzZ1GpmGQuvbwwm+O4Cbm2NrPzwlup9+/3fdkuzo1YqOZcXw26+YUJB84Ja7j9yURWposEHLYwxUfQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-function-name" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-literals@^7.0.0", "@babel/plugin-transform-literals@^7.14.5":
  "integrity" "sha512-ql33+epql2F49bi8aHXxvLURHkxJbSmMKl9J5yHqg4PLtdE6Uc48CH1GS6TQvZ86eoB/ApZXwm7jlA+B3kra7A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-member-expression-literals@^7.0.0", "@babel/plugin-transform-member-expression-literals@^7.14.5":
  "integrity" "sha512-WkNXxH1VXVTKarWFqmso83xl+2V3Eo28YY5utIkbsmXoItO8Q3aZxN4BTS2k0hz9dGUloHK26mJMyQEYfkn/+Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-modules-amd@^7.14.5":
  "integrity" "sha512-3lpOU8Vxmp3roC4vzFpSdEpGUWSMsHFreTWOMMLzel2gNGfHE5UWIh/LN6ghHs2xurUp4jRFYMUIZhuFbody1g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-module-transforms" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.0.0", "@babel/plugin-transform-modules-commonjs@^7.1.0", "@babel/plugin-transform-modules-commonjs@^7.15.4":
  "integrity" "sha512-qg4DPhwG8hKp4BbVDvX1s8cohM8a6Bvptu4l6Iingq5rW+yRUAhe/YRup/YcW2zCOlrysEWVhftIcKzrEZv3sA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-module-transforms" "^7.15.4"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-simple-access" "^7.15.4"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.15.4":
  "integrity" "sha512-fJUnlQrl/mezMneR72CKCgtOoahqGJNVKpompKwzv3BrEXdlPspTcyxrZ1XmDTIr9PpULrgEQo3qNKp6dW7ssw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-hoist-variables" "^7.15.4"
    "@babel/helper-module-transforms" "^7.15.4"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-identifier" "^7.14.9"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.14.5":
  "integrity" "sha512-RfPGoagSngC06LsGUYyM9QWSXZ8MysEjDJTAea1lqRjNECE3y0qIJF/qbvJxc4oA4s99HumIMdXOrd+TdKaAAA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-module-transforms" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-named-capturing-groups-regex@^7.14.9":
  "integrity" "sha512-l666wCVYO75mlAtGFfyFwnWmIXQm3kSH0C3IRnJqWcZbWkoihyAdDhFm2ZWaxWTqvBvhVFfJjMRQ0ez4oN1yYA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.14.9.tgz"
  "version" "7.14.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.14.5"

"@babel/plugin-transform-new-target@^7.14.5":
  "integrity" "sha512-Nx054zovz6IIRWEB49RDRuXGI4Gy0GMgqG0cII9L3MxqgXz/+rgII+RU58qpo4g7tNEx1jG7rRVH4ihZoP4esQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-object-assign@^7.0.0":
  "integrity" "sha512-lvhjk4UN9xJJYB1mI5KC0/o1D5EcJXdbhVe+4fSk08D6ZN+iuAIs7LJC+71h8av9Ew4+uRq9452v9R93SFmQlQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-object-assign/-/plugin-transform-object-assign-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-object-super@^7.0.0", "@babel/plugin-transform-object-super@^7.14.5":
  "integrity" "sha512-MKfOBWzK0pZIrav9z/hkRqIk/2bTv9qvxHzPQc12RcVkMOzpIKnFCNYJip00ssKWYkd8Sf5g0Wr7pqJ+cmtuFg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-replace-supers" "^7.14.5"

"@babel/plugin-transform-parameters@^7.0.0", "@babel/plugin-transform-parameters@^7.15.4":
  "integrity" "sha512-9WB/GUTO6lvJU3XQsSr6J/WKvBC2hcs4Pew8YxZagi6GkTdniyqp8On5kqdK8MN0LMeu0mGbhPN+O049NV/9FQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-property-literals@^7.0.0", "@babel/plugin-transform-property-literals@^7.14.5":
  "integrity" "sha512-r1uilDthkgXW8Z1vJz2dKYLV1tuw2xsbrp3MrZmD99Wh9vsfKoob+JTgri5VUb/JqyKRXotlOtwgu4stIYCmnw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-display-name@^7.0.0":
  "integrity" "sha512-yQZ/i/pUCJAHI/LbtZr413S3VT26qNrEm0M5RRxQJA947/YNYwbZbBaXGDrq6CG5QsZycI1VIP6d7pQaBfP+8Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.15.1.tgz"
  "version" "7.15.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-jsx-self@^7.0.0":
  "integrity" "sha512-Fqqu0f8zv9W+RyOnx29BX/RlEsBRANbOf5xs5oxb2aHP4FKbLXxIaVPUiCti56LAR1IixMH4EyaixhUsKqoBHw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.14.9.tgz"
  "version" "7.14.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-jsx-source@^7.0.0":
  "integrity" "sha512-1TpSDnD9XR/rQ2tzunBVPThF5poaYT9GqP+of8fAtguYuI/dm2RkrMBDemsxtY0XBzvW7nXjYM0hRyKX9QYj7Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-jsx@^7.0.0":
  "integrity" "sha512-30PeETvS+AeD1f58i1OVyoDlVYQhap/K20ZrMjLmmzmC2AYR/G43D4sdJAaDAqCD3MYpSWbmrz3kES158QSLjw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.14.9.tgz"
  "version" "7.14.9"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.14.5"
    "@babel/helper-module-imports" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-jsx" "^7.14.5"
    "@babel/types" "^7.14.9"

"@babel/plugin-transform-regenerator@^7.0.0", "@babel/plugin-transform-regenerator@^7.14.5":
  "integrity" "sha512-NVIY1W3ITDP5xQl50NgTKlZ0GrotKtLna08/uGY6ErQt6VEQZXla86x/CTddm5gZdcr+5GSsvMeTmWA5Ii6pkg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "regenerator-transform" "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.14.5":
  "integrity" "sha512-cv4F2rv1nD4qdexOGsRQXJrOcyb5CrgjUH9PKrrtyhSDBNWGxd0UIitjyJiWagS+EbUGjG++22mGH1Pub8D6Vg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-runtime@^7.0.0":
  "integrity" "sha512-sfHYkLGjhzWTq6xsuQ01oEsUYjkHRux9fW1iUA68dC7Qd8BS1Unq4aZ8itmQp95zUzIcyR2EbNMTzAicFj+guw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.15.0.tgz"
  "version" "7.15.0"
  dependencies:
    "@babel/helper-module-imports" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"
    "babel-plugin-polyfill-corejs2" "^0.2.2"
    "babel-plugin-polyfill-corejs3" "^0.2.2"
    "babel-plugin-polyfill-regenerator" "^0.2.2"
    "semver" "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.0.0", "@babel/plugin-transform-shorthand-properties@^7.14.5":
  "integrity" "sha512-xLucks6T1VmGsTB+GWK5Pl9Jl5+nRXD1uoFdA5TSO6xtiNjtXTjKkmPdFXVLGlK5A2/or/wQMKfmQ2Y0XJfn5g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-spread@^7.0.0", "@babel/plugin-transform-spread@^7.14.6":
  "integrity" "sha512-Zr0x0YroFJku7n7+/HH3A2eIrGMjbmAIbJSVv0IZ+t3U2WUQUA64S/oeied2e+MaGSjmt4alzBCsK9E8gh+fag=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.14.6.tgz"
  "version" "7.14.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.14.5"

"@babel/plugin-transform-sticky-regex@^7.0.0", "@babel/plugin-transform-sticky-regex@^7.14.5":
  "integrity" "sha512-Z7F7GyvEMzIIbwnziAZmnSNpdijdr4dWt+FJNBnBLz5mwDFkqIXU9wmBcWWad3QeJF5hMTkRe4dAq2sUZiG+8A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-template-literals@^7.0.0", "@babel/plugin-transform-template-literals@^7.14.5":
  "integrity" "sha512-22btZeURqiepOfuy/VkFr+zStqlujWaarpMErvay7goJS6BWwdd6BY9zQyDLDa4x2S3VugxFb162IZ4m/S/+Gg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-typeof-symbol@^7.14.5":
  "integrity" "sha512-lXzLD30ffCWseTbMQzrvDWqljvZlHkXU+CnseMhkMNqU1sASnCsz3tSzAaH3vCUXb9PHeUb90ZT1BdFTm1xxJw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-typescript@^7.15.0", "@babel/plugin-transform-typescript@^7.5.0":
  "integrity" "sha512-sM1/FEjwYjXvMwu1PJStH11kJ154zd/lpY56NQJ5qH2D0mabMv1CAy/kdvS9RP4Xgfj9fBBA3JiSLdDHgXdzOA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.15.4"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-typescript" "^7.14.5"

"@babel/plugin-transform-unicode-escapes@^7.14.5":
  "integrity" "sha512-crTo4jATEOjxj7bt9lbYXcBAM3LZaUrbP2uUdxb6WIorLmjNKSpHfIybgY4B8SRpbf8tEVIWH3Vtm7ayCrKocA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-unicode-regex@^7.0.0", "@babel/plugin-transform-unicode-regex@^7.14.5":
  "integrity" "sha512-UygduJpC5kHeCiRw/xDVzC+wj8VaYSoKl5JNVmbP7MadpNinAm3SvZCxZ42H37KZBKztz46YC73i9yV34d0Tzw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.14.5"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/preset-env@^7.1.6":
  "integrity" "sha512-L+6jcGn7EWu7zqaO2uoTDjjMBW+88FXzV8KvrBl2z6MtRNxlsmUNRlZPaNNPUTgqhyC5DHNFk/2Jmra+ublZWw=="
  "resolved" "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.15.6.tgz"
  "version" "7.15.6"
  dependencies:
    "@babel/compat-data" "^7.15.0"
    "@babel/helper-compilation-targets" "^7.15.4"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.15.4"
    "@babel/plugin-proposal-async-generator-functions" "^7.15.4"
    "@babel/plugin-proposal-class-properties" "^7.14.5"
    "@babel/plugin-proposal-class-static-block" "^7.15.4"
    "@babel/plugin-proposal-dynamic-import" "^7.14.5"
    "@babel/plugin-proposal-export-namespace-from" "^7.14.5"
    "@babel/plugin-proposal-json-strings" "^7.14.5"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.14.5"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.14.5"
    "@babel/plugin-proposal-numeric-separator" "^7.14.5"
    "@babel/plugin-proposal-object-rest-spread" "^7.15.6"
    "@babel/plugin-proposal-optional-catch-binding" "^7.14.5"
    "@babel/plugin-proposal-optional-chaining" "^7.14.5"
    "@babel/plugin-proposal-private-methods" "^7.14.5"
    "@babel/plugin-proposal-private-property-in-object" "^7.15.4"
    "@babel/plugin-proposal-unicode-property-regex" "^7.14.5"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.14.5"
    "@babel/plugin-transform-async-to-generator" "^7.14.5"
    "@babel/plugin-transform-block-scoped-functions" "^7.14.5"
    "@babel/plugin-transform-block-scoping" "^7.15.3"
    "@babel/plugin-transform-classes" "^7.15.4"
    "@babel/plugin-transform-computed-properties" "^7.14.5"
    "@babel/plugin-transform-destructuring" "^7.14.7"
    "@babel/plugin-transform-dotall-regex" "^7.14.5"
    "@babel/plugin-transform-duplicate-keys" "^7.14.5"
    "@babel/plugin-transform-exponentiation-operator" "^7.14.5"
    "@babel/plugin-transform-for-of" "^7.15.4"
    "@babel/plugin-transform-function-name" "^7.14.5"
    "@babel/plugin-transform-literals" "^7.14.5"
    "@babel/plugin-transform-member-expression-literals" "^7.14.5"
    "@babel/plugin-transform-modules-amd" "^7.14.5"
    "@babel/plugin-transform-modules-commonjs" "^7.15.4"
    "@babel/plugin-transform-modules-systemjs" "^7.15.4"
    "@babel/plugin-transform-modules-umd" "^7.14.5"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.14.9"
    "@babel/plugin-transform-new-target" "^7.14.5"
    "@babel/plugin-transform-object-super" "^7.14.5"
    "@babel/plugin-transform-parameters" "^7.15.4"
    "@babel/plugin-transform-property-literals" "^7.14.5"
    "@babel/plugin-transform-regenerator" "^7.14.5"
    "@babel/plugin-transform-reserved-words" "^7.14.5"
    "@babel/plugin-transform-shorthand-properties" "^7.14.5"
    "@babel/plugin-transform-spread" "^7.14.6"
    "@babel/plugin-transform-sticky-regex" "^7.14.5"
    "@babel/plugin-transform-template-literals" "^7.14.5"
    "@babel/plugin-transform-typeof-symbol" "^7.14.5"
    "@babel/plugin-transform-unicode-escapes" "^7.14.5"
    "@babel/plugin-transform-unicode-regex" "^7.14.5"
    "@babel/preset-modules" "^0.1.4"
    "@babel/types" "^7.15.6"
    "babel-plugin-polyfill-corejs2" "^0.2.2"
    "babel-plugin-polyfill-corejs3" "^0.2.2"
    "babel-plugin-polyfill-regenerator" "^0.2.2"
    "core-js-compat" "^3.16.0"
    "semver" "^6.3.0"

"@babel/preset-flow@^7.0.0":
  "integrity" "sha512-pP5QEb4qRUSVGzzKx9xqRuHUrM/jEzMqdrZpdMA+oUCRgd5zM1qGr5y5+ZgAL/1tVv1H0dyk5t4SKJntqyiVtg=="
  "resolved" "https://registry.npmjs.org/@babel/preset-flow/-/preset-flow-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "@babel/plugin-transform-flow-strip-types" "^7.14.5"

"@babel/preset-modules@^0.1.4":
  "integrity" "sha512-J36NhwnfdzpmH41M1DrnkkgAqhZaqr/NBdPfQ677mLzlaXo+oDiv1deyCDtgAhz8p328otdob0Du7+xgHGZbKg=="
  "resolved" "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/preset-typescript@^7.1.0":
  "integrity" "sha512-lt0Y/8V3y06Wq/8H/u0WakrqciZ7Fz7mwPDHWUJAXlABL5hiUG42BNlRXiELNjeWjO5rWmnNKlx+yzJvxezHow=="
  "resolved" "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.15.0.tgz"
  "version" "7.15.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "@babel/plugin-transform-typescript" "^7.15.0"

"@babel/register@^7.0.0":
  "integrity" "sha512-mj4IY1ZJkorClxKTImccn4T81+UKTo4Ux0+OFSV9hME1ooqS9UV+pJ6BjD0qXPK4T3XW/KNa79XByjeEMZz+fw=="
  "resolved" "https://registry.npmjs.org/@babel/register/-/register-7.15.3.tgz"
  "version" "7.15.3"
  dependencies:
    "clone-deep" "^4.0.1"
    "find-cache-dir" "^2.0.0"
    "make-dir" "^2.1.0"
    "pirates" "^4.0.0"
    "source-map-support" "^0.5.16"

"@babel/runtime@^7.15.4", "@babel/runtime@^7.8.4":
  "integrity" "sha512-99catp6bHCaxr4sJ/DbTGgHS4+Rs2RVd2g7iOap6SLGPDknRK9ztKNsE/Fg6QhSeh1FGE5f6gHGQmvvn3I3xhw=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "regenerator-runtime" "^0.13.4"

"@babel/template@^7.0.0", "@babel/template@^7.15.4", "@babel/template@^7.3.3":
  "integrity" "sha512-UgBAfEa1oGuYgDIPM2G+aHa4Nlo9Lh6mGD2bDBGMTbYnc38vulXPuC1MGjYILIEmlwl6Rd+BPR9ee3gm20CBtg=="
  "resolved" "https://registry.npmjs.org/@babel/template/-/template-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/code-frame" "^7.14.5"
    "@babel/parser" "^7.15.4"
    "@babel/types" "^7.15.4"

"@babel/traverse@^7.1.0", "@babel/traverse@^7.13.0", "@babel/traverse@^7.14.0", "@babel/traverse@^7.15.4", "@babel/traverse@^7.7.0", "@babel/traverse@^7.7.2", "@babel/traverse@^7.7.4":
  "integrity" "sha512-W6lQD8l4rUbQR/vYgSuCAE75ADyyQvOpFVsvPPdkhf6lATXAsQIG9YdtOcu8BB1dZ0LKu+Zo3c1wEcbKeuhdlA=="
  "resolved" "https://registry.npmjs.org/@babel/traverse/-/traverse-7.15.4.tgz"
  "version" "7.15.4"
  dependencies:
    "@babel/code-frame" "^7.14.5"
    "@babel/generator" "^7.15.4"
    "@babel/helper-function-name" "^7.15.4"
    "@babel/helper-hoist-variables" "^7.15.4"
    "@babel/helper-split-export-declaration" "^7.15.4"
    "@babel/parser" "^7.15.4"
    "@babel/types" "^7.15.4"
    "debug" "^4.1.0"
    "globals" "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.14.9", "@babel/types@^7.15.4", "@babel/types@^7.15.6", "@babel/types@^7.3.0", "@babel/types@^7.3.3", "@babel/types@^7.4.4", "@babel/types@^7.7.0":
  "integrity" "sha512-BPU+7QhqNjmWyDO0/vitH/CuhpV8ZmK1wpKva8nuyNF5MJfuRNWMc+hc14+u9xT93kvykMdncrJT19h74uB1Ig=="
  "resolved" "https://registry.npmjs.org/@babel/types/-/types-7.15.6.tgz"
  "version" "7.15.6"
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.9"
    "to-fast-properties" "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  "integrity" "sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw=="
  "resolved" "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz"
  "version" "0.2.3"

"@cnakazawa/watch@^1.0.3":
  "integrity" "sha512-v9kIhKwjeZThiWrLmj0y17CWoyddASLj9O2yvbZkbvw/N3rWOYy9zkV66ursAoVr0mV15bL8g0c4QZUE6cdDoQ=="
  "resolved" "https://registry.npmjs.org/@cnakazawa/watch/-/watch-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "exec-sh" "^0.3.2"
    "minimist" "^1.2.0"

"@eslint/eslintrc@^0.4.3":
  "integrity" "sha512-J6KFFz5QCYUJq3pf0mjEcCJVERbzv71PUIDczuh9JkwGEzced6CO5ADLHB1rbf/+oPBtoPfMYNOpGDzCANlbXw=="
  "resolved" "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.4.3.tgz"
  "version" "0.4.3"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.1.1"
    "espree" "^7.3.0"
    "globals" "^13.9.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.2.1"
    "js-yaml" "^3.13.1"
    "minimatch" "^3.0.4"
    "strip-json-comments" "^3.1.1"

"@hapi/hoek@^9.0.0":
  "integrity" "sha512-sqKVVVOe5ivCaXDWivIJYVSaEgdQK9ul7a4Kity5Iw7u9+wBAPbX1RMSnLLmp7O4Vzj0WOWwMAJsTL00xwaNug=="
  "resolved" "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.2.0.tgz"
  "version" "9.2.0"

"@hapi/topo@^5.0.0":
  "integrity" "sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg=="
  "resolved" "https://registry.npmjs.org/@hapi/topo/-/topo-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@humanwhocodes/config-array@^0.5.0":
  "integrity" "sha512-FagtKFz74XrTl7y6HCzQpwDfXP0yhxe9lHLD1UZxjvZIcbyRz8zTFF/yYNfSfzU414eDwZ1SrO0Qvtyf+wFMQg=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    "debug" "^4.1.1"
    "minimatch" "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  "integrity" "sha512-wdppn25U8z/2yiaT6YGquE6X8sSv7hNMWSXYSSU1jGv/yd6XqjXgTDJ8KP4NgjTXfJ3GbRjeeb8RTV7a/VpM+w=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.0.tgz"
  "version" "1.2.0"

"@istanbuljs/load-nyc-config@^1.0.0":
  "integrity" "sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ=="
  "resolved" "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "camelcase" "^5.3.1"
    "find-up" "^4.1.0"
    "get-package-type" "^0.1.0"
    "js-yaml" "^3.13.1"
    "resolve-from" "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  "integrity" "sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA=="
  "resolved" "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz"
  "version" "0.1.3"

"@jest/console@^27.2.0":
  "integrity" "sha512-35z+RqsK2CCgNxn+lWyK8X4KkaDtfL4BggT7oeZ0JffIiAiEYFYPo5B67V50ZubqDS1ehBrdCR2jduFnIrZOYw=="
  "resolved" "https://registry.npmjs.org/@jest/console/-/console-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/types" "^27.1.1"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "jest-message-util" "^27.2.0"
    "jest-util" "^27.2.0"
    "slash" "^3.0.0"

"@jest/core@^27.2.0":
  "integrity" "sha512-E/2NHhq+VMo18DpKkoty8Sjey8Kps5Cqa88A8NP757s6JjYqPdioMuyUBhDiIOGCdQByEp0ou3jskkTszMS0nw=="
  "resolved" "https://registry.npmjs.org/@jest/core/-/core-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/console" "^27.2.0"
    "@jest/reporters" "^27.2.0"
    "@jest/test-result" "^27.2.0"
    "@jest/transform" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/node" "*"
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.0.0"
    "emittery" "^0.8.1"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "jest-changed-files" "^27.1.1"
    "jest-config" "^27.2.0"
    "jest-haste-map" "^27.2.0"
    "jest-message-util" "^27.2.0"
    "jest-regex-util" "^27.0.6"
    "jest-resolve" "^27.2.0"
    "jest-resolve-dependencies" "^27.2.0"
    "jest-runner" "^27.2.0"
    "jest-runtime" "^27.2.0"
    "jest-snapshot" "^27.2.0"
    "jest-util" "^27.2.0"
    "jest-validate" "^27.2.0"
    "jest-watcher" "^27.2.0"
    "micromatch" "^4.0.4"
    "p-each-series" "^2.1.0"
    "rimraf" "^3.0.0"
    "slash" "^3.0.0"
    "strip-ansi" "^6.0.0"

"@jest/create-cache-key-function@^27.0.1":
  "integrity" "sha512-o+Q6CAT/mPNxiOGn6+UD/buY8O5cyDHbjD8FXDCUZbzfCwt6zYlmCMObLE+k1i/XJc5M3YIvEeaa6L7x7uAeYQ=="
  "resolved" "https://registry.npmjs.org/@jest/create-cache-key-function/-/create-cache-key-function-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/types" "^27.1.1"

"@jest/environment@^27.2.0":
  "integrity" "sha512-iPWmQI0wRIYSZX3wKu4FXHK4eIqkfq6n1DCDJS+v3uby7SOXrHvX4eiTBuEdSvtDRMTIH2kjrSkjHf/F9JIYyQ=="
  "resolved" "https://registry.npmjs.org/@jest/environment/-/environment-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/fake-timers" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/node" "*"
    "jest-mock" "^27.1.1"

"@jest/fake-timers@^27.2.0":
  "integrity" "sha512-gSu3YHvQOoVaTWYGgHFB7IYFtcF2HBzX4l7s47VcjvkUgL4/FBnE20x7TNLa3W6ABERtGd5gStSwsA8bcn+c4w=="
  "resolved" "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/types" "^27.1.1"
    "@sinonjs/fake-timers" "^7.0.2"
    "@types/node" "*"
    "jest-message-util" "^27.2.0"
    "jest-mock" "^27.1.1"
    "jest-util" "^27.2.0"

"@jest/globals@^27.2.0":
  "integrity" "sha512-raqk9Gf9WC3hlBa57rmRmJfRl9hom2b+qEE/ifheMtwn5USH5VZxzrHHOZg0Zsd/qC2WJ8UtyTwHKQAnNlDMdg=="
  "resolved" "https://registry.npmjs.org/@jest/globals/-/globals-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/environment" "^27.2.0"
    "@jest/types" "^27.1.1"
    "expect" "^27.2.0"

"@jest/reporters@^27.2.0":
  "integrity" "sha512-7wfkE3iRTLaT0F51h1mnxH3nQVwDCdbfgXiLuCcNkF1FnxXLH9utHqkSLIiwOTV1AtmiE0YagHbOvx4rnMP/GA=="
  "resolved" "https://registry.npmjs.org/@jest/reporters/-/reporters-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^27.2.0"
    "@jest/test-result" "^27.2.0"
    "@jest/transform" "^27.2.0"
    "@jest/types" "^27.1.1"
    "chalk" "^4.0.0"
    "collect-v8-coverage" "^1.0.0"
    "exit" "^0.1.2"
    "glob" "^7.1.2"
    "graceful-fs" "^4.2.4"
    "istanbul-lib-coverage" "^3.0.0"
    "istanbul-lib-instrument" "^4.0.3"
    "istanbul-lib-report" "^3.0.0"
    "istanbul-lib-source-maps" "^4.0.0"
    "istanbul-reports" "^3.0.2"
    "jest-haste-map" "^27.2.0"
    "jest-resolve" "^27.2.0"
    "jest-util" "^27.2.0"
    "jest-worker" "^27.2.0"
    "slash" "^3.0.0"
    "source-map" "^0.6.0"
    "string-length" "^4.0.1"
    "terminal-link" "^2.0.0"
    "v8-to-istanbul" "^8.0.0"

"@jest/source-map@^27.0.6":
  "integrity" "sha512-Fek4mi5KQrqmlY07T23JRi0e7Z9bXTOOD86V/uS0EIW4PClvPDqZOyFlLpNJheS6QI0FNX1CgmPjtJ4EA/2M+g=="
  "resolved" "https://registry.npmjs.org/@jest/source-map/-/source-map-27.0.6.tgz"
  "version" "27.0.6"
  dependencies:
    "callsites" "^3.0.0"
    "graceful-fs" "^4.2.4"
    "source-map" "^0.6.0"

"@jest/test-result@^27.2.0":
  "integrity" "sha512-JPPqn8h0RGr4HyeY1Km+FivDIjTFzDROU46iAvzVjD42ooGwYoqYO/MQTilhfajdz6jpVnnphFrKZI5OYrBONA=="
  "resolved" "https://registry.npmjs.org/@jest/test-result/-/test-result-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/console" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "collect-v8-coverage" "^1.0.0"

"@jest/test-sequencer@^27.2.0":
  "integrity" "sha512-PrqarcpzOU1KSAK7aPwfL8nnpaqTMwPe7JBPnaOYRDSe/C6AoJiL5Kbnonqf1+DregxZIRAoDg69R9/DXMGqXA=="
  "resolved" "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/test-result" "^27.2.0"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^27.2.0"
    "jest-runtime" "^27.2.0"

"@jest/transform@^27.2.0":
  "integrity" "sha512-Q8Q/8xXIZYllk1AF7Ou5sV3egOZsdY/Wlv09CSbcexBRcC1Qt6lVZ7jRFAZtbHsEEzvOCyFEC4PcrwKwyjXtCg=="
  "resolved" "https://registry.npmjs.org/@jest/transform/-/transform-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^27.1.1"
    "babel-plugin-istanbul" "^6.0.0"
    "chalk" "^4.0.0"
    "convert-source-map" "^1.4.0"
    "fast-json-stable-stringify" "^2.0.0"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^27.2.0"
    "jest-regex-util" "^27.0.6"
    "jest-util" "^27.2.0"
    "micromatch" "^4.0.4"
    "pirates" "^4.0.1"
    "slash" "^3.0.0"
    "source-map" "^0.6.1"
    "write-file-atomic" "^3.0.0"

"@jest/types@^26.6.2":
  "integrity" "sha512-fC6QCp7Sc5sX6g8Tvbmj4XUTbyrik0akgRy03yjXbQaBWWNWGE7SGtJk98m0N8nzegD/7SggrUlivxo5ax4KWQ=="
  "resolved" "https://registry.npmjs.org/@jest/types/-/types-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^15.0.0"
    "chalk" "^4.0.0"

"@jest/types@^27.1.1":
  "integrity" "sha512-yqJPDDseb0mXgKqmNqypCsb85C22K1aY5+LUxh7syIM9n/b0AsaltxNy+o6tt29VcfGDpYEve175bm3uOhcehA=="
  "resolved" "https://registry.npmjs.org/@jest/types/-/types-27.1.1.tgz"
  "version" "27.1.1"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^16.0.0"
    "chalk" "^4.0.0"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  "integrity" "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@react-native-community/cli-debugger-ui@^6.0.0-rc.0":
  "integrity" "sha512-achYcPPoWa9D02C5tn6TBzjeY443wQTyx37urptc75JpZ7gR5YHsDyIEEWa3DDYp1va9zx/iGg+uZ/hWw07GAw=="
  "resolved" "https://registry.npmjs.org/@react-native-community/cli-debugger-ui/-/cli-debugger-ui-6.0.0-rc.0.tgz"
  "version" "6.0.0-rc.0"
  dependencies:
    "serve-static" "^1.13.1"

"@react-native-community/cli-hermes@^6.0.0":
  "integrity" "sha512-YUX8MEmDsEYdFuo/juCZUUDPPRQ/su3K/SPcSVmv7AIAwO/7ItuQ7+58PRI914XNvnRmY1GNVHKfWhUoNXMxvA=="
  "resolved" "https://registry.npmjs.org/@react-native-community/cli-hermes/-/cli-hermes-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@react-native-community/cli-platform-android" "^6.0.0"
    "@react-native-community/cli-tools" "^6.0.0-rc.0"
    "chalk" "^3.0.0"
    "hermes-profile-transformer" "^0.0.6"
    "ip" "^1.1.5"

"@react-native-community/cli-platform-android@^6.0.0":
  "integrity" "sha512-yXyrM2elKM8/thf1d8EMMm0l0KdeWmIMhWZzCoRpCIQoUuVtiCEMyrZF+aufvNvy74soKiCFeAmGNI8LPk2hzg=="
  "resolved" "https://registry.npmjs.org/@react-native-community/cli-platform-android/-/cli-platform-android-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@react-native-community/cli-tools" "^6.0.0-rc.0"
    "chalk" "^3.0.0"
    "execa" "^1.0.0"
    "fs-extra" "^8.1.0"
    "glob" "^7.1.3"
    "jetifier" "^1.6.2"
    "lodash" "^4.17.15"
    "logkitty" "^0.7.1"
    "slash" "^3.0.0"
    "xmldoc" "^1.1.2"

"@react-native-community/cli-platform-ios@^6.0.0":
  "integrity" "sha512-+f6X4jDGuPpVcY2NsVAstnId4stnG7EvzLUhs7FUpMFjzss9c1ZJhsqQeKikOtzZbwLzFrpki/QrTK79ur7xSg=="
  "resolved" "https://registry.npmjs.org/@react-native-community/cli-platform-ios/-/cli-platform-ios-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@react-native-community/cli-tools" "^6.0.0-rc.0"
    "chalk" "^3.0.0"
    "glob" "^7.1.3"
    "js-yaml" "^3.13.1"
    "lodash" "^4.17.15"
    "plist" "^3.0.2"
    "xcode" "^2.0.0"

"@react-native-community/cli-server-api@^6.0.0-rc.0":
  "integrity" "sha512-shPG9RXXpDYeluoB3tzaYU9Ut0jTvZ3osatLLUJkWjbRjFreK9zUcnoFDDrsVT6fEoyeBftp5DSa+wCUnPmcJA=="
  "resolved" "https://registry.npmjs.org/@react-native-community/cli-server-api/-/cli-server-api-6.0.0-rc.0.tgz"
  "version" "6.0.0-rc.0"
  dependencies:
    "@react-native-community/cli-debugger-ui" "^6.0.0-rc.0"
    "@react-native-community/cli-tools" "^6.0.0-rc.0"
    "compression" "^1.7.1"
    "connect" "^3.6.5"
    "errorhandler" "^1.5.0"
    "nocache" "^2.1.0"
    "pretty-format" "^26.6.2"
    "serve-static" "^1.13.1"
    "ws" "^1.1.0"

"@react-native-community/cli-tools@^6.0.0-rc.0":
  "integrity" "sha512-N31BhNacTe0UGYQxUx0WHWPKnF4pBe62hNRV9WNJdWqVl4TP45T1Fd/7ziiosfalIar+tOo9Sk0Pqq48x1+wNw=="
  "resolved" "https://registry.npmjs.org/@react-native-community/cli-tools/-/cli-tools-6.0.0-rc.0.tgz"
  "version" "6.0.0-rc.0"
  dependencies:
    "chalk" "^3.0.0"
    "lodash" "^4.17.15"
    "mime" "^2.4.1"
    "node-fetch" "^2.6.0"
    "open" "^6.2.0"
    "shell-quote" "1.6.1"

"@react-native-community/cli-types@^6.0.0":
  "integrity" "sha512-K493Fk2DMJC0ZM8s8gnfseKxGasIhuDaCUDeLZcoCSFlrjKEuEs1BKKEJiev0CARhKEXKOyyp/uqYM9nWhisNw=="
  "resolved" "https://registry.npmjs.org/@react-native-community/cli-types/-/cli-types-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "ora" "^3.4.0"

"@react-native-community/cli@^6.0.0":
  "integrity" "sha512-wTbdpai58WzUBrw8lNbF/cSzX3pOWz+y+d46ip3M3Abd5yHNRvhuejRMVQC1o9luOM+ESJa4imYSbVdh7y5g+w=="
  "resolved" "https://registry.npmjs.org/@react-native-community/cli/-/cli-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@react-native-community/cli-debugger-ui" "^6.0.0-rc.0"
    "@react-native-community/cli-hermes" "^6.0.0"
    "@react-native-community/cli-server-api" "^6.0.0-rc.0"
    "@react-native-community/cli-tools" "^6.0.0-rc.0"
    "@react-native-community/cli-types" "^6.0.0"
    "appdirsjs" "^1.2.4"
    "chalk" "^3.0.0"
    "command-exists" "^1.2.8"
    "commander" "^2.19.0"
    "cosmiconfig" "^5.1.0"
    "deepmerge" "^3.2.0"
    "envinfo" "^7.7.2"
    "execa" "^1.0.0"
    "find-up" "^4.1.0"
    "fs-extra" "^8.1.0"
    "glob" "^7.1.3"
    "graceful-fs" "^4.1.3"
    "joi" "^17.2.1"
    "leven" "^3.1.0"
    "lodash" "^4.17.15"
    "metro" "^0.66.1"
    "metro-config" "^0.66.1"
    "metro-core" "^0.66.1"
    "metro-react-native-babel-transformer" "^0.66.1"
    "metro-resolver" "^0.66.1"
    "metro-runtime" "^0.66.1"
    "minimist" "^1.2.0"
    "mkdirp" "^0.5.1"
    "node-stream-zip" "^1.9.1"
    "ora" "^3.4.0"
    "pretty-format" "^26.6.2"
    "prompts" "^2.4.0"
    "semver" "^6.3.0"
    "serve-static" "^1.13.1"
    "strip-ansi" "^5.2.0"
    "sudo-prompt" "^9.0.0"
    "wcwidth" "^1.0.1"

"@react-native-community/eslint-config@^3.0.1":
  "integrity" "sha512-h0MRsaHkyfoQjEiTjG3a+Ie/p6AmQNAHXpYrVhtYMZNRF26TCwNOsQNp4/13QhKAq6vhS8EJppge0P43GnPmfQ=="
  "resolved" "https://registry.npmjs.org/@react-native-community/eslint-config/-/eslint-config-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@react-native-community/eslint-plugin" "^1.1.0"
    "@typescript-eslint/eslint-plugin" "^4.22.1"
    "@typescript-eslint/parser" "^4.22.1"
    "babel-eslint" "^10.1.0"
    "eslint-config-prettier" "^6.10.1"
    "eslint-plugin-eslint-comments" "^3.1.2"
    "eslint-plugin-flowtype" "2.50.3"
    "eslint-plugin-jest" "22.4.1"
    "eslint-plugin-prettier" "3.1.2"
    "eslint-plugin-react" "^7.20.0"
    "eslint-plugin-react-hooks" "^4.0.7"
    "eslint-plugin-react-native" "^3.10.0"
    "prettier" "^2.0.2"

"@react-native-community/eslint-plugin@^1.1.0":
  "integrity" "sha512-W/J0fNYVO01tioHjvYWQ9m6RgndVtbElzYozBq1ZPrHO/iCzlqoySHl4gO/fpCl9QEFjvJfjPgtPMTMlsoq5DQ=="
  "resolved" "https://registry.npmjs.org/@react-native-community/eslint-plugin/-/eslint-plugin-1.1.0.tgz"
  "version" "1.1.0"

"@react-native/assets@1.0.0":
  "integrity" "sha512-KrwSpS1tKI70wuKl68DwJZYEvXktDHdZMG0k2AXD/rJVSlB23/X2CB2cutVR0HwNMJIal9HOUOBB2rVfa6UGtQ=="
  "resolved" "https://registry.npmjs.org/@react-native/assets/-/assets-1.0.0.tgz"
  "version" "1.0.0"

"@react-native/normalize-color@1.0.0":
  "integrity" "sha512-xUNRvNmCl3UGCPbbHvfyFMnpvLPoOjDCcp5bT9m2k+TF/ZBklEQwhPZlkrxRx2NhgFh1X3a5uL7mJ7ZR+8G7Qg=="
  "resolved" "https://registry.npmjs.org/@react-native/normalize-color/-/normalize-color-1.0.0.tgz"
  "version" "1.0.0"

"@react-native/polyfills@1.0.0":
  "integrity" "sha512-0jbp4RxjYopTsIdLl+/Fy2TiwVYHy4mgeu07DG4b/LyM0OS/+lPP5c9sbnt/AMlnF6qz2JRZpPpGw1eMNS6A4w=="
  "resolved" "https://registry.npmjs.org/@react-native/polyfills/-/polyfills-1.0.0.tgz"
  "version" "1.0.0"

"@sideway/address@^4.1.0":
  "integrity" "sha512-idTz8ibqWFrPU8kMirL0CoPH/A29XOzzAzpyN3zQ4kAWnzmNfFmRaoMNN6VI8ske5M73HZyhIaW4OuSFIdM4oA=="
  "resolved" "https://registry.npmjs.org/@sideway/address/-/address-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@sideway/formula@^3.0.0":
  "integrity" "sha512-vHe7wZ4NOXVfkoRb8T5otiENVlT7a3IAiw7H5M2+GO+9CDgcVUUsX1zalAztCmwyOr2RUTGJdgB+ZvSVqmdHmg=="
  "resolved" "https://registry.npmjs.org/@sideway/formula/-/formula-3.0.0.tgz"
  "version" "3.0.0"

"@sideway/pinpoint@^2.0.0":
  "integrity" "sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ=="
  "resolved" "https://registry.npmjs.org/@sideway/pinpoint/-/pinpoint-2.0.0.tgz"
  "version" "2.0.0"

"@sinonjs/commons@^1.7.0":
  "integrity" "sha512-xkNcLAn/wZaX14RPlwizcKicDk9G3F8m2nU3L7Ukm5zBgTwiT0wsoFAHx9Jq56fJA1z/7uKGtCRu16sOUCLIHQ=="
  "resolved" "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.3.tgz"
  "version" "1.8.3"
  dependencies:
    "type-detect" "4.0.8"

"@sinonjs/fake-timers@^7.0.2":
  "integrity" "sha512-iQADsW4LBMISqZ6Ci1dupJL9pprqwcVFTcOsEmQOEhW+KLCVn/Y4Jrvg2k19fIHCp+iFprriYPTdRcQR8NbUPg=="
  "resolved" "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-7.1.2.tgz"
  "version" "7.1.2"
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@tootallnate/once@1":
  "integrity" "sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw=="
  "resolved" "https://registry.npmjs.org/@tootallnate/once/-/once-1.1.2.tgz"
  "version" "1.1.2"

"@types/babel__core@^7.0.0", "@types/babel__core@^7.1.14":
  "integrity" "sha512-EAEHtisTMM+KaKwfWdC3oyllIqswlznXCIVCt7/oRNrh+DhgT4UEBNC/jlADNjvw7UnfbcdkGQcPVZ1xYiLcrQ=="
  "resolved" "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.16.tgz"
  "version" "7.1.16"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  "integrity" "sha512-/GWCmzJWqV7diQW54smJZzWbSFf4QYtF71WCKhcx6Ru/tFyQIY2eiiITcCAeuPbNSvT9YCGkVMqqvSk2Z0mXiA=="
  "resolved" "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.3.tgz"
  "version" "7.6.3"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  "integrity" "sha512-azBFKemX6kMg5Io+/rdGT0dkGreboUVR0Cdm3fz9QJWpaQGJRQXl7C+6hOTCZcMll7KFyEQpgbYI2lHdsS4U7g=="
  "resolved" "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.1.tgz"
  "version" "7.4.1"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.4", "@types/babel__traverse@^7.0.6":
  "integrity" "sha512-K2waXdXBi2302XUdcHcR1jCeU0LL4TD9HRs/gk0N2Xvrht+G/BfJa4QObBQZfhMdxiCpV3COl5Nfq4uKTeTnJA=="
  "resolved" "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.14.2.tgz"
  "version" "7.14.2"
  dependencies:
    "@babel/types" "^7.3.0"

"@types/graceful-fs@^4.1.2":
  "integrity" "sha512-anKkLmZZ+xm4p8JWBf4hElkM4XR+EZeA2M9BAkkTldmcyDY4mbdIJnRghDJH3Ov5ooY7/UAoENtmdMSkaAd7Cw=="
  "resolved" "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  "integrity" "sha512-sz7iLqvVUg1gIedBOvlkxPlc8/uVzyS5OwGz1cKjXzkl3FpL3al0crU8YGU1WoHkxn0Wxbw5tyi6hvzJKNzFsw=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.3.tgz"
  "version" "2.0.3"

"@types/istanbul-lib-report@*":
  "integrity" "sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  "integrity" "sha512-c3mAZEuK0lvBp8tmuL74XRKn1+y2dcwOUpH7x4WrF6gk1GIgiluDRgMYQtw2OFcBvAJWlt6ASU3tSqxp0Uu0Aw=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/json-schema@^7.0.7":
  "integrity" "sha512-qcUXuemtEu+E5wZSJHNxUXeCZhAfXKQ41D+duX+VYPde7xyEVZci+/oXKJL13tnRs9lR2pr4fod59GT6/X1/yQ=="
  "resolved" "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.9.tgz"
  "version" "7.0.9"

"@types/node@*":
  "integrity" "sha512-QpLcX9ZSsq3YYUUnD3nFDY8H7wctAhQj/TFKL8Ya8v5fMm3CFXxo8zStsLAl780ltoYoo1WvKUVGBQK+1ifr7g=="
  "resolved" "https://registry.npmjs.org/@types/node/-/node-16.9.1.tgz"
  "version" "16.9.1"

"@types/prettier@^2.1.5":
  "integrity" "sha512-eI5Yrz3Qv4KPUa/nSIAi0h+qX0XyewOliug5F2QAtuRg6Kjg6jfmxe1GIwoIRhZspD1A0RP8ANrPwvEXXtRFog=="
  "resolved" "https://registry.npmjs.org/@types/prettier/-/prettier-2.3.2.tgz"
  "version" "2.3.2"

"@types/stack-utils@^2.0.0":
  "integrity" "sha512-Hl219/BT5fLAaz6NDkSuhzasy49dwQS/DSdu4MdggFB8zcXv7vflBI3xp7FEmkmdDkBUI2bPUNeMttp2knYdxw=="
  "resolved" "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.1.tgz"
  "version" "2.0.1"

"@types/yargs-parser@*":
  "integrity" "sha512-7tFImggNeNBVMsn0vLrpn1H1uPrUBdnARPTpZoitY37ZrdJREzf7I16tMrlK3hen349gr1NYh8CmZQa7CTG6Aw=="
  "resolved" "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-20.2.1.tgz"
  "version" "20.2.1"

"@types/yargs@^15.0.0":
  "integrity" "sha512-yEJzHoxf6SyQGhBhIYGXQDSCkJjB6HohDShto7m8vaKg9Yp0Yn8+71J9eakh2bnPg6BfsH9PRMhiRTZnd4eXGQ=="
  "resolved" "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.14.tgz"
  "version" "15.0.14"
  dependencies:
    "@types/yargs-parser" "*"

"@types/yargs@^16.0.0":
  "integrity" "sha512-T8Yc9wt/5LbJyCaLiHPReJa0kApcIgJ7Bn735GjItUfh08Z1pJvu8QZqb9s+mMvKV6WUQRV7K2R46YbjMXTTJw=="
  "resolved" "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.4.tgz"
  "version" "16.0.4"
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^4.22.1":
  "integrity" "sha512-iPKZTZNavAlOhfF4gymiSuUkgLne/nh5Oz2/mdiUmuZVD42m9PapnCnzjxuDsnpnbH3wT5s2D8bw6S39TC6GNw=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-4.31.0.tgz"
  "version" "4.31.0"
  dependencies:
    "@typescript-eslint/experimental-utils" "4.31.0"
    "@typescript-eslint/scope-manager" "4.31.0"
    "debug" "^4.3.1"
    "functional-red-black-tree" "^1.0.1"
    "regexpp" "^3.1.0"
    "semver" "^7.3.5"
    "tsutils" "^3.21.0"

"@typescript-eslint/experimental-utils@4.31.0":
  "integrity" "sha512-Hld+EQiKLMppgKKkdUsLeVIeEOrwKc2G983NmznY/r5/ZtZCDvIOXnXtwqJIgYz/ymsy7n7RGvMyrzf1WaSQrw=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/experimental-utils/-/experimental-utils-4.31.0.tgz"
  "version" "4.31.0"
  dependencies:
    "@types/json-schema" "^7.0.7"
    "@typescript-eslint/scope-manager" "4.31.0"
    "@typescript-eslint/types" "4.31.0"
    "@typescript-eslint/typescript-estree" "4.31.0"
    "eslint-scope" "^5.1.1"
    "eslint-utils" "^3.0.0"

"@typescript-eslint/parser@^4.0.0", "@typescript-eslint/parser@^4.22.1":
  "integrity" "sha512-oWbzvPh5amMuTmKaf1wp0ySxPt2ZXHnFQBN2Szu1O//7LmOvgaKTCIDNLK2NvzpmVd5A2M/1j/rujBqO37hj3w=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-4.31.0.tgz"
  "version" "4.31.0"
  dependencies:
    "@typescript-eslint/scope-manager" "4.31.0"
    "@typescript-eslint/types" "4.31.0"
    "@typescript-eslint/typescript-estree" "4.31.0"
    "debug" "^4.3.1"

"@typescript-eslint/scope-manager@4.31.0":
  "integrity" "sha512-LJ+xtl34W76JMRLjbaQorhR0hfRAlp3Lscdiz9NeI/8i+q0hdBZ7BsiYieLoYWqy+AnRigaD3hUwPFugSzdocg=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-4.31.0.tgz"
  "version" "4.31.0"
  dependencies:
    "@typescript-eslint/types" "4.31.0"
    "@typescript-eslint/visitor-keys" "4.31.0"

"@typescript-eslint/types@4.31.0":
  "integrity" "sha512-9XR5q9mk7DCXgXLS7REIVs+BaAswfdHhx91XqlJklmqWpTALGjygWVIb/UnLh4NWhfwhR5wNe1yTyCInxVhLqQ=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/types/-/types-4.31.0.tgz"
  "version" "4.31.0"

"@typescript-eslint/typescript-estree@4.31.0":
  "integrity" "sha512-QHl2014t3ptg+xpmOSSPn5hm4mY8D4s97ftzyk9BZ8RxYQ3j73XcwuijnJ9cMa6DO4aLXeo8XS3z1omT9LA/Eg=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-4.31.0.tgz"
  "version" "4.31.0"
  dependencies:
    "@typescript-eslint/types" "4.31.0"
    "@typescript-eslint/visitor-keys" "4.31.0"
    "debug" "^4.3.1"
    "globby" "^11.0.3"
    "is-glob" "^4.0.1"
    "semver" "^7.3.5"
    "tsutils" "^3.21.0"

"@typescript-eslint/visitor-keys@4.31.0":
  "integrity" "sha512-HUcRp2a9I+P21+O21yu3ezv3GEPGjyGiXoEUQwZXjR8UxRApGeLyWH4ZIIUSalE28aG4YsV6GjtaAVB3QKOu0w=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-4.31.0.tgz"
  "version" "4.31.0"
  dependencies:
    "@typescript-eslint/types" "4.31.0"
    "eslint-visitor-keys" "^2.0.0"

"abab@^2.0.3", "abab@^2.0.5":
  "integrity" "sha512-9IK9EadsbHo6jLWIpxpR6pL0sazTXV6+SQv25ZB+F7Bj9mJNaOc4nCRabwd5M/JwmUa8idz6Eci6eKfJryPs6Q=="
  "resolved" "https://registry.npmjs.org/abab/-/abab-2.0.5.tgz"
  "version" "2.0.5"

"abort-controller@^3.0.0":
  "integrity" "sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg=="
  "resolved" "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "event-target-shim" "^5.0.0"

"absolute-path@^0.0.0":
  "integrity" "sha1-p4di+9rftSl76ZsV01p4Wy8JW/c="
  "resolved" "https://registry.npmjs.org/absolute-path/-/absolute-path-0.0.0.tgz"
  "version" "0.0.0"

"accepts@^1.3.7", "accepts@~1.3.5", "accepts@~1.3.7":
  "integrity" "sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA=="
  "resolved" "https://registry.npmjs.org/accepts/-/accepts-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "mime-types" "~2.1.24"
    "negotiator" "0.6.2"

"acorn-globals@^6.0.0":
  "integrity" "sha512-ZQl7LOWaF5ePqqcX4hLuv/bLXYQNfNWw2c0/yX/TsPRKamzHcTGQnlCjHT3TsmkOUVEPS3crCxiPfdzE/Trlhg=="
  "resolved" "https://registry.npmjs.org/acorn-globals/-/acorn-globals-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-walk" "^7.1.1"

"acorn-jsx@^5.3.1":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn-walk@^7.1.1":
  "integrity" "sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA=="
  "resolved" "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.2.0.tgz"
  "version" "7.2.0"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^7.1.1", "acorn@^7.4.0":
  "integrity" "sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz"
  "version" "7.4.1"

"acorn@^8.2.4":
  "integrity" "sha512-yXbYeFy+jUuYd3/CDcg2NkIYE991XYX/bje7LmjJigUciaeO1JR4XxXgCIV1/Zc/dRuFEyw1L0pbA+qynJkW5Q=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-8.5.0.tgz"
  "version" "8.5.0"

"agent-base@6":
  "integrity" "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ=="
  "resolved" "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "debug" "4"

"ajv@^6.10.0", "ajv@^6.12.4":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.0.1":
  "integrity" "sha512-SMJOdDP6LqTkD0Uq8qLi+gMwSt0imXLSV080qFVwJCpH9U6Mb+SUGHAXM0KNbcBPguytWyvFxcHgMLe2D2XSpw=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.6.3.tgz"
  "version" "8.6.3"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"anser@^1.4.9":
  "integrity" "sha512-hCv9AqTQ8ycjpSd3upOJd7vFwW1JaoYQ7tpham03GJ1ca8/65rqn0RpaWpItOAd6ylW9wAw6luXYPJIyPFVOww=="
  "resolved" "https://registry.npmjs.org/anser/-/anser-1.4.10.tgz"
  "version" "1.4.10"

"ansi-colors@^4.1.1":
  "integrity" "sha512-JoX0apGbHaUJBNl6yF+p6JAFYZ666/hhCGKN5t9QFjbJQKUU/g8MNbFDbvfrgKXvI1QpZplPOnwIo99lX/AAmA=="
  "resolved" "https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.1.tgz"
  "version" "4.1.1"

"ansi-escapes@^4.2.1":
  "integrity" "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ=="
  "resolved" "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-fragments@^0.2.1":
  "integrity" "sha512-DykbNHxuXQwUDRv5ibc2b0x7uw7wmwOGLBUd5RmaQ5z8Lhx19vwvKV+FAsM5rEA6dEcHxX+/Ad5s9eF2k2bB+w=="
  "resolved" "https://registry.npmjs.org/ansi-fragments/-/ansi-fragments-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "colorette" "^1.0.7"
    "slice-ansi" "^2.0.0"
    "strip-ansi" "^5.0.0"

"ansi-regex@^4.1.0":
  "integrity" "sha512-1apePfXM1UOSqw0o9IiFAovVz9M5S1Dg+4TrDwfMewQ6p/rmMueb7tWZjQ1rx4Loy1ArBggoqGpfqqdI4rondg=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.0.tgz"
  "version" "4.1.0"

"ansi-regex@^5.0.0":
  "integrity" "sha512-bY6fj56OUQ0hU1KjFNDQuJFezqKdrAyFdIevADiqrWHwSlbmBNMHp5ak2f40Pm8JTFyM2mqxkG6ngkHO11f/lg=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.0.tgz"
  "version" "5.0.0"

"ansi-styles@^3.2.0", "ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0", "ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^5.0.0":
  "integrity" "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz"
  "version" "5.2.0"

"anymatch@^2.0.0":
  "integrity" "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"anymatch@^3.0.3":
  "integrity" "sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"appdirsjs@^1.2.4":
  "integrity" "sha512-UyaAyzj+7XLoKhbXJi4zoAw8IDXCiLNCKfQEiuCsCCTkDmiG1vpCliQn/MoUvO3DZqCN1i6gOahokcFtNSIrVA=="
  "resolved" "https://registry.npmjs.org/appdirsjs/-/appdirsjs-1.2.5.tgz"
  "version" "1.2.5"

"argparse@^1.0.7":
  "integrity" "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "https://registry.npmjs.org/arr-diff/-/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.1.0":
  "integrity" "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg=="
  "resolved" "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-filter@~0.0.0":
  "integrity" "sha1-fajPLiZijtcygDWB/SH2fKzS7uw="
  "resolved" "https://registry.npmjs.org/array-filter/-/array-filter-0.0.1.tgz"
  "version" "0.0.1"

"array-includes@^3.1.2", "array-includes@^3.1.3":
  "integrity" "sha512-gcem1KlBU7c9rB+Rq8/3PPKsK2kjqeEBa3bD5kkQo4nYlOHQCJqIJFqBXDEfwaRuYTT4E+FxA9xez7Gf/e3Q7A=="
  "resolved" "https://registry.npmjs.org/array-includes/-/array-includes-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.2"
    "get-intrinsic" "^1.1.1"
    "is-string" "^1.0.5"

"array-map@~0.0.0":
  "integrity" "sha1-iKK6tz0c97zVwbEYoAP2b2ZfpmI="
  "resolved" "https://registry.npmjs.org/array-map/-/array-map-0.0.0.tgz"
  "version" "0.0.0"

"array-reduce@~0.0.0":
  "integrity" "sha1-FziZ0//Rx9k4PkR5Ul2+J4yrXys="
  "resolved" "https://registry.npmjs.org/array-reduce/-/array-reduce-0.0.0.tgz"
  "version" "0.0.0"

"array-union@^2.1.0":
  "integrity" "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw=="
  "resolved" "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
  "version" "2.1.0"

"array-unique@^0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "https://registry.npmjs.org/array-unique/-/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"array.prototype.flatmap@^1.2.4":
  "integrity" "sha512-r9Z0zYoxqHz60vvQbWEdXIEtCwHF0yxaWfno9qzXeNHvfyl3BZqygmGzb84dsubyaXLH4husF+NFgMSdpZhk2Q=="
  "resolved" "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.1"
    "function-bind" "^1.1.1"

"asap@~2.0.6":
  "integrity" "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY="
  "resolved" "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  "version" "2.0.6"

"assign-symbols@^1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"ast-types@0.14.2":
  "integrity" "sha512-O0yuUDnZeQDL+ncNGlJ78BiO4jnYI3bvMsD5prT0/nsgijG/LpNBIr63gTjVTNsiGkgQhiyCShTgxt8oXOrklA=="
  "resolved" "https://registry.npmjs.org/ast-types/-/ast-types-0.14.2.tgz"
  "version" "0.14.2"
  dependencies:
    "tslib" "^2.0.1"

"astral-regex@^1.0.0":
  "integrity" "sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg=="
  "resolved" "https://registry.npmjs.org/astral-regex/-/astral-regex-1.0.0.tgz"
  "version" "1.0.0"

"astral-regex@^2.0.0":
  "integrity" "sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ=="
  "resolved" "https://registry.npmjs.org/astral-regex/-/astral-regex-2.0.0.tgz"
  "version" "2.0.0"

"async-limiter@~1.0.0":
  "integrity" "sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ=="
  "resolved" "https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.1.tgz"
  "version" "1.0.1"

"async@^2.4.0":
  "integrity" "sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg=="
  "resolved" "https://registry.npmjs.org/async/-/async-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "lodash" "^4.17.14"

"asynckit@^0.4.0":
  "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.2":
  "integrity" "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="
  "resolved" "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"babel-core@^7.0.0-bridge.0":
  "integrity" "sha512-poPX9mZH/5CSanm50Q+1toVci6pv5KSRv/5TWCwtzQS5XEwn40BcCrgIeMFWP9CKKIniKXNxoIOnOq4VVlGXhg=="
  "resolved" "https://registry.npmjs.org/babel-core/-/babel-core-7.0.0-bridge.0.tgz"
  "version" "7.0.0-bridge.0"

"babel-eslint@^10.1.0":
  "integrity" "sha512-ifWaTHQ0ce+448CYop8AdrQiBsGrnC+bMgfyKFdi6EsPLTAWG+QfyDeM6OH+FmWnKvEq5NnBMLvlBUPKQZoDSg=="
  "resolved" "https://registry.npmjs.org/babel-eslint/-/babel-eslint-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    "eslint-visitor-keys" "^1.0.0"
    "resolve" "^1.12.0"

"babel-jest@^27.2.0":
  "integrity" "sha512-bS2p+KGGVVmWXBa8+i6SO/xzpiz2Q/2LnqLbQknPKefWXVZ67YIjA4iXup/jMOEZplga9PpWn+wrdb3UdDwRaA=="
  "resolved" "https://registry.npmjs.org/babel-jest/-/babel-jest-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/transform" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/babel__core" "^7.1.14"
    "babel-plugin-istanbul" "^6.0.0"
    "babel-preset-jest" "^27.2.0"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "slash" "^3.0.0"

"babel-plugin-dynamic-import-node@^2.3.3":
  "integrity" "sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ=="
  "resolved" "https://registry.npmjs.org/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "object.assign" "^4.1.0"

"babel-plugin-istanbul@^6.0.0":
  "integrity" "sha512-AF55rZXpe7trmEylbaE1Gv54wn6rwU03aptvRoVIGP8YykoSxqdVLV1TfwflBCE/QtHmqtP8SWlTENqbK8GCSQ=="
  "resolved" "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-instrument" "^4.0.0"
    "test-exclude" "^6.0.0"

"babel-plugin-jest-hoist@^27.2.0":
  "integrity" "sha512-TOux9khNKdi64mW+0OIhcmbAn75tTlzKhxmiNXevQaPbrBYK7YKjP1jl6NHTJ6XR5UgUrJbCnWlKVnJn29dfjw=="
  "resolved" "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.0.0"
    "@types/babel__traverse" "^7.0.6"

"babel-plugin-polyfill-corejs2@^0.2.2":
  "integrity" "sha512-kISrENsJ0z5dNPq5eRvcctITNHYXWOA4DUZRFYCz3jYCcvTb/A546LIddmoGNMVYg2U38OyFeNosQwI9ENTqIQ=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.2.2"
    "semver" "^6.1.1"

"babel-plugin-polyfill-corejs3@^0.2.2":
  "integrity" "sha512-z3HnJE5TY/j4EFEa/qpQMSbcUJZ5JQi+3UFjXzn6pQCmIKc5Ug5j98SuYyH+m4xQnvKlMDIW4plLfgyVnd0IcQ=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.2.4.tgz"
  "version" "0.2.4"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.2.2"
    "core-js-compat" "^3.14.0"

"babel-plugin-polyfill-regenerator@^0.2.2":
  "integrity" "sha512-Goy5ghsc21HgPDFtzRkSirpZVW35meGoTmTOb2bxqdl60ghub4xOidgNTHaZfQ2FaxQsKmwvXtOAkcIS4SMBWg=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.2.2"

"babel-plugin-syntax-trailing-function-commas@^7.0.0-beta.0":
  "integrity" "sha512-Xj9XuRuz3nTSbaTXWv3itLOcxyF4oPD8douBBmj7U9BBC6nEBYfyOJYQMf/8PJAFotC62UY5dFfIGEPr7WswzQ=="
  "resolved" "https://registry.npmjs.org/babel-plugin-syntax-trailing-function-commas/-/babel-plugin-syntax-trailing-function-commas-7.0.0-beta.0.tgz"
  "version" "7.0.0-beta.0"

"babel-preset-current-node-syntax@^1.0.0":
  "integrity" "sha512-M7LQ0bxarkxQoN+vz5aJPsLBn77n8QgTFmo8WK0/44auK2xlCXrYcUxHFxgU7qW5Yzw/CjmLRK2uJzaCd7LvqQ=="
  "resolved" "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

"babel-preset-fbjs@^3.4.0":
  "integrity" "sha512-9ywCsCvo1ojrw0b+XYk7aFvTH6D9064t0RIL1rtMf3nsa02Xw41MS7sZw216Im35xj/UY0PDBQsa1brUDDF1Ow=="
  "resolved" "https://registry.npmjs.org/babel-preset-fbjs/-/babel-preset-fbjs-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-syntax-class-properties" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-member-expression-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-super" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-property-literals" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    "babel-plugin-syntax-trailing-function-commas" "^7.0.0-beta.0"

"babel-preset-jest@^27.2.0":
  "integrity" "sha512-z7MgQ3peBwN5L5aCqBKnF6iqdlvZvFUQynEhu0J+X9nHLU72jO3iY331lcYrg+AssJ8q7xsv5/3AICzVmJ/wvg=="
  "resolved" "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "babel-plugin-jest-hoist" "^27.2.0"
    "babel-preset-current-node-syntax" "^1.0.0"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base@^0.11.1":
  "integrity" "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg=="
  "resolved" "https://registry.npmjs.org/base/-/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-js@^1.1.2", "base64-js@^1.5.1":
  "integrity" "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="
  "resolved" "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"big-integer@^1.6.44":
  "integrity" "sha512-j51egjPa7/i+RdiRuJbPdJ2FIUYYPhvYLjzoYbcMMm62ooO6F94fETG4MTs46zPAF9Brs04OajboA/qTGuz78w=="
  "resolved" "https://registry.npmjs.org/big-integer/-/big-integer-1.6.48.tgz"
  "version" "1.6.48"

"bplist-creator@0.0.8":
  "integrity" "sha512-Za9JKzD6fjLC16oX2wsXfc+qBEhJBJB1YPInoAQpMLhDuj5aVOv1baGeIQSq1Fr3OCqzvsoQcSBSwGId/Ja2PA=="
  "resolved" "https://registry.npmjs.org/bplist-creator/-/bplist-creator-0.0.8.tgz"
  "version" "0.0.8"
  dependencies:
    "stream-buffers" "~2.2.0"

"bplist-parser@0.2.0":
  "integrity" "sha512-z0M+byMThzQmD9NILRniCUXYsYpjwnlO8N5uCFaCqIOpqRsJCrQL9NK3JsD67CN5a08nF5oIL2bD6loTdHOuKw=="
  "resolved" "https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "big-integer" "^1.6.44"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^2.3.1":
  "integrity" "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^3.0.1":
  "integrity" "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"browser-process-hrtime@^1.0.0":
  "integrity" "sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow=="
  "resolved" "https://registry.npmjs.org/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz"
  "version" "1.0.0"

"browserslist@^4.16.6", "browserslist@^4.17.0":
  "integrity" "sha512-g2BJ2a0nEYvEFQC208q8mVAhfNwpZ5Mu8BwgtCdZKO3qx98HChmeg448fPdUzld8aFmfLgVh7yymqV+q1lJZ5g=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-4.17.0.tgz"
  "version" "4.17.0"
  dependencies:
    "caniuse-lite" "^1.0.30001254"
    "colorette" "^1.3.0"
    "electron-to-chromium" "^1.3.830"
    "escalade" "^3.1.1"
    "node-releases" "^1.1.75"

"bser@2.1.1":
  "integrity" "sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ=="
  "resolved" "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "node-int64" "^0.4.0"

"buffer-from@^1.0.0":
  "integrity" "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="
  "resolved" "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz"
  "version" "3.0.0"

"cache-base@^1.0.1":
  "integrity" "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ=="
  "resolved" "https://registry.npmjs.org/cache-base/-/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"call-bind@^1.0.0", "call-bind@^1.0.2":
  "integrity" "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA=="
  "resolved" "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.0.2"

"caller-callsite@^2.0.0":
  "integrity" "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ="
  "resolved" "https://registry.npmjs.org/caller-callsite/-/caller-callsite-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "callsites" "^2.0.0"

"caller-path@^2.0.0":
  "integrity" "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ="
  "resolved" "https://registry.npmjs.org/caller-path/-/caller-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-callsite" "^2.0.0"

"callsites@^2.0.0":
  "integrity" "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-2.0.0.tgz"
  "version" "2.0.0"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camelcase@^5.0.0", "camelcase@^5.3.1":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^6.0.0":
  "integrity" "sha512-c7wVvbw3f37nuobQNtgsgG9POC9qMbNuMQmTCqZv23b6MIz0fcYpBiOlv9gEN/hdLdnZTDQhg6e9Dq5M1vKvfg=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-6.2.0.tgz"
  "version" "6.2.0"

"camelcase@^6.2.0":
  "integrity" "sha512-c7wVvbw3f37nuobQNtgsgG9POC9qMbNuMQmTCqZv23b6MIz0fcYpBiOlv9gEN/hdLdnZTDQhg6e9Dq5M1vKvfg=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-6.2.0.tgz"
  "version" "6.2.0"

"caniuse-lite@^1.0.30001254":
  "integrity" "sha512-JN49KplOgHSXpIsVSF+LUyhD8PUp6xPpAXeRrrcBh4KBeP7W864jHn6RvzJgDlrReyeVjMFJL3PLpPvKIxlIHA=="
  "resolved" "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001257.tgz"
  "version" "1.0.30001257"

"capture-exit@^2.0.0":
  "integrity" "sha512-PiT/hQmTonHhl/HFGN+Lx3JJUznrVYJ3+AQsnthneZbvW7x+f08Tk7yLJTLEOUvBTbduLeeBkxEaYXUOUrRq6g=="
  "resolved" "https://registry.npmjs.org/capture-exit/-/capture-exit-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "rsvp" "^4.8.4"

"chalk@^2.0.0", "chalk@^2.0.1", "chalk@^2.4.2":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^3.0.0":
  "integrity" "sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.0.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"char-regex@^1.0.2":
  "integrity" "sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw=="
  "resolved" "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz"
  "version" "1.0.2"

"ci-info@^2.0.0":
  "integrity" "sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ=="
  "resolved" "https://registry.npmjs.org/ci-info/-/ci-info-2.0.0.tgz"
  "version" "2.0.0"

"ci-info@^3.1.1":
  "integrity" "sha512-dVqRX7fLUm8J6FgHJ418XuIgDLZDkYcDFTeL6TA2gt5WlIZUQrrH6EZrNClwT/H0FateUsZkGIOPRrLbP+PR9A=="
  "resolved" "https://registry.npmjs.org/ci-info/-/ci-info-3.2.0.tgz"
  "version" "3.2.0"

"cjs-module-lexer@^1.0.0":
  "integrity" "sha512-cOU9usZw8/dXIXKtwa8pM0OTJQuJkxMN6w30csNRUerHfeQ5R6U3kkU/FtJeIf3M202OHfY2U8ccInBG7/xogA=="
  "resolved" "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.2.2.tgz"
  "version" "1.2.2"

"class-utils@^0.3.5":
  "integrity" "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg=="
  "resolved" "https://registry.npmjs.org/class-utils/-/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"cli-cursor@^2.1.0":
  "integrity" "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU="
  "resolved" "https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-spinners@^2.0.0":
  "integrity" "sha512-t+4/y50K/+4xcCRosKkA7W4gTr1MySvLV0q+PxmG7FJ5g+66ChKurYjxBCjHggHH3HA5Hh9cy+lcUGWDqVH+4Q=="
  "resolved" "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.6.0.tgz"
  "version" "2.6.0"

"cliui@^6.0.0":
  "integrity" "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"cliui@^7.0.2":
  "integrity" "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^7.0.0"

"clone-deep@^4.0.1":
  "integrity" "sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ=="
  "resolved" "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"
    "kind-of" "^6.0.2"
    "shallow-clone" "^3.0.0"

"clone@^1.0.2":
  "integrity" "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="
  "resolved" "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  "version" "1.0.4"

"co@^4.6.0":
  "integrity" "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="
  "resolved" "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
  "version" "4.6.0"

"collect-v8-coverage@^1.0.0":
  "integrity" "sha512-iBPtljfCNcTKNAto0KEtDfZ3qzjJvqE3aTGZsbhjSBlorqpXJlaWWtPO35D+ZImoC3KWejX64o+yPGxhWSTzfg=="
  "resolved" "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.1.tgz"
  "version" "1.0.1"

"collection-visit@^1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "https://registry.npmjs.org/collection-visit/-/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"colorette@^1.0.7", "colorette@^1.3.0":
  "integrity" "sha512-Y2oEozpomLn7Q3HFP7dpww7AtMJplbM9lGZP6RDfHqmbeRjiwRg4n6VM6j4KLmRke85uWEI7JqF17f3pqdRA0g=="
  "resolved" "https://registry.npmjs.org/colorette/-/colorette-1.4.0.tgz"
  "version" "1.4.0"

"colors@^1.1.2":
  "integrity" "sha512-a+UqTh4kgZg/SlGvfbzDHpgRu7AAQOmmqRHJnxhRZICKFUT91brVhNNt58CMWU9PsBbv3PDCZUHbVxuDiH2mtA=="
  "resolved" "https://registry.npmjs.org/colors/-/colors-1.4.0.tgz"
  "version" "1.4.0"

"combined-stream@^1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"command-exists@^1.2.8":
  "integrity" "sha512-LTQ/SGc+s0Xc0Fu5WaKnR0YiygZkm9eKFvyS+fRsU7/ZWFF8ykFM6Pc9aCVf1+xasOOZpO3BAVgVrKvsqKHV7w=="
  "resolved" "https://registry.npmjs.org/command-exists/-/command-exists-1.2.9.tgz"
  "version" "1.2.9"

"commander@^2.19.0":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@~2.13.0":
  "integrity" "sha512-MVuS359B+YzaWqjCL/c+22gfryv+mCBPHAv3zyVI2GN8EY6IRP8VwtasXn8jyyhvvq84R4ImN1OKRtcbIasjYA=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.13.0.tgz"
  "version" "2.13.0"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-emitter@^1.2.1":
  "integrity" "sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg=="
  "resolved" "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"compressible@~2.0.16":
  "integrity" "sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg=="
  "resolved" "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression@^1.7.1":
  "integrity" "sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ=="
  "resolved" "https://registry.npmjs.org/compression/-/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"connect@^3.6.5":
  "integrity" "sha512-ZqRXc+tZukToSNmh5C2iWMSoV3X1YUcPbqEM4DkEG5tNQXrQUZCNVGGv3IuicnkMtPfGf3Xtp8WCXs295iQ1pQ=="
  "resolved" "https://registry.npmjs.org/connect/-/connect-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "debug" "2.6.9"
    "finalhandler" "1.1.2"
    "parseurl" "~1.3.3"
    "utils-merge" "1.0.1"

"convert-source-map@^1.4.0", "convert-source-map@^1.6.0", "convert-source-map@^1.7.0":
  "integrity" "sha512-+OQdjP49zViI/6i7nIJpA8rAl4sV/JdPfU9nZs3VqOwGIgizICvuN2ru6fMd+4llL0tar18UYJXfZ/TWtmhUjA=="
  "resolved" "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"copy-descriptor@^0.1.0":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "https://registry.npmjs.org/copy-descriptor/-/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"core-js-compat@^3.14.0", "core-js-compat@^3.16.0":
  "integrity" "sha512-+in61CKYs4hQERiADCJsdgewpdl/X0GhEX77pjKgbeibXviIt2oxEjTc8O2fqHX8mDdBrDvX8MYD/RYsBv4OiA=="
  "resolved" "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.17.3.tgz"
  "version" "3.17.3"
  dependencies:
    "browserslist" "^4.17.0"
    "semver" "7.0.0"

"core-util-is@~1.0.0":
  "integrity" "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="
  "resolved" "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"cosmiconfig@^5.0.5", "cosmiconfig@^5.1.0":
  "integrity" "sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA=="
  "resolved" "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "^2.0.0"
    "is-directory" "^0.3.1"
    "js-yaml" "^3.13.1"
    "parse-json" "^4.0.0"

"cross-spawn@^6.0.0":
  "integrity" "sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^7.0.2", "cross-spawn@^7.0.3":
  "integrity" "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"cssom@^0.4.4":
  "integrity" "sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw=="
  "resolved" "https://registry.npmjs.org/cssom/-/cssom-0.4.4.tgz"
  "version" "0.4.4"

"cssom@~0.3.6":
  "integrity" "sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg=="
  "resolved" "https://registry.npmjs.org/cssom/-/cssom-0.3.8.tgz"
  "version" "0.3.8"

"cssstyle@^2.3.0":
  "integrity" "sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A=="
  "resolved" "https://registry.npmjs.org/cssstyle/-/cssstyle-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "cssom" "~0.3.6"

"data-urls@^2.0.0":
  "integrity" "sha512-X5eWTSXO/BJmpdIKCRuKUgSCgAN0OwliVK3yPKbwIWU1Tdw5BRajxlzMidvh+gwko9AfQ9zIj52pzF91Q3YAvQ=="
  "resolved" "https://registry.npmjs.org/data-urls/-/data-urls-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "abab" "^2.0.3"
    "whatwg-mimetype" "^2.3.0"
    "whatwg-url" "^8.0.0"

"dayjs@^1.8.15":
  "integrity" "sha512-P6twpd70BcPK34K26uJ1KT3wlhpuOAPoMwJzpsIWUxHZ7wpmbdZL/hQqBDfz7hGurYSa5PhzdhDHtt319hL3ig=="
  "resolved" "https://registry.npmjs.org/dayjs/-/dayjs-1.10.7.tgz"
  "version" "1.10.7"

"debug@^2.2.0":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.3.3":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^4.0.1", "debug@^4.1.0", "debug@^4.1.1", "debug@^4.3.1", "debug@4":
  "integrity" "sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "ms" "2.1.2"

"debug@2.6.9":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"decamelize@^1.2.0":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decimal.js@^10.2.1":
  "integrity" "sha512-V0pfhfr8suzyPGOx3nmq4aHqabehUZn6Ch9kyFpV79TGDTWFmHqUqXdabR7QHqxzrYolF4+tVmJhUG4OURg5dQ=="
  "resolved" "https://registry.npmjs.org/decimal.js/-/decimal.js-10.3.1.tgz"
  "version" "10.3.1"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"dedent@^0.7.0":
  "integrity" "sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw="
  "resolved" "https://registry.npmjs.org/dedent/-/dedent-0.7.0.tgz"
  "version" "0.7.0"

"deep-is@^0.1.3", "deep-is@~0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"deepmerge@^3.2.0":
  "integrity" "sha512-GRQOafGHwMHpjPx9iCvTgpu9NojZ49q794EEL94JVEw6VaeA8XTUyBKvAkOOjBX9oJNiV6G3P+T+tihFjo2TqA=="
  "resolved" "https://registry.npmjs.org/deepmerge/-/deepmerge-3.3.0.tgz"
  "version" "3.3.0"

"deepmerge@^4.2.2":
  "integrity" "sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg=="
  "resolved" "https://registry.npmjs.org/deepmerge/-/deepmerge-4.2.2.tgz"
  "version" "4.2.2"

"defaults@^1.0.3":
  "integrity" "sha1-xlYFHpgX2f8I7YgUd/P+QBnz730="
  "resolved" "https://registry.npmjs.org/defaults/-/defaults-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "clone" "^1.0.2"

"define-properties@^1.1.3":
  "integrity" "sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ=="
  "resolved" "https://registry.npmjs.org/define-properties/-/define-properties-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "object-keys" "^1.0.12"

"define-property@^0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ=="
  "resolved" "https://registry.npmjs.org/define-property/-/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"delayed-stream@~1.0.0":
  "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"denodeify@^1.2.1":
  "integrity" "sha1-OjYof1A05pnnV3kBBSwubJQlFjE="
  "resolved" "https://registry.npmjs.org/denodeify/-/denodeify-1.2.1.tgz"
  "version" "1.2.1"

"depd@~1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  "version" "1.1.2"

"destroy@~1.0.4":
  "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
  "resolved" "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz"
  "version" "1.0.4"

"detect-newline@^3.0.0":
  "integrity" "sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA=="
  "resolved" "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz"
  "version" "3.1.0"

"diff-sequences@^27.0.6":
  "integrity" "sha512-ag6wfpBFyNXZ0p8pcuIDS//D8H062ZQJ3fzYxjpmeKjnz8W4pekL3AI8VohmyZmsWW2PWaHgjsmqR6L13101VQ=="
  "resolved" "https://registry.npmjs.org/diff-sequences/-/diff-sequences-27.0.6.tgz"
  "version" "27.0.6"

"dir-glob@^3.0.1":
  "integrity" "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA=="
  "resolved" "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "path-type" "^4.0.0"

"doctrine@^2.1.0":
  "integrity" "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw=="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"doctrine@^3.0.0":
  "integrity" "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w=="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"domexception@^2.0.1":
  "integrity" "sha512-yxJ2mFy/sibVQlu5qHjOkf9J3K6zgmCxgJ94u2EdvDOV09H+32LtRswEcUsmUWN72pVLOEnTSRaIVVzVQgS0dg=="
  "resolved" "https://registry.npmjs.org/domexception/-/domexception-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "webidl-conversions" "^5.0.0"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"electron-to-chromium@^1.3.830":
  "integrity" "sha512-Ney3pHOJBWkG/AqYjrW0hr2AUCsao+2uvq9HUlRP8OlpSdk/zOHOUJP7eu0icDvePC9DlgffuelP4TnOJmMRUg=="
  "resolved" "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.3.836.tgz"
  "version" "1.3.836"

"emittery@^0.8.1":
  "integrity" "sha512-uDfvUjVrfGJJhymx/kz6prltenw1u7WrCg1oa94zYY8xxVpLLUu045LAT0dhDZdXG58/EpPL/5kA180fQ/qudg=="
  "resolved" "https://registry.npmjs.org/emittery/-/emittery-0.8.1.tgz"
  "version" "0.8.1"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"end-of-stream@^1.1.0":
  "integrity" "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="
  "resolved" "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"enquirer@^2.3.5":
  "integrity" "sha512-yjNnPr315/FjS4zIsUxYguYUPP2e1NK4d7E7ZOLiyYCcbFBiTMyID+2wvm2w6+pZ/odMA7cRkjhsPbltwBOrLg=="
  "resolved" "https://registry.npmjs.org/enquirer/-/enquirer-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "ansi-colors" "^4.1.1"

"envinfo@^7.7.2":
  "integrity" "sha512-/o+BXHmB7ocbHEAs6F2EnG0ogybVVUdkRunTT2glZU9XAaGmhqskrvKwqXuDfNjEO0LZKWdejEEpnq8aM0tOaw=="
  "resolved" "https://registry.npmjs.org/envinfo/-/envinfo-7.8.1.tgz"
  "version" "7.8.1"

"error-ex@^1.3.1":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.6":
  "integrity" "sha512-d51brTeqC+BHlwF0BhPtcYgF5nlzf9ZZ0ZIUQNZpc9ZB9qw5IJ2diTrBY9jlCJkTLITYPjmiX6OWCwH+fuyNgQ=="
  "resolved" "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "stackframe" "^1.1.1"

"errorhandler@^1.5.0":
  "integrity" "sha512-rcOwbfvP1WTViVoUjcfZicVzjhjTuhSMntHh6mW3IrEiyE6mJyXvsToJUJGlGlw/2xU9P5whlWNGlIDVeCiT4A=="
  "resolved" "https://registry.npmjs.org/errorhandler/-/errorhandler-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "accepts" "~1.3.7"
    "escape-html" "~1.0.3"

"es-abstract@^1.18.0-next.1", "es-abstract@^1.18.0-next.2", "es-abstract@^1.18.2":
  "integrity" "sha512-kAeIT4cku5eNLNuUKhlmtuk1/TRZvQoYccn6TO0cSVdf1kzB0T7+dYuVK9MWM7l+/53W2Q8M7N2c6MQvhXFcUQ=="
  "resolved" "https://registry.npmjs.org/es-abstract/-/es-abstract-1.18.6.tgz"
  "version" "1.18.6"
  dependencies:
    "call-bind" "^1.0.2"
    "es-to-primitive" "^1.2.1"
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.1.1"
    "get-symbol-description" "^1.0.0"
    "has" "^1.0.3"
    "has-symbols" "^1.0.2"
    "internal-slot" "^1.0.3"
    "is-callable" "^1.2.4"
    "is-negative-zero" "^2.0.1"
    "is-regex" "^1.1.4"
    "is-string" "^1.0.7"
    "object-inspect" "^1.11.0"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.2"
    "string.prototype.trimend" "^1.0.4"
    "string.prototype.trimstart" "^1.0.4"
    "unbox-primitive" "^1.0.1"

"es-to-primitive@^1.2.1":
  "integrity" "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA=="
  "resolved" "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"escalade@^3.1.1":
  "integrity" "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw=="
  "resolved" "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^2.0.0":
  "integrity" "sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"
  "version" "2.0.0"

"escape-string-regexp@^4.0.0":
  "integrity" "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"escodegen@^2.0.0":
  "integrity" "sha512-mmHKys/C8BFUGI+MAWNcSYoORYLMdPzjrknd2Vc+bUsjN5bXcr8EhrNB+UTqfL1y3I9c4fw2ihgtMPQLBRiQxw=="
  "resolved" "https://registry.npmjs.org/escodegen/-/escodegen-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "esprima" "^4.0.1"
    "estraverse" "^5.2.0"
    "esutils" "^2.0.2"
    "optionator" "^0.8.1"
  optionalDependencies:
    "source-map" "~0.6.1"

"eslint-config-prettier@^6.10.1":
  "integrity" "sha512-a1+kOYLR8wMGustcgAjdydMsQ2A/2ipRPwRKUmfYaSxc9ZPcrku080Ctl6zrZzZNs/U82MjSv+qKREkoq3bJaw=="
  "resolved" "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-6.15.0.tgz"
  "version" "6.15.0"
  dependencies:
    "get-stdin" "^6.0.0"

"eslint-plugin-eslint-comments@^3.1.2":
  "integrity" "sha512-0jkOl0hfojIHHmEHgmNdqv4fmh7300NdpA9FFpF7zaoLvB/QeXOGNLIo86oAveJFrfB1p05kC8hpEMHM8DwWVQ=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-eslint-comments/-/eslint-plugin-eslint-comments-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"
    "ignore" "^5.0.5"

"eslint-plugin-flowtype@2.50.3":
  "integrity" "sha512-X+AoKVOr7Re0ko/yEXyM5SSZ0tazc6ffdIOocp2fFUlWoDt7DV0Bz99mngOkAFLOAWjqRA5jPwqUCbrx13XoxQ=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-flowtype/-/eslint-plugin-flowtype-2.50.3.tgz"
  "version" "2.50.3"
  dependencies:
    "lodash" "^4.17.10"

"eslint-plugin-jest@22.4.1":
  "integrity" "sha512-gcLfn6P2PrFAVx3AobaOzlIEevpAEf9chTpFZz7bYfc7pz8XRv7vuKTIE4hxPKZSha6XWKKplDQ0x9Pq8xX2mg=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-jest/-/eslint-plugin-jest-22.4.1.tgz"
  "version" "22.4.1"

"eslint-plugin-prettier@3.1.2":
  "integrity" "sha512-GlolCC9y3XZfv3RQfwGew7NnuFDKsfI4lbvRK+PIIo23SFH+LemGs4cKwzAaRa+Mdb+lQO/STaIayno8T5sJJA=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "prettier-linter-helpers" "^1.0.0"

"eslint-plugin-react-hooks@^4.0.7":
  "integrity" "sha512-623WEiZJqxR7VdxFCKLI6d6LLpwJkGPYKODnkH3D7WpOG5KM8yWueBd8TLsNAetEJNF5iJmolaAKO3F8yzyVBQ=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.2.0.tgz"
  "version" "4.2.0"

"eslint-plugin-react-native-globals@^0.1.1":
  "integrity" "sha512-9aEPf1JEpiTjcFAmmyw8eiIXmcNZOqaZyHO77wgm0/dWfT/oxC1SrIq8ET38pMxHYrcB6Uew+TzUVsBeczF88g=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-react-native-globals/-/eslint-plugin-react-native-globals-0.1.2.tgz"
  "version" "0.1.2"

"eslint-plugin-react-native@^3.10.0":
  "integrity" "sha512-7F3OTwrtQPfPFd+VygqKA2VZ0f2fz0M4gJmry/TRE18JBb94/OtMxwbL7Oqwu7FGyrdeIOWnXQbBAveMcSTZIA=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-react-native/-/eslint-plugin-react-native-3.11.0.tgz"
  "version" "3.11.0"
  dependencies:
    "@babel/traverse" "^7.7.4"
    "eslint-plugin-react-native-globals" "^0.1.1"

"eslint-plugin-react@^7.20.0":
  "integrity" "sha512-P4j9K1dHoFXxDNP05AtixcJEvIT6ht8FhYKsrkY0MPCPaUMYijhpWwNiRDZVtA8KFuZOkGSeft6QwH8KuVpJug=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.25.1.tgz"
  "version" "7.25.1"
  dependencies:
    "array-includes" "^3.1.3"
    "array.prototype.flatmap" "^1.2.4"
    "doctrine" "^2.1.0"
    "estraverse" "^5.2.0"
    "has" "^1.0.3"
    "jsx-ast-utils" "^2.4.1 || ^3.0.0"
    "minimatch" "^3.0.4"
    "object.entries" "^1.1.4"
    "object.fromentries" "^2.0.4"
    "object.values" "^1.1.4"
    "prop-types" "^15.7.2"
    "resolve" "^2.0.0-next.3"
    "string.prototype.matchall" "^4.0.5"

"eslint-scope@^5.1.1":
  "integrity" "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-utils@^2.1.0":
  "integrity" "sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg=="
  "resolved" "https://registry.npmjs.org/eslint-utils/-/eslint-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"eslint-utils@^3.0.0":
  "integrity" "sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA=="
  "resolved" "https://registry.npmjs.org/eslint-utils/-/eslint-utils-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "eslint-visitor-keys" "^2.0.0"

"eslint-visitor-keys@^1.0.0":
  "integrity" "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint-visitor-keys@^1.1.0":
  "integrity" "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint-visitor-keys@^1.3.0":
  "integrity" "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint-visitor-keys@^2.0.0":
  "integrity" "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  "version" "2.1.0"

"eslint@*", "eslint@^3 || ^4 || ^5 || ^6 || ^7", "eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0", "eslint@^3.17.0 || ^4 || ^5 || ^6 || ^7", "eslint@^5.0.0 || ^6.0.0 || ^7.0.0", "eslint@^7.32.0", "eslint@>= 4.12.1", "eslint@>= 5.0.0", "eslint@>=2.0.0", "eslint@>=3.14.1", "eslint@>=4.19.1", "eslint@>=5", "eslint@>=7":
  "integrity" "sha512-VHZ8gX+EDfz+97jGcgyGCyRia/dPOd6Xh9yPv8Bl1+SoaIwD+a/vlrOmGRUyOYu7MwUhc7CxqeaDZU13S4+EpA=="
  "resolved" "https://registry.npmjs.org/eslint/-/eslint-7.32.0.tgz"
  "version" "7.32.0"
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    "ajv" "^6.10.0"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.2"
    "debug" "^4.0.1"
    "doctrine" "^3.0.0"
    "enquirer" "^2.3.5"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^5.1.1"
    "eslint-utils" "^2.1.0"
    "eslint-visitor-keys" "^2.0.0"
    "espree" "^7.3.1"
    "esquery" "^1.4.0"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^6.0.1"
    "functional-red-black-tree" "^1.0.1"
    "glob-parent" "^5.1.2"
    "globals" "^13.6.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.0.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "js-yaml" "^3.13.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.4.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.0.4"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.1"
    "progress" "^2.0.0"
    "regexpp" "^3.1.0"
    "semver" "^7.2.1"
    "strip-ansi" "^6.0.0"
    "strip-json-comments" "^3.1.0"
    "table" "^6.0.9"
    "text-table" "^0.2.0"
    "v8-compile-cache" "^2.0.3"

"espree@^7.3.0", "espree@^7.3.1":
  "integrity" "sha512-v3JCNCE64umkFpmkFGqzVKsOT0tN1Zr+ueqLZfpV1Ob8e+CEgPWa+OxCoGH3tnhimMKIaBm4m/vaRpJ/krRz2g=="
  "resolved" "https://registry.npmjs.org/espree/-/espree-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "acorn" "^7.4.0"
    "acorn-jsx" "^5.3.1"
    "eslint-visitor-keys" "^1.3.0"

"esprima@^4.0.0", "esprima@^4.0.1", "esprima@~4.0.0":
  "integrity" "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="
  "resolved" "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.4.0":
  "integrity" "sha512-cCDispWt5vHHtwMY2YrAQ4ibFkAL8RbH5YGBnZBc90MolvvfkkQcJro/aZiAQUlQ3qgrYS6D6v8Gc5G5CQsc9w=="
  "resolved" "https://registry.npmjs.org/esquery/-/esquery-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0", "estraverse@^5.2.0":
  "integrity" "sha512-BxbNGGNm0RyRYvUdHpIwv9IWzeM9XClbOxwoATuFdOE7ZE6wHL+HQ5T8hoPM+zHvmKzzsEqhgy0GrQ5X13afiQ=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-5.2.0.tgz"
  "version" "5.2.0"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-target-shim@^5.0.0", "event-target-shim@^5.0.1":
  "integrity" "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ=="
  "resolved" "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz"
  "version" "5.0.1"

"exec-sh@^0.3.2":
  "integrity" "sha512-nQn+hI3yp+oD0huYhKwvYI32+JFeq+XkNcD1GAo3Y/MjxsfVGmrrzrnzjWiNY6f+pUCP440fThsFh5gZrRAU/w=="
  "resolved" "https://registry.npmjs.org/exec-sh/-/exec-sh-0.3.6.tgz"
  "version" "0.3.6"

"execa@^1.0.0":
  "integrity" "sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^5.0.0":
  "integrity" "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cross-spawn" "^7.0.3"
    "get-stream" "^6.0.0"
    "human-signals" "^2.1.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.1"
    "onetime" "^5.1.2"
    "signal-exit" "^3.0.3"
    "strip-final-newline" "^2.0.0"

"exit@^0.1.2":
  "integrity" "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw="
  "resolved" "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz"
  "version" "0.1.2"

"expand-brackets@^2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "https://registry.npmjs.org/expand-brackets/-/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"expect@^27.2.0":
  "integrity" "sha512-oOTbawMQv7AK1FZURbPTgGSzmhxkjFzoARSvDjOMnOpeWuYQx1tP6rXu9MIX5mrACmyCAM7fSNP8IJO2f1p0CQ=="
  "resolved" "https://registry.npmjs.org/expect/-/expect-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/types" "^27.1.1"
    "ansi-styles" "^5.0.0"
    "jest-get-type" "^27.0.6"
    "jest-matcher-utils" "^27.2.0"
    "jest-message-util" "^27.2.0"
    "jest-regex-util" "^27.0.6"

"extend-shallow@^2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0", "extend-shallow@^3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extglob@^2.0.4":
  "integrity" "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw=="
  "resolved" "https://registry.npmjs.org/extglob/-/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-diff@^1.1.2":
  "integrity" "sha512-xJuoT5+L99XlZ8twedaRf6Ax2TgQVxvgZOYoPKqZufmJib0tL2tegPBOZb1pVNgIhlqDlA0eO0c3wBvQcmzx4w=="
  "resolved" "https://registry.npmjs.org/fast-diff/-/fast-diff-1.2.0.tgz"
  "version" "1.2.0"

"fast-glob@^3.1.1":
  "integrity" "sha512-rYGMRwip6lUMvYD3BTScMwT1HtAs2d71SMv66Vrxs0IekGZEjhM0pcMfjQPnknBt2zeCwQMEupiN02ZP4DiT1Q=="
  "resolved" "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.4"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6", "fast-levenshtein@~2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastq@^1.6.0":
  "integrity" "sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw=="
  "resolved" "https://registry.npmjs.org/fastq/-/fastq-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "reusify" "^1.0.4"

"fb-watchman@^2.0.0":
  "integrity" "sha512-DkPJKQeY6kKwmuMretBhr7G6Vodr7bFwDYTXIkfG1gjvNpaxBTQV3PbXg6bR1c1UP4jPOX0jHUbbHANL9vRjVg=="
  "resolved" "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "bser" "2.1.1"

"file-entry-cache@^6.0.1":
  "integrity" "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg=="
  "resolved" "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "flat-cache" "^3.0.4"

"fill-range@^4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"fill-range@^7.0.1":
  "integrity" "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@1.1.2":
  "integrity" "sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA=="
  "resolved" "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "statuses" "~1.5.0"
    "unpipe" "~1.0.0"

"find-cache-dir@^2.0.0":
  "integrity" "sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ=="
  "resolved" "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^2.0.0"
    "pkg-dir" "^3.0.0"

"find-up@^3.0.0":
  "integrity" "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "locate-path" "^3.0.0"

"find-up@^4.0.0", "find-up@^4.1.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"flat-cache@^3.0.4":
  "integrity" "sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg=="
  "resolved" "https://registry.npmjs.org/flat-cache/-/flat-cache-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "flatted" "^3.1.0"
    "rimraf" "^3.0.2"

"flatted@^3.1.0":
  "integrity" "sha512-JaTY/wtrcSyvXJl4IMFHPKyFur1sE9AUqc0QnhOaJ0CxHtAoIV8pYDzeEfAaNEtGkOfq4gr3LBFmdXW5mOQFnA=="
  "resolved" "https://registry.npmjs.org/flatted/-/flatted-3.2.2.tgz"
  "version" "3.2.2"

"flow-parser@^0.121.0", "flow-parser@0.*":
  "integrity" "sha512-1gIBiWJNR0tKUNv8gZuk7l9rVX06OuLzY9AoGio7y/JT4V1IZErEMEq2TJS+PFcw/y0RshZ1J/27VfK1UQzYVg=="
  "resolved" "https://registry.npmjs.org/flow-parser/-/flow-parser-0.121.0.tgz"
  "version" "0.121.0"

"for-in@^1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz"
  "version" "1.0.2"

"form-data@^3.0.0":
  "integrity" "sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"fragment-cache@^0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "https://registry.npmjs.org/fragment-cache/-/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  "version" "0.5.2"

"fs-extra@^1.0.0":
  "integrity" "sha1-zTzl9+fLYUWIP8rjGR6Yd/hYeVA="
  "resolved" "https://registry.npmjs.org/fs-extra/-/fs-extra-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^2.1.0"
    "klaw" "^1.0.0"

"fs-extra@^8.1.0":
  "integrity" "sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g=="
  "resolved" "https://registry.npmjs.org/fs-extra/-/fs-extra-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "graceful-fs" "^4.2.0"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"function-bind@^1.1.1":
  "integrity" "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="
  "resolved" "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="
  "resolved" "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.1", "get-caller-file@^2.0.5":
  "integrity" "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="
  "resolved" "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.0", "get-intrinsic@^1.1.1":
  "integrity" "sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q=="
  "resolved" "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.1"

"get-package-type@^0.1.0":
  "integrity" "sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q=="
  "resolved" "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz"
  "version" "0.1.0"

"get-stdin@^6.0.0":
  "integrity" "sha512-jp4tHawyV7+fkkSKyvjuLZswblUtz+SQKzSWnBbii16BuZksJlU1wuBYXY75r+duh/llF1ur6oNwi+2ZzjKZ7g=="
  "resolved" "https://registry.npmjs.org/get-stdin/-/get-stdin-6.0.0.tgz"
  "version" "6.0.0"

"get-stream@^4.0.0":
  "integrity" "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w=="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^6.0.0":
  "integrity" "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg=="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  "version" "6.0.1"

"get-symbol-description@^1.0.0":
  "integrity" "sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw=="
  "resolved" "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.1.1"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz"
  "version" "2.0.6"

"glob-parent@^5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob@^7.1.1", "glob@^7.1.2", "glob@^7.1.3", "glob@^7.1.4":
  "integrity" "sha512-OvD9ENzPLbegENnYP5UUfJIirTg4+XwMWGaQfQTY0JenxNvvIKP3U3/tAQSPIu/lHxXYSZmpXlUHeqAIdKzBLQ=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.1.7.tgz"
  "version" "7.1.7"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"globals@^11.1.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^13.6.0":
  "integrity" "sha512-08/xrJ7wQjK9kkkRoI3OFUBbLx4f+6x3SGwcPvQ0QH6goFDrOU2oyAWrmh3dJezu65buo+HBMzAMQy6rovVC3g=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-13.11.0.tgz"
  "version" "13.11.0"
  dependencies:
    "type-fest" "^0.20.2"

"globals@^13.9.0":
  "integrity" "sha512-08/xrJ7wQjK9kkkRoI3OFUBbLx4f+6x3SGwcPvQ0QH6goFDrOU2oyAWrmh3dJezu65buo+HBMzAMQy6rovVC3g=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-13.11.0.tgz"
  "version" "13.11.0"
  dependencies:
    "type-fest" "^0.20.2"

"globby@^11.0.3":
  "integrity" "sha512-9O4MVG9ioZJ08ffbcyVYyLOJLk5JQ688pJ4eMGLpdWLHq/Wr1D9BlriLQyL0E+jbkuePVZXYFj47QM/v093wHg=="
  "resolved" "https://registry.npmjs.org/globby/-/globby-11.0.4.tgz"
  "version" "11.0.4"
  dependencies:
    "array-union" "^2.1.0"
    "dir-glob" "^3.0.1"
    "fast-glob" "^3.1.1"
    "ignore" "^5.1.4"
    "merge2" "^1.3.0"
    "slash" "^3.0.0"

"graceful-fs@^4.1.11", "graceful-fs@^4.1.2", "graceful-fs@^4.1.3", "graceful-fs@^4.1.6", "graceful-fs@^4.1.9", "graceful-fs@^4.2.0", "graceful-fs@^4.2.4":
  "integrity" "sha512-qkIilPUYcNhJpd33n0GBXTB1MMPp14TxEsEs0pTrsSVucApsYzW5V+Q8Qxhik6KU3evy+qkAAowTByymK0avdg=="
  "resolved" "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.8.tgz"
  "version" "4.2.8"

"has-bigints@^1.0.1":
  "integrity" "sha512-LSBS2LjbNBTf6287JEbEzvJgftkF5qFkmCo9hDRpAzKhUOlJ+hx8dd4USs00SgsUNwc4617J9ki5YtEClM2ffA=="
  "resolved" "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.1.tgz"
  "version" "1.0.1"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-symbols@^1.0.1", "has-symbols@^1.0.2":
  "integrity" "sha512-chXa79rL/UC2KlX17jo3vRGz0azaWEx5tGqZg5pO3NUyEJVB17dMruQlzCCOfUvElghKcm5194+BCRvi2Rv/Gw=="
  "resolved" "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.2.tgz"
  "version" "1.0.2"

"has-tostringtag@^1.0.0":
  "integrity" "sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ=="
  "resolved" "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-symbols" "^1.0.2"

"has-value@^0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "https://registry.npmjs.org/has-value/-/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "https://registry.npmjs.org/has-values/-/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.3":
  "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  "resolved" "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hermes-engine@~0.8.1":
  "integrity" "sha512-as9Iccj/qrqqtDmfYUHbOIjt5xsQbUB6pjNIW3i1+RVr+pCAdz5S8/Jry778mz3rJWplYzHWdR1u1xQSYfBRYw=="
  "resolved" "https://registry.npmjs.org/hermes-engine/-/hermes-engine-0.8.1.tgz"
  "version" "0.8.1"

"hermes-parser@0.4.7":
  "integrity" "sha512-jc+zCtXbtwTiXoMAoXOHepxAaGVFIp89wwE9qcdwnMd/uGVEtPoY8FaFSsx0ThPvyKirdR2EsIIDVrpbSXz1Ag=="
  "resolved" "https://registry.npmjs.org/hermes-parser/-/hermes-parser-0.4.7.tgz"
  "version" "0.4.7"

"hermes-profile-transformer@^0.0.6":
  "integrity" "sha512-cnN7bQUm65UWOy6cbGcCcZ3rpwW8Q/j4OP5aWRhEry4Z2t2aR1cjrbp0BS+KiBN0smvP1caBgAuxutvyvJILzQ=="
  "resolved" "https://registry.npmjs.org/hermes-profile-transformer/-/hermes-profile-transformer-0.0.6.tgz"
  "version" "0.0.6"
  dependencies:
    "source-map" "^0.7.3"

"html-encoding-sniffer@^2.0.1":
  "integrity" "sha512-D5JbOMBIR/TVZkubHT+OyT2705QvogUW4IBn6nHd756OwieSF9aDYFj4dv6HHEVGYbHaLETa3WggZYWWMyy3ZQ=="
  "resolved" "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "whatwg-encoding" "^1.0.5"

"html-escaper@^2.0.0":
  "integrity" "sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg=="
  "resolved" "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz"
  "version" "2.0.2"

"http-errors@~1.7.2":
  "integrity" "sha512-ZTTX0MWrsQ2ZAhA1cejAwDLycFsd7I7nVtnkT3Ol0aqodaKW+0CTZDQ1uBv5whptCnc8e8HeRRJxRs0kmm/Qfw=="
  "resolved" "https://registry.npmjs.org/http-errors/-/http-errors-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.4"
    "setprototypeof" "1.1.1"
    "statuses" ">= 1.5.0 < 2"
    "toidentifier" "1.0.0"

"http-proxy-agent@^4.0.1":
  "integrity" "sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg=="
  "resolved" "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "@tootallnate/once" "1"
    "agent-base" "6"
    "debug" "4"

"https-proxy-agent@^5.0.0":
  "integrity" "sha512-EkYm5BcKUGiduxzSt3Eppko+PiNWNEpa4ySk9vTC6wDsQJW9rHSa+UhGNJoRYp7bz6Ht1eaRIa6QaJqO5rCFbA=="
  "resolved" "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "agent-base" "6"
    "debug" "4"

"human-signals@^2.1.0":
  "integrity" "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw=="
  "resolved" "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz"
  "version" "2.1.0"

"iconv-lite@0.4.24":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"ignore@^4.0.6":
  "integrity" "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz"
  "version" "4.0.6"

"ignore@^5.0.5":
  "integrity" "sha512-BMpfD7PpiETpBl/A6S498BaIJ6Y/ABT93ETbby2fP00v4EbvPBXWEoaR1UBPKs3iR53pJY7EtZk5KACI57i1Uw=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-5.1.8.tgz"
  "version" "5.1.8"

"ignore@^5.1.4":
  "integrity" "sha512-BMpfD7PpiETpBl/A6S498BaIJ6Y/ABT93ETbby2fP00v4EbvPBXWEoaR1UBPKs3iR53pJY7EtZk5KACI57i1Uw=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-5.1.8.tgz"
  "version" "5.1.8"

"image-size@^0.6.0":
  "integrity" "sha512-47xSUiQioGaB96nqtp5/q55m0aBQSQdyIloMOc/x+QVTDZLNmXE892IIDrJ0hM1A5vcNUDD5tDffkSP5lCaIIA=="
  "resolved" "https://registry.npmjs.org/image-size/-/image-size-0.6.3.tgz"
  "version" "0.6.3"

"import-fresh@^2.0.0":
  "integrity" "sha1-2BNVwVYS04bGH53dOSLUMEgipUY="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-path" "^2.0.0"
    "resolve-from" "^3.0.0"

"import-fresh@^3.0.0", "import-fresh@^3.2.1":
  "integrity" "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw=="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-local@^3.0.2":
  "integrity" "sha512-vjL3+w0oulAVZ0hBHnxa/Nm5TAurf9YLQJDhqRZyqb+VKGOB6LU8t9H1Nr5CIo16vh9XfJTOoHwU0B71S557gA=="
  "resolved" "https://registry.npmjs.org/import-local/-/import-local-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "pkg-dir" "^4.2.0"
    "resolve-cwd" "^3.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"internal-slot@^1.0.3":
  "integrity" "sha512-O0DB1JC/sPyZl7cIo78n5dR7eUSwwpYPiXRhTzNxZVAMUuB8vlnRFyLxdrVToks6XPLVnFfbzaVd5WLjhgg+vA=="
  "resolved" "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "get-intrinsic" "^1.1.0"
    "has" "^1.0.3"
    "side-channel" "^1.0.4"

"invariant@^2.2.4":
  "integrity" "sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA=="
  "resolved" "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"ip@^1.1.5":
  "integrity" "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="
  "resolved" "https://registry.npmjs.org/ip/-/ip-1.1.5.tgz"
  "version" "1.1.5"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ=="
  "resolved" "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-bigint@^1.0.1":
  "integrity" "sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg=="
  "resolved" "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-bigints" "^1.0.1"

"is-boolean-object@^1.1.0":
  "integrity" "sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA=="
  "resolved" "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="
  "resolved" "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-callable@^1.1.4", "is-callable@^1.2.4":
  "integrity" "sha512-nsuwtxZfMX67Oryl9LCQ+upnC0Z0BgpwntpS89m1H/TLF0zNfzfLMV/9Wa/6MZsj0acpEjAO0KF1xT6ZdLl95w=="
  "resolved" "https://registry.npmjs.org/is-callable/-/is-callable-1.2.4.tgz"
  "version" "1.2.4"

"is-ci@^2.0.0":
  "integrity" "sha512-YfJT7rkpQB0updsdHLGWrvhBJfcfzNNawYDNIyQXJz0IViGf75O8EBPKSdvw2rF+LGCsX4FZ8tcr3b19LcZq4w=="
  "resolved" "https://registry.npmjs.org/is-ci/-/is-ci-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ci-info" "^2.0.0"

"is-ci@^3.0.0":
  "integrity" "sha512-kDXyttuLeslKAHYL/K28F2YkM3x5jvFPEw3yXbRptXydjD9rpLEz+C5K5iutY9ZiUu6AP41JdvRQwF4Iqs4ZCQ=="
  "resolved" "https://registry.npmjs.org/is-ci/-/is-ci-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ci-info" "^3.1.1"

"is-core-module@^2.2.0":
  "integrity" "sha512-wShG8vs60jKfPWpF2KZRaAtvt3a20OAn7+IJ6hLPECpSABLcKtFKTTI4ZtH5QcBruBHlq+WsdHWyz0BCZW7svQ=="
  "resolved" "https://registry.npmjs.org/is-core-module/-/is-core-module-2.6.0.tgz"
  "version" "2.6.0"
  dependencies:
    "has" "^1.0.3"

"is-data-descriptor@^0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ=="
  "resolved" "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-date-object@^1.0.1":
  "integrity" "sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ=="
  "resolved" "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-descriptor@^0.1.0":
  "integrity" "sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg=="
  "resolved" "https://registry.npmjs.org/is-descriptor/-/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0", "is-descriptor@^1.0.2":
  "integrity" "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg=="
  "resolved" "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="
  "resolved" "https://registry.npmjs.org/is-directory/-/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA=="
  "resolved" "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-generator-fn@^2.0.0":
  "integrity" "sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ=="
  "resolved" "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz"
  "version" "2.1.0"

"is-glob@^4.0.0", "is-glob@^4.0.1":
  "integrity" "sha512-5G0tKtBTFImOqDnLB2hG6Bp2qcKEFduo4tZu9MT/H6NQv/ghhy30o55ufafxJ/LdH79LLs2Kfrn85TLKyA7BUg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-extglob" "^2.1.1"

"is-negative-zero@^2.0.1":
  "integrity" "sha512-2z6JzQvZRa9A2Y7xC6dQQm4FSTSTNWjKIYYTt4246eMTJmIo0Q+ZyOsU66X8lxK1AbB92dFeglPLrhwpeRKO6w=="
  "resolved" "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.1.tgz"
  "version" "2.0.1"

"is-number-object@^1.0.4":
  "integrity" "sha512-bEVOqiRcvo3zO1+G2lVMy+gkkEm9Yh7cDMRusKKu5ZJKPUYSJwICTKZrNKHA2EbSP0Tu0+6B/emsYNHZyn6K8g=="
  "resolved" "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-number@^3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og=="
  "resolved" "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-potential-custom-element-name@^1.0.1":
  "integrity" "sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ=="
  "resolved" "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz"
  "version" "1.0.1"

"is-regex@^1.1.4":
  "integrity" "sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg=="
  "resolved" "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-stream@^1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^2.0.0":
  "integrity" "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg=="
  "resolved" "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-string@^1.0.5", "is-string@^1.0.7":
  "integrity" "sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg=="
  "resolved" "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-symbol@^1.0.2", "is-symbol@^1.0.3":
  "integrity" "sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg=="
  "resolved" "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-symbols" "^1.0.2"

"is-typedarray@^1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
  "resolved" "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-windows@^1.0.2":
  "integrity" "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="
  "resolved" "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^1.1.0":
  "integrity" "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="
  "resolved" "https://registry.npmjs.org/is-wsl/-/is-wsl-1.1.0.tgz"
  "version" "1.1.0"

"isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"istanbul-lib-coverage@^3.0.0":
  "integrity" "sha512-UiUIqxMgRDET6eR+o5HbfRYP1l0hqkWOs7vNxC/mggutCMUIhWMm8gAHb8tHlyfD3/l6rlgNA5cKdDzEAf6hEg=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.0.0.tgz"
  "version" "3.0.0"

"istanbul-lib-instrument@^4.0.0", "istanbul-lib-instrument@^4.0.3":
  "integrity" "sha512-BXgQl9kf4WTCPCCpmFGoJkz/+uhvm7h7PFKUYxh7qarQd3ER33vHG//qaE8eN25l07YqZPpHXU9I09l/RD5aGQ=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "@babel/core" "^7.7.5"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-coverage" "^3.0.0"
    "semver" "^6.3.0"

"istanbul-lib-report@^3.0.0":
  "integrity" "sha512-wcdi+uAKzfiGT2abPpKZ0hSU1rGQjUQnLvtY5MpQ7QCTahD3VODhcu4wcfY1YtkGaDD5yuydOLINXsfbus9ROw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "istanbul-lib-coverage" "^3.0.0"
    "make-dir" "^3.0.0"
    "supports-color" "^7.1.0"

"istanbul-lib-source-maps@^4.0.0":
  "integrity" "sha512-c16LpFRkR8vQXyHZ5nLpY35JZtzj1PQY1iZmesUbf1FZHbIupcWfjgOXBY9YHkLEQ6puz1u4Dgj6qmU/DisrZg=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "debug" "^4.1.1"
    "istanbul-lib-coverage" "^3.0.0"
    "source-map" "^0.6.1"

"istanbul-reports@^3.0.2":
  "integrity" "sha512-9tZvz7AiR3PEDNGiV9vIouQ/EAcqMXFmkcA1CDFTwOB98OZVDL0PH9glHotf5Ugp6GCOTypfzGWI/OqjWNCRUw=="
  "resolved" "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "html-escaper" "^2.0.0"
    "istanbul-lib-report" "^3.0.0"

"jest-changed-files@^27.1.1":
  "integrity" "sha512-5TV9+fYlC2A6hu3qtoyGHprBwCAn0AuGA77bZdUgYvVlRMjHXo063VcWTEAyx6XAZ85DYHqp0+aHKbPlfRDRvA=="
  "resolved" "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.1.1.tgz"
  "version" "27.1.1"
  dependencies:
    "@jest/types" "^27.1.1"
    "execa" "^5.0.0"
    "throat" "^6.0.1"

"jest-circus@^27.2.0":
  "integrity" "sha512-WwENhaZwOARB1nmcboYPSv/PwHBUGRpA4MEgszjr9DLCl97MYw0qZprBwLb7rNzvMwfIvNGG7pefQ5rxyBlzIA=="
  "resolved" "https://registry.npmjs.org/jest-circus/-/jest-circus-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/environment" "^27.2.0"
    "@jest/test-result" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "co" "^4.6.0"
    "dedent" "^0.7.0"
    "expect" "^27.2.0"
    "is-generator-fn" "^2.0.0"
    "jest-each" "^27.2.0"
    "jest-matcher-utils" "^27.2.0"
    "jest-message-util" "^27.2.0"
    "jest-runtime" "^27.2.0"
    "jest-snapshot" "^27.2.0"
    "jest-util" "^27.2.0"
    "pretty-format" "^27.2.0"
    "slash" "^3.0.0"
    "stack-utils" "^2.0.3"
    "throat" "^6.0.1"

"jest-cli@^27.2.0":
  "integrity" "sha512-bq1X/B/b1kT9y1zIFMEW3GFRX1HEhFybiqKdbxM+j11XMMYSbU9WezfyWIhrSOmPT+iODLATVjfsCnbQs7cfIA=="
  "resolved" "https://registry.npmjs.org/jest-cli/-/jest-cli-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/core" "^27.2.0"
    "@jest/test-result" "^27.2.0"
    "@jest/types" "^27.1.1"
    "chalk" "^4.0.0"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "import-local" "^3.0.2"
    "jest-config" "^27.2.0"
    "jest-util" "^27.2.0"
    "jest-validate" "^27.2.0"
    "prompts" "^2.0.1"
    "yargs" "^16.0.3"

"jest-config@^27.2.0":
  "integrity" "sha512-Z1romHpxeNwLxQtouQ4xt07bY6HSFGKTo0xJcvOK3u6uJHveA4LB2P+ty9ArBLpTh3AqqPxsyw9l9GMnWBYS9A=="
  "resolved" "https://registry.npmjs.org/jest-config/-/jest-config-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^27.2.0"
    "@jest/types" "^27.1.1"
    "babel-jest" "^27.2.0"
    "chalk" "^4.0.0"
    "deepmerge" "^4.2.2"
    "glob" "^7.1.1"
    "graceful-fs" "^4.2.4"
    "is-ci" "^3.0.0"
    "jest-circus" "^27.2.0"
    "jest-environment-jsdom" "^27.2.0"
    "jest-environment-node" "^27.2.0"
    "jest-get-type" "^27.0.6"
    "jest-jasmine2" "^27.2.0"
    "jest-regex-util" "^27.0.6"
    "jest-resolve" "^27.2.0"
    "jest-runner" "^27.2.0"
    "jest-util" "^27.2.0"
    "jest-validate" "^27.2.0"
    "micromatch" "^4.0.4"
    "pretty-format" "^27.2.0"

"jest-diff@^27.2.0":
  "integrity" "sha512-QSO9WC6btFYWtRJ3Hac0sRrkspf7B01mGrrQEiCW6TobtViJ9RWL0EmOs/WnBsZDsI/Y2IoSHZA2x6offu0sYw=="
  "resolved" "https://registry.npmjs.org/jest-diff/-/jest-diff-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "chalk" "^4.0.0"
    "diff-sequences" "^27.0.6"
    "jest-get-type" "^27.0.6"
    "pretty-format" "^27.2.0"

"jest-docblock@^27.0.6":
  "integrity" "sha512-Fid6dPcjwepTFraz0YxIMCi7dejjJ/KL9FBjPYhBp4Sv1Y9PdhImlKZqYU555BlN4TQKaTc+F2Av1z+anVyGkA=="
  "resolved" "https://registry.npmjs.org/jest-docblock/-/jest-docblock-27.0.6.tgz"
  "version" "27.0.6"
  dependencies:
    "detect-newline" "^3.0.0"

"jest-each@^27.2.0":
  "integrity" "sha512-biDmmUQjg+HZOB7MfY2RHSFL3j418nMoC3TK3pGAj880fQQSxvQe1y2Wy23JJJNUlk6YXiGU0yWy86Le1HBPmA=="
  "resolved" "https://registry.npmjs.org/jest-each/-/jest-each-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/types" "^27.1.1"
    "chalk" "^4.0.0"
    "jest-get-type" "^27.0.6"
    "jest-util" "^27.2.0"
    "pretty-format" "^27.2.0"

"jest-environment-jsdom@^27.2.0":
  "integrity" "sha512-wNQJi6Rd/AkUWqTc4gWhuTIFPo7tlMK0RPZXeM6AqRHZA3D3vwvTa9ktAktyVyWYmUoXdYstOfyYMG3w4jt7eA=="
  "resolved" "https://registry.npmjs.org/jest-environment-jsdom/-/jest-environment-jsdom-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/environment" "^27.2.0"
    "@jest/fake-timers" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/node" "*"
    "jest-mock" "^27.1.1"
    "jest-util" "^27.2.0"
    "jsdom" "^16.6.0"

"jest-environment-node@^27.2.0":
  "integrity" "sha512-WbW+vdM4u88iy6Q3ftUEQOSgMPtSgjm3qixYYK2AKEuqmFO2zmACTw1vFUB0qI/QN88X6hA6ZkVKIdIWWzz+yg=="
  "resolved" "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/environment" "^27.2.0"
    "@jest/fake-timers" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/node" "*"
    "jest-mock" "^27.1.1"
    "jest-util" "^27.2.0"

"jest-get-type@^26.3.0":
  "integrity" "sha512-TpfaviN1R2pQWkIihlfEanwOXK0zcxrKEE4MlU6Tn7keoXdN6/3gK/xl0yEh8DOunn5pOVGKf8hB4R9gVh04ig=="
  "resolved" "https://registry.npmjs.org/jest-get-type/-/jest-get-type-26.3.0.tgz"
  "version" "26.3.0"

"jest-get-type@^27.0.6":
  "integrity" "sha512-XTkK5exIeUbbveehcSR8w0bhH+c0yloW/Wpl+9vZrjzztCPWrxhHwkIFpZzCt71oRBsgxmuUfxEqOYoZI2macg=="
  "resolved" "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.0.6.tgz"
  "version" "27.0.6"

"jest-haste-map@^26.5.2":
  "integrity" "sha512-easWIJXIw71B2RdR8kgqpjQrbMRWQBgiBwXYEhtGUTaX+doCjBheluShdDMeR8IMfJiTqH4+zfhtg29apJf/8w=="
  "resolved" "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    "anymatch" "^3.0.3"
    "fb-watchman" "^2.0.0"
    "graceful-fs" "^4.2.4"
    "jest-regex-util" "^26.0.0"
    "jest-serializer" "^26.6.2"
    "jest-util" "^26.6.2"
    "jest-worker" "^26.6.2"
    "micromatch" "^4.0.2"
    "sane" "^4.0.3"
    "walker" "^1.0.7"
  optionalDependencies:
    "fsevents" "^2.1.2"

"jest-haste-map@^27.2.0":
  "integrity" "sha512-laFet7QkNlWjwZtMGHCucLvF8o9PAh2cgePRck1+uadSM4E4XH9J4gnx4do+a6do8ZV5XHNEAXEkIoNg5XUH2Q=="
  "resolved" "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/types" "^27.1.1"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    "anymatch" "^3.0.3"
    "fb-watchman" "^2.0.0"
    "graceful-fs" "^4.2.4"
    "jest-regex-util" "^27.0.6"
    "jest-serializer" "^27.0.6"
    "jest-util" "^27.2.0"
    "jest-worker" "^27.2.0"
    "micromatch" "^4.0.4"
    "walker" "^1.0.7"
  optionalDependencies:
    "fsevents" "^2.3.2"

"jest-jasmine2@^27.2.0":
  "integrity" "sha512-NcPzZBk6IkDW3Z2V8orGueheGJJYfT5P0zI/vTO/Jp+R9KluUdgFrgwfvZ0A34Kw6HKgiWFILZmh3oQ/eS+UxA=="
  "resolved" "https://registry.npmjs.org/jest-jasmine2/-/jest-jasmine2-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^27.2.0"
    "@jest/source-map" "^27.0.6"
    "@jest/test-result" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "co" "^4.6.0"
    "expect" "^27.2.0"
    "is-generator-fn" "^2.0.0"
    "jest-each" "^27.2.0"
    "jest-matcher-utils" "^27.2.0"
    "jest-message-util" "^27.2.0"
    "jest-runtime" "^27.2.0"
    "jest-snapshot" "^27.2.0"
    "jest-util" "^27.2.0"
    "pretty-format" "^27.2.0"
    "throat" "^6.0.1"

"jest-leak-detector@^27.2.0":
  "integrity" "sha512-e91BIEmbZw5+MHkB4Hnrq7S86coTxUMCkz4n7DLmQYvl9pEKmRx9H/JFH87bBqbIU5B2Ju1soKxRWX6/eGFGpA=="
  "resolved" "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "jest-get-type" "^27.0.6"
    "pretty-format" "^27.2.0"

"jest-matcher-utils@^27.2.0":
  "integrity" "sha512-F+LG3iTwJ0gPjxBX6HCyrARFXq6jjiqhwBQeskkJQgSLeF1j6ui1RTV08SR7O51XTUhtc8zqpDj8iCG4RGmdKw=="
  "resolved" "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "chalk" "^4.0.0"
    "jest-diff" "^27.2.0"
    "jest-get-type" "^27.0.6"
    "pretty-format" "^27.2.0"

"jest-message-util@^27.2.0":
  "integrity" "sha512-y+sfT/94CiP8rKXgwCOzO1mUazIEdEhrLjuiu+RKmCP+8O/TJTSne9dqQRbFIHBtlR2+q7cddJlWGir8UATu5w=="
  "resolved" "https://registry.npmjs.org/jest-message-util/-/jest-message-util-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^27.1.1"
    "@types/stack-utils" "^2.0.0"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "micromatch" "^4.0.4"
    "pretty-format" "^27.2.0"
    "slash" "^3.0.0"
    "stack-utils" "^2.0.3"

"jest-mock@^27.1.1":
  "integrity" "sha512-SClsFKuYBf+6SSi8jtAYOuPw8DDMsTElUWEae3zq7vDhH01ayVSIHUSIa8UgbDOUalCFp6gNsaikN0rbxN4dbw=="
  "resolved" "https://registry.npmjs.org/jest-mock/-/jest-mock-27.1.1.tgz"
  "version" "27.1.1"
  dependencies:
    "@jest/types" "^27.1.1"
    "@types/node" "*"

"jest-pnp-resolver@^1.2.2":
  "integrity" "sha512-olV41bKSMm8BdnuMsewT4jqlZ8+3TCARAXjZGT9jcoSnrfUnRCqnMoF9XEeoWjbzObpqF9dRhHQj0Xb9QdF6/w=="
  "resolved" "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.2.tgz"
  "version" "1.2.2"

"jest-regex-util@^26.0.0":
  "integrity" "sha512-Gv3ZIs/nA48/Zvjrl34bf+oD76JHiGDUxNOVgUjh3j890sblXryjY4rss71fPtD/njchl6PSE2hIhvyWa1eT0A=="
  "resolved" "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-26.0.0.tgz"
  "version" "26.0.0"

"jest-regex-util@^27.0.6":
  "integrity" "sha512-SUhPzBsGa1IKm8hx2F4NfTGGp+r7BXJ4CulsZ1k2kI+mGLG+lxGrs76veN2LF/aUdGosJBzKgXmNCw+BzFqBDQ=="
  "resolved" "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-27.0.6.tgz"
  "version" "27.0.6"

"jest-resolve-dependencies@^27.2.0":
  "integrity" "sha512-EY5jc/Y0oxn+oVEEldTidmmdVoZaknKPyDORA012JUdqPyqPL+lNdRyI3pGti0RCydds6coaw6xt4JQY54dKsg=="
  "resolved" "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/types" "^27.1.1"
    "jest-regex-util" "^27.0.6"
    "jest-snapshot" "^27.2.0"

"jest-resolve@*", "jest-resolve@^27.2.0":
  "integrity" "sha512-v09p9Ib/VtpHM6Cz+i9lEAv1Z/M5NVxsyghRHRMEUOqwPQs3zwTdwp1xS3O/k5LocjKiGS0OTaJoBSpjbM2Jlw=="
  "resolved" "https://registry.npmjs.org/jest-resolve/-/jest-resolve-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/types" "^27.1.1"
    "chalk" "^4.0.0"
    "escalade" "^3.1.1"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^27.2.0"
    "jest-pnp-resolver" "^1.2.2"
    "jest-util" "^27.2.0"
    "jest-validate" "^27.2.0"
    "resolve" "^1.20.0"
    "slash" "^3.0.0"

"jest-runner@^27.2.0":
  "integrity" "sha512-Cl+BHpduIc0cIVTjwoyx0pQk4Br8gn+wkr35PmKCmzEdOUnQ2wN7QVXA8vXnMQXSlFkN/+KWnk20TAVBmhgrww=="
  "resolved" "https://registry.npmjs.org/jest-runner/-/jest-runner-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/console" "^27.2.0"
    "@jest/environment" "^27.2.0"
    "@jest/test-result" "^27.2.0"
    "@jest/transform" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "emittery" "^0.8.1"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "jest-docblock" "^27.0.6"
    "jest-environment-jsdom" "^27.2.0"
    "jest-environment-node" "^27.2.0"
    "jest-haste-map" "^27.2.0"
    "jest-leak-detector" "^27.2.0"
    "jest-message-util" "^27.2.0"
    "jest-resolve" "^27.2.0"
    "jest-runtime" "^27.2.0"
    "jest-util" "^27.2.0"
    "jest-worker" "^27.2.0"
    "source-map-support" "^0.5.6"
    "throat" "^6.0.1"

"jest-runtime@^27.2.0":
  "integrity" "sha512-6gRE9AVVX49hgBbWQ9PcNDeM4upMUXzTpBs0kmbrjyotyUyIJixLPsYjpeTFwAA07PVLDei1iAm2chmWycdGdQ=="
  "resolved" "https://registry.npmjs.org/jest-runtime/-/jest-runtime-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/console" "^27.2.0"
    "@jest/environment" "^27.2.0"
    "@jest/fake-timers" "^27.2.0"
    "@jest/globals" "^27.2.0"
    "@jest/source-map" "^27.0.6"
    "@jest/test-result" "^27.2.0"
    "@jest/transform" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/yargs" "^16.0.0"
    "chalk" "^4.0.0"
    "cjs-module-lexer" "^1.0.0"
    "collect-v8-coverage" "^1.0.0"
    "execa" "^5.0.0"
    "exit" "^0.1.2"
    "glob" "^7.1.3"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^27.2.0"
    "jest-message-util" "^27.2.0"
    "jest-mock" "^27.1.1"
    "jest-regex-util" "^27.0.6"
    "jest-resolve" "^27.2.0"
    "jest-snapshot" "^27.2.0"
    "jest-util" "^27.2.0"
    "jest-validate" "^27.2.0"
    "slash" "^3.0.0"
    "strip-bom" "^4.0.0"
    "yargs" "^16.0.3"

"jest-serializer@^26.6.2":
  "integrity" "sha512-S5wqyz0DXnNJPd/xfIzZ5Xnp1HrJWBczg8mMfMpN78OJ5eDxXyf+Ygld9wX1DnUWbIbhM1YDY95NjR4CBXkb2g=="
  "resolved" "https://registry.npmjs.org/jest-serializer/-/jest-serializer-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@types/node" "*"
    "graceful-fs" "^4.2.4"

"jest-serializer@^27.0.6":
  "integrity" "sha512-PtGdVK9EGC7dsaziskfqaAPib6wTViY3G8E5wz9tLVPhHyiDNTZn/xjZ4khAw+09QkoOVpn7vF5nPSN6dtBexA=="
  "resolved" "https://registry.npmjs.org/jest-serializer/-/jest-serializer-27.0.6.tgz"
  "version" "27.0.6"
  dependencies:
    "@types/node" "*"
    "graceful-fs" "^4.2.4"

"jest-snapshot@^27.2.0":
  "integrity" "sha512-MukJvy3KEqemCT2FoT3Gum37CQqso/62PKTfIzWmZVTsLsuyxQmJd2PI5KPcBYFqLlA8LgZLHM8ZlazkVt8LsQ=="
  "resolved" "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@babel/core" "^7.7.2"
    "@babel/generator" "^7.7.2"
    "@babel/parser" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/traverse" "^7.7.2"
    "@babel/types" "^7.0.0"
    "@jest/transform" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/babel__traverse" "^7.0.4"
    "@types/prettier" "^2.1.5"
    "babel-preset-current-node-syntax" "^1.0.0"
    "chalk" "^4.0.0"
    "expect" "^27.2.0"
    "graceful-fs" "^4.2.4"
    "jest-diff" "^27.2.0"
    "jest-get-type" "^27.0.6"
    "jest-haste-map" "^27.2.0"
    "jest-matcher-utils" "^27.2.0"
    "jest-message-util" "^27.2.0"
    "jest-resolve" "^27.2.0"
    "jest-util" "^27.2.0"
    "natural-compare" "^1.4.0"
    "pretty-format" "^27.2.0"
    "semver" "^7.3.2"

"jest-util@^26.6.2":
  "integrity" "sha512-MDW0fKfsn0OI7MS7Euz6h8HNDXVQ0gaM9uW6RjfDmd1DAFcaxX9OqIakHIqhbnmF08Cf2DLDG+ulq8YQQ0Lp0Q=="
  "resolved" "https://registry.npmjs.org/jest-util/-/jest-util-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "is-ci" "^2.0.0"
    "micromatch" "^4.0.2"

"jest-util@^27.2.0":
  "integrity" "sha512-T5ZJCNeFpqcLBpx+Hl9r9KoxBCUqeWlJ1Htli+vryigZVJ1vuLB9j35grEBASp4R13KFkV7jM52bBGnArpJN6A=="
  "resolved" "https://registry.npmjs.org/jest-util/-/jest-util-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/types" "^27.1.1"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "is-ci" "^3.0.0"
    "picomatch" "^2.2.3"

"jest-validate@^26.5.2":
  "integrity" "sha512-NEYZ9Aeyj0i5rQqbq+tpIOom0YS1u2MVu6+euBsvpgIme+FOfRmoC4R5p0JiAUpaFvFy24xgrpMknarR/93XjQ=="
  "resolved" "https://registry.npmjs.org/jest-validate/-/jest-validate-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "camelcase" "^6.0.0"
    "chalk" "^4.0.0"
    "jest-get-type" "^26.3.0"
    "leven" "^3.1.0"
    "pretty-format" "^26.6.2"

"jest-validate@^27.2.0":
  "integrity" "sha512-uIEZGkFKk3+4liA81Xu0maG5aGDyPLdp+4ed244c+Ql0k3aLWQYcMbaMLXOIFcb83LPHzYzqQ8hwNnIxTqfAGQ=="
  "resolved" "https://registry.npmjs.org/jest-validate/-/jest-validate-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/types" "^27.1.1"
    "camelcase" "^6.2.0"
    "chalk" "^4.0.0"
    "jest-get-type" "^27.0.6"
    "leven" "^3.1.0"
    "pretty-format" "^27.2.0"

"jest-watcher@^27.2.0":
  "integrity" "sha512-SjRWhnr+qO8aBsrcnYIyF+qRxNZk6MZH8TIDgvi+VlsyrvOyqg0d+Rm/v9KHiTtC9mGGeFi9BFqgavyWib6xLg=="
  "resolved" "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/test-result" "^27.2.0"
    "@jest/types" "^27.1.1"
    "@types/node" "*"
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.0.0"
    "jest-util" "^27.2.0"
    "string-length" "^4.0.1"

"jest-worker@^26.0.0", "jest-worker@^26.6.2":
  "integrity" "sha512-KWYVV1c4i+jbMpaBC+U++4Va0cp8OisU185o73T1vo99hqi7w8tSJfUXYswwqqrjzwxa6KpRK54WhPvwf5w6PQ=="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^7.0.0"

"jest-worker@^27.2.0":
  "integrity" "sha512-laB0ZVIBz+voh/QQy9dmUuuDsadixeerrKqyVpgPz+CCWiOYjOBabUXHIXZhsdvkWbLqSHbgkAHWl5cg24Q6RA=="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^8.0.0"

"jest@^27.2.0":
  "integrity" "sha512-oUqVXyvh5YwEWl263KWdPUAqEzBFzGHdFLQ05hUnITr1tH+9SscEI9A/GH9eBClA+Nw1ct+KNuuOV6wlnmBPcg=="
  "resolved" "https://registry.npmjs.org/jest/-/jest-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/core" "^27.2.0"
    "import-local" "^3.0.2"
    "jest-cli" "^27.2.0"

"jetifier@^1.6.2":
  "integrity" "sha512-3Zi16h6L5tXDRQJTb221cnRoVG9/9OvreLdLU2/ZjRv/GILL+2Cemt0IKvkowwkDpvouAU1DQPOJ7qaiHeIdrw=="
  "resolved" "https://registry.npmjs.org/jetifier/-/jetifier-1.6.8.tgz"
  "version" "1.6.8"

"joi@^17.2.1":
  "integrity" "sha512-Lm56PP+n0+Z2A2rfRvsfWVDXGEWjXxatPopkQ8qQ5mxCEhwHG+Ettgg5o98FFaxilOxozoa14cFhrE/hOzh/Nw=="
  "resolved" "https://registry.npmjs.org/joi/-/joi-17.4.2.tgz"
  "version" "17.4.2"
  dependencies:
    "@hapi/hoek" "^9.0.0"
    "@hapi/topo" "^5.0.0"
    "@sideway/address" "^4.1.0"
    "@sideway/formula" "^3.0.0"
    "@sideway/pinpoint" "^2.0.0"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "integrity" "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsc-android@^250230.2.1":
  "integrity" "sha512-KmxeBlRjwoqCnBBKGsihFtvsBHyUFlBxJPK4FzeYcIuBfdjv6jFys44JITAgSTbQD+vIdwMEfyZklsuQX0yI1Q=="
  "resolved" "https://registry.npmjs.org/jsc-android/-/jsc-android-250230.2.1.tgz"
  "version" "250230.2.1"

"jscodeshift@^0.11.0":
  "integrity" "sha512-SdRK2C7jjs4k/kT2mwtO07KJN9RnjxtKn03d9JVj6c3j9WwaLcFYsICYDnLAzY0hp+wG2nxl+Cm2jWLiNVYb8g=="
  "resolved" "https://registry.npmjs.org/jscodeshift/-/jscodeshift-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "@babel/core" "^7.1.6"
    "@babel/parser" "^7.1.6"
    "@babel/plugin-proposal-class-properties" "^7.1.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.1.0"
    "@babel/plugin-proposal-optional-chaining" "^7.1.0"
    "@babel/plugin-transform-modules-commonjs" "^7.1.0"
    "@babel/preset-flow" "^7.0.0"
    "@babel/preset-typescript" "^7.1.0"
    "@babel/register" "^7.0.0"
    "babel-core" "^7.0.0-bridge.0"
    "colors" "^1.1.2"
    "flow-parser" "0.*"
    "graceful-fs" "^4.2.4"
    "micromatch" "^3.1.10"
    "neo-async" "^2.5.0"
    "node-dir" "^0.1.17"
    "recast" "^0.20.3"
    "temp" "^0.8.1"
    "write-file-atomic" "^2.3.0"

"jsdom@^16.6.0":
  "integrity" "sha512-u9Smc2G1USStM+s/x1ru5Sxrl6mPYCbByG1U/hUmqaVsm4tbNyS7CicOSRyuGQYZhTu0h84qkZZQ/I+dzizSVw=="
  "resolved" "https://registry.npmjs.org/jsdom/-/jsdom-16.7.0.tgz"
  "version" "16.7.0"
  dependencies:
    "abab" "^2.0.5"
    "acorn" "^8.2.4"
    "acorn-globals" "^6.0.0"
    "cssom" "^0.4.4"
    "cssstyle" "^2.3.0"
    "data-urls" "^2.0.0"
    "decimal.js" "^10.2.1"
    "domexception" "^2.0.1"
    "escodegen" "^2.0.0"
    "form-data" "^3.0.0"
    "html-encoding-sniffer" "^2.0.1"
    "http-proxy-agent" "^4.0.1"
    "https-proxy-agent" "^5.0.0"
    "is-potential-custom-element-name" "^1.0.1"
    "nwsapi" "^2.2.0"
    "parse5" "6.0.1"
    "saxes" "^5.0.1"
    "symbol-tree" "^3.2.4"
    "tough-cookie" "^4.0.0"
    "w3c-hr-time" "^1.0.2"
    "w3c-xmlserializer" "^2.0.0"
    "webidl-conversions" "^6.1.0"
    "whatwg-encoding" "^1.0.5"
    "whatwg-mimetype" "^2.3.0"
    "whatwg-url" "^8.5.0"
    "ws" "^7.4.6"
    "xml-name-validator" "^3.0.0"

"jsesc@^2.5.1":
  "integrity" "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-parse-better-errors@^1.0.1":
  "integrity" "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw=="
  "resolved" "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="
  "resolved" "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json5@^2.1.2":
  "integrity" "sha512-f+8cldu7X/y7RAJurMEJmdoKXGB/X550w2Nr3tTbezL6RwEE/iMcm+tZnXeoZtKuOq6ft8+CqzEkrIgx1fPoQA=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "minimist" "^1.2.5"

"jsonfile@^2.1.0":
  "integrity" "sha1-NzaitCi4e72gzIO1P6PWM6NcKug="
  "resolved" "https://registry.npmjs.org/jsonfile/-/jsonfile-2.4.0.tgz"
  "version" "2.4.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonfile@^4.0.0":
  "integrity" "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss="
  "resolved" "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz"
  "version" "4.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonify@~0.0.0":
  "integrity" "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM="
  "resolved" "https://registry.npmjs.org/jsonify/-/jsonify-0.0.0.tgz"
  "version" "0.0.0"

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  "integrity" "sha512-EIsmt3O3ljsU6sot/J4E1zDRxfBNrhjyf/OKjlydwgEimQuznlM4Wv7U+ueONJMyEn1WRE0K8dhi3dVAXYT24Q=="
  "resolved" "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "array-includes" "^3.1.2"
    "object.assign" "^4.1.2"

"kind-of@^3.0.2", "kind-of@^3.0.3":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.2.0":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"klaw@^1.0.0":
  "integrity" "sha1-QIhDO0azsbolnXh4XY6W9zugJDk="
  "resolved" "https://registry.npmjs.org/klaw/-/klaw-1.3.1.tgz"
  "version" "1.3.1"
  optionalDependencies:
    "graceful-fs" "^4.1.9"

"kleur@^3.0.3":
  "integrity" "sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w=="
  "resolved" "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz"
  "version" "3.0.3"

"leven@^3.1.0":
  "integrity" "sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A=="
  "resolved" "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz"
  "version" "3.1.0"

"levn@^0.4.1":
  "integrity" "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ=="
  "resolved" "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"levn@~0.3.0":
  "integrity" "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4="
  "resolved" "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"

"locate-path@^3.0.0":
  "integrity" "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-locate" "^3.0.0"
    "path-exists" "^3.0.0"

"locate-path@^5.0.0":
  "integrity" "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash.clonedeep@^4.5.0":
  "integrity" "sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8="
  "resolved" "https://registry.npmjs.org/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz"
  "version" "4.5.0"

"lodash.debounce@^4.0.8":
  "integrity" "sha1-gteb/zCmfEAF/9XiUVMArZyk168="
  "resolved" "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.throttle@^4.1.1":
  "integrity" "sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ="
  "resolved" "https://registry.npmjs.org/lodash.throttle/-/lodash.throttle-4.1.1.tgz"
  "version" "4.1.1"

"lodash.truncate@^4.4.2":
  "integrity" "sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM="
  "resolved" "https://registry.npmjs.org/lodash.truncate/-/lodash.truncate-4.4.2.tgz"
  "version" "4.4.2"

"lodash@^4.17.10", "lodash@^4.17.14", "lodash@^4.17.15", "lodash@^4.7.0":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"log-symbols@^2.2.0":
  "integrity" "sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg=="
  "resolved" "https://registry.npmjs.org/log-symbols/-/log-symbols-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chalk" "^2.0.1"

"logkitty@^0.7.1":
  "integrity" "sha512-/3ER20CTTbahrCrpYfPn7Xavv9diBROZpoXGVZDWMw4b/X4uuUwAC0ki85tgsdMRONURyIJbcOvS94QsUBYPbQ=="
  "resolved" "https://registry.npmjs.org/logkitty/-/logkitty-0.7.1.tgz"
  "version" "0.7.1"
  dependencies:
    "ansi-fragments" "^0.2.1"
    "dayjs" "^1.8.15"
    "yargs" "^15.1.0"

"loose-envify@^1.0.0", "loose-envify@^1.1.0", "loose-envify@^1.4.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lru-cache@^6.0.0":
  "integrity" "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"make-dir@^2.0.0":
  "integrity" "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^2.1.0":
  "integrity" "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^3.0.0":
  "integrity" "sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "semver" "^6.0.0"

"makeerror@1.0.x":
  "integrity" "sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw="
  "resolved" "https://registry.npmjs.org/makeerror/-/makeerror-1.0.11.tgz"
  "version" "1.0.11"
  dependencies:
    "tmpl" "1.0.x"

"map-cache@^0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-visit@^1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "https://registry.npmjs.org/map-visit/-/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"merge-stream@^2.0.0":
  "integrity" "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
  "resolved" "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge2@^1.3.0":
  "integrity" "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="
  "resolved" "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"metro-babel-register@0.66.2":
  "integrity" "sha512-3F+vsVubUPJYKfVMeol8/7pd8CC287Rw92QYzJD8LEmI980xcgwMUEVBZ0UIAUwlLgiJG/f4Mwhuji2EeBXrPg=="
  "resolved" "https://registry.npmjs.org/metro-babel-register/-/metro-babel-register-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-syntax-class-properties" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/register" "^7.0.0"
    "escape-string-regexp" "^1.0.5"

"metro-babel-transformer@0.66.2":
  "integrity" "sha512-aJ/7fc/Xkofw8Fqa51OTDhBzBz26mmpIWrXAZcPdQ8MSTt883EWncxeCEjasc79NJ89BRi7sOkkaWZo2sXlKvw=="
  "resolved" "https://registry.npmjs.org/metro-babel-transformer/-/metro-babel-transformer-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "@babel/core" "^7.14.0"
    "hermes-parser" "0.4.7"
    "metro-source-map" "0.66.2"
    "nullthrows" "^1.1.1"

"metro-cache-key@0.66.2":
  "integrity" "sha512-WtkNmRt41qOpHh1MkNA4nLiQ/m7iGL90ysSKD+fcLqlUnOBKJptPQm0ZUv8Kfqk18ddWX2KmsSbq+Sf3I6XohQ=="
  "resolved" "https://registry.npmjs.org/metro-cache-key/-/metro-cache-key-0.66.2.tgz"
  "version" "0.66.2"

"metro-cache@0.66.2":
  "integrity" "sha512-5QCYJtJOHoBSbL3H4/Fpl36oA697C3oYHqsce+Hk/dh2qtODUGpS3gOBhvP1B8iB+H8jJMyR75lZq129LJEsIQ=="
  "resolved" "https://registry.npmjs.org/metro-cache/-/metro-cache-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "metro-core" "0.66.2"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"

"metro-config@^0.66.1", "metro-config@0.66.2":
  "integrity" "sha512-0C+PrKKIBNNzLZUKN/8ZDJS2U5FLMOTXDWbvBHIdqb6YXz8WplXR2+xlSlaSCCi5b+GR7cWFWUNeKA4GQS1/AQ=="
  "resolved" "https://registry.npmjs.org/metro-config/-/metro-config-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "cosmiconfig" "^5.0.5"
    "jest-validate" "^26.5.2"
    "metro" "0.66.2"
    "metro-cache" "0.66.2"
    "metro-core" "0.66.2"
    "metro-runtime" "0.66.2"

"metro-core@^0.66.1", "metro-core@0.66.2":
  "integrity" "sha512-JieLZkef/516yxXYvQxWnf3OWw5rcgWRy76K8JV/wr/i8LGVGulPAXlIi445/QZzXVydzRVASKAEVqyxM5F4mA=="
  "resolved" "https://registry.npmjs.org/metro-core/-/metro-core-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "jest-haste-map" "^26.5.2"
    "lodash.throttle" "^4.1.1"
    "metro-resolver" "0.66.2"

"metro-hermes-compiler@0.66.2":
  "integrity" "sha512-nCVL1g9uR6vrw5+X1wjwZruRyMkndnzGRMqjqoljf+nGEqBTD607CR7elXw4fMWn/EM+1y0Vdq5altUu9LdgCA=="
  "resolved" "https://registry.npmjs.org/metro-hermes-compiler/-/metro-hermes-compiler-0.66.2.tgz"
  "version" "0.66.2"

"metro-inspector-proxy@0.66.2":
  "integrity" "sha512-gnLc9121eznwP0iiA9tCBW8qZjwIsCgwHWMF1g1Qaki9le9tzeJv3dK4/lFNGxyfSaLO7vahQEhsEYsiRnTROg=="
  "resolved" "https://registry.npmjs.org/metro-inspector-proxy/-/metro-inspector-proxy-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "connect" "^3.6.5"
    "debug" "^2.2.0"
    "ws" "^1.1.5"
    "yargs" "^15.3.1"

"metro-minify-uglify@0.66.2":
  "integrity" "sha512-7TUK+L5CmB5x1PVnFbgmjzHW4CUadq9H5jgp0HfFoWT1skXAyEsx0DHkKDXwnot0khnNhBOEfl62ctQOnE110Q=="
  "resolved" "https://registry.npmjs.org/metro-minify-uglify/-/metro-minify-uglify-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "uglify-es" "^3.1.9"

"metro-react-native-babel-preset@^0.66.2", "metro-react-native-babel-preset@0.66.2":
  "integrity" "sha512-H/nLBAz0MgfDloSe1FjyH4EnbokHFdncyERvLPXDACY3ROVRCeUyFNo70ywRGXW2NMbrV4H7KUyU4zkfWhC2HQ=="
  "resolved" "https://registry.npmjs.org/metro-react-native-babel-preset/-/metro-react-native-babel-preset-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-export-default-from" "^7.0.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-syntax-dynamic-import" "^7.0.0"
    "@babel/plugin-syntax-export-default-from" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.2.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-syntax-optional-chaining" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-async-to-generator" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-assign" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-react-jsx-self" "^7.0.0"
    "@babel/plugin-transform-react-jsx-source" "^7.0.0"
    "@babel/plugin-transform-regenerator" "^7.0.0"
    "@babel/plugin-transform-runtime" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-sticky-regex" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    "@babel/plugin-transform-typescript" "^7.5.0"
    "@babel/plugin-transform-unicode-regex" "^7.0.0"
    "@babel/template" "^7.0.0"
    "react-refresh" "^0.4.0"

"metro-react-native-babel-transformer@^0.66.1", "metro-react-native-babel-transformer@0.66.2":
  "integrity" "sha512-z1ab7ihIT0pJrwgi9q2IH+LcW/xUWMQ0hH+Mrk7wbKQB0RnJdXFoxphrfoVHBHMUu+TBPetUcEkKawkK1e7Cng=="
  "resolved" "https://registry.npmjs.org/metro-react-native-babel-transformer/-/metro-react-native-babel-transformer-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "@babel/core" "^7.14.0"
    "babel-preset-fbjs" "^3.4.0"
    "hermes-parser" "0.4.7"
    "metro-babel-transformer" "0.66.2"
    "metro-react-native-babel-preset" "0.66.2"
    "metro-source-map" "0.66.2"
    "nullthrows" "^1.1.1"

"metro-resolver@^0.66.1", "metro-resolver@0.66.2":
  "integrity" "sha512-pXQAJR/xauRf4kWFj2/hN5a77B4jLl0Fom5I3PHp6Arw/KxSBp0cnguXpGLwNQ6zQC0nxKCoYGL9gQpzMnN7Hw=="
  "resolved" "https://registry.npmjs.org/metro-resolver/-/metro-resolver-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "absolute-path" "^0.0.0"

"metro-runtime@^0.66.1", "metro-runtime@0.66.2":
  "integrity" "sha512-vFhKBk2ot9FS4b+2v0OTa/guCF/QDAOJubY0CNg7PzCS5+w4y3IvZIcPX4SSS1t8pYEZBLvtdtTDarlDl81xmg=="
  "resolved" "https://registry.npmjs.org/metro-runtime/-/metro-runtime-0.66.2.tgz"
  "version" "0.66.2"

"metro-source-map@0.66.2":
  "integrity" "sha512-038tFmB7vSh73VQcDWIbr5O1m+WXWyYafDaOy+1A/2K308YP0oj33gbEgDnZsLZDwcJ+xt1x6KUEBIzlX4YGeQ=="
  "resolved" "https://registry.npmjs.org/metro-source-map/-/metro-source-map-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "@babel/traverse" "^7.14.0"
    "@babel/types" "^7.0.0"
    "invariant" "^2.2.4"
    "metro-symbolicate" "0.66.2"
    "nullthrows" "^1.1.1"
    "ob1" "0.66.2"
    "source-map" "^0.5.6"
    "vlq" "^1.0.0"

"metro-symbolicate@0.66.2":
  "integrity" "sha512-u+DeQHyAFXVD7mVP+GST/894WHJ3i/U8oEJFnT7U3P52ZuLgX8n4tMNxhqZU12RcLR6etF8143aP0Ktx1gFLEQ=="
  "resolved" "https://registry.npmjs.org/metro-symbolicate/-/metro-symbolicate-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "invariant" "^2.2.4"
    "metro-source-map" "0.66.2"
    "nullthrows" "^1.1.1"
    "source-map" "^0.5.6"
    "through2" "^2.0.1"
    "vlq" "^1.0.0"

"metro-transform-plugins@0.66.2":
  "integrity" "sha512-KTvqplh0ut7oDKovvDG6yzXM02R6X+9b2oVG+qYq8Zd3aCGTi51ASx4ThCNkAHyEvCuJdYg9fxXTL+j+wvhB5w=="
  "resolved" "https://registry.npmjs.org/metro-transform-plugins/-/metro-transform-plugins-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/generator" "^7.14.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.14.0"
    "nullthrows" "^1.1.1"

"metro-transform-worker@0.66.2":
  "integrity" "sha512-dO4PtYOMGB7Vzte8aIzX39xytODhmbJrBYPu+zYzlDjyefJZT7BkZ0LkPIThtyJi96xWcGqi9JBSo0CeRupAHw=="
  "resolved" "https://registry.npmjs.org/metro-transform-worker/-/metro-transform-worker-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/generator" "^7.14.0"
    "@babel/parser" "^7.14.0"
    "@babel/types" "^7.0.0"
    "babel-preset-fbjs" "^3.4.0"
    "metro" "0.66.2"
    "metro-babel-transformer" "0.66.2"
    "metro-cache" "0.66.2"
    "metro-cache-key" "0.66.2"
    "metro-hermes-compiler" "0.66.2"
    "metro-source-map" "0.66.2"
    "metro-transform-plugins" "0.66.2"
    "nullthrows" "^1.1.1"

"metro@^0.66.1", "metro@0.66.2":
  "integrity" "sha512-uNsISfcQ3iKKSHoN5Q+LAh0l3jeeg7ZcNZ/4BAHGsk02erA0OP+l2m+b5qYVoPptHz9Oc3KyG5oGJoTu41pWjg=="
  "resolved" "https://registry.npmjs.org/metro/-/metro-0.66.2.tgz"
  "version" "0.66.2"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/core" "^7.14.0"
    "@babel/generator" "^7.14.0"
    "@babel/parser" "^7.14.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.14.0"
    "@babel/types" "^7.0.0"
    "absolute-path" "^0.0.0"
    "accepts" "^1.3.7"
    "async" "^2.4.0"
    "chalk" "^4.0.0"
    "ci-info" "^2.0.0"
    "connect" "^3.6.5"
    "debug" "^2.2.0"
    "denodeify" "^1.2.1"
    "error-stack-parser" "^2.0.6"
    "fs-extra" "^1.0.0"
    "graceful-fs" "^4.1.3"
    "hermes-parser" "0.4.7"
    "image-size" "^0.6.0"
    "invariant" "^2.2.4"
    "jest-haste-map" "^26.5.2"
    "jest-worker" "^26.0.0"
    "lodash.throttle" "^4.1.1"
    "metro-babel-register" "0.66.2"
    "metro-babel-transformer" "0.66.2"
    "metro-cache" "0.66.2"
    "metro-cache-key" "0.66.2"
    "metro-config" "0.66.2"
    "metro-core" "0.66.2"
    "metro-hermes-compiler" "0.66.2"
    "metro-inspector-proxy" "0.66.2"
    "metro-minify-uglify" "0.66.2"
    "metro-react-native-babel-preset" "0.66.2"
    "metro-resolver" "0.66.2"
    "metro-runtime" "0.66.2"
    "metro-source-map" "0.66.2"
    "metro-symbolicate" "0.66.2"
    "metro-transform-plugins" "0.66.2"
    "metro-transform-worker" "0.66.2"
    "mime-types" "^2.1.27"
    "mkdirp" "^0.5.1"
    "node-fetch" "^2.2.0"
    "nullthrows" "^1.1.1"
    "rimraf" "^2.5.4"
    "serialize-error" "^2.1.0"
    "source-map" "^0.5.6"
    "strip-ansi" "^6.0.0"
    "temp" "0.8.3"
    "throat" "^5.0.0"
    "ws" "^1.1.5"
    "yargs" "^15.3.1"

"micromatch@^3.1.10":
  "integrity" "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"micromatch@^3.1.4":
  "integrity" "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"micromatch@^4.0.2", "micromatch@^4.0.4":
  "integrity" "sha512-pRmzw/XUcwXGpD9aI9q/0XOwLNygjETJ8y0ao0wdqprrzDa4YnxLcz7fQRZr8voh8V10kGhABbNcHVk5wHgWwg=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "braces" "^3.0.1"
    "picomatch" "^2.2.3"

"mime-db@>= 1.43.0 < 2", "mime-db@1.49.0":
  "integrity" "sha512-CIc8j9URtOVApSFCQIF+VBkX1RwXp/oMMOrqdyXSBXq5RWNEsRfyj1kiRnQgmNXmHxPoFIxOroKA3zcU9P+nAA=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.49.0.tgz"
  "version" "1.49.0"

"mime-types@^2.1.12", "mime-types@^2.1.27", "mime-types@~2.1.24":
  "integrity" "sha512-hJGaVS4G4c9TSMYh2n6SQAGrC4RnfU+daP8G7cSCmaqNjiOoUY0VHCMS42pxnQmVF1GWwFhbHWn3RIxCqTmZ9A=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.32.tgz"
  "version" "2.1.32"
  dependencies:
    "mime-db" "1.49.0"

"mime@^2.4.1":
  "integrity" "sha512-tqkh47FzKeCPD2PUiPB6pkbMzsCasjxAfC62/Wap5qrUWcb+sFasXUC5I3gYM5iBM8v/Qpn4UK0x+j0iHyFPDg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-2.5.2.tgz"
  "version" "2.5.2"

"mime@1.6.0":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mimic-fn@^1.0.0":
  "integrity" "sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ=="
  "resolved" "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"mimic-fn@^2.1.0":
  "integrity" "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="
  "resolved" "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"minimatch@^3.0.2", "minimatch@^3.0.4":
  "integrity" "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.1.1", "minimist@^1.2.0", "minimist@^1.2.5":
  "integrity" "sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw=="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz"
  "version" "1.2.5"

"mixin-deep@^1.2.0":
  "integrity" "sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA=="
  "resolved" "https://registry.npmjs.org/mixin-deep/-/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@^0.5.1":
  "integrity" "sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ=="
  "resolved" "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.5.tgz"
  "version" "0.5.5"
  dependencies:
    "minimist" "^1.2.5"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.1":
  "integrity" "sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.1.tgz"
  "version" "2.1.1"

"ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"nanomatch@^1.2.9":
  "integrity" "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA=="
  "resolved" "https://registry.npmjs.org/nanomatch/-/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.2":
  "integrity" "sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw=="
  "resolved" "https://registry.npmjs.org/negotiator/-/negotiator-0.6.2.tgz"
  "version" "0.6.2"

"neo-async@^2.5.0":
  "integrity" "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw=="
  "resolved" "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"nice-try@^1.0.4":
  "integrity" "sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ=="
  "resolved" "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"nocache@^2.1.0":
  "integrity" "sha512-0L9FvHG3nfnnmaEQPjT9xhfN4ISk0A8/2j4M37Np4mcDesJjHgEUfgPhdCyZuFI954tjokaIj/A3NdpFNdEh4Q=="
  "resolved" "https://registry.npmjs.org/nocache/-/nocache-2.1.0.tgz"
  "version" "2.1.0"

"node-dir@^0.1.17":
  "integrity" "sha1-X1Zl2TNRM1yqvvjxxVRRbPXx5OU="
  "resolved" "https://registry.npmjs.org/node-dir/-/node-dir-0.1.17.tgz"
  "version" "0.1.17"
  dependencies:
    "minimatch" "^3.0.2"

"node-fetch@^2.2.0", "node-fetch@^2.6.0":
  "integrity" "sha512-aLoxToI6RfZ+0NOjmWAgn9+LEd30YCkJKFSyWacNZdEKTit/ZMcKjGkTRo8uWEsnIb/hfKecNPEbln02PdWbcA=="
  "resolved" "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.2.tgz"
  "version" "2.6.2"

"node-int64@^0.4.0":
  "integrity" "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs="
  "resolved" "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz"
  "version" "0.4.0"

"node-modules-regexp@^1.0.0":
  "integrity" "sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA="
  "resolved" "https://registry.npmjs.org/node-modules-regexp/-/node-modules-regexp-1.0.0.tgz"
  "version" "1.0.0"

"node-releases@^1.1.75":
  "integrity" "sha512-Qe5OUajvqrqDSy6wrWFmMwfJ0jVgwiw4T3KqmbTcZ62qW0gQkheXYhcFM1+lOVcGUoRxcEcfyvFMAnDgaF1VWw=="
  "resolved" "https://registry.npmjs.org/node-releases/-/node-releases-1.1.75.tgz"
  "version" "1.1.75"

"node-stream-zip@^1.9.1":
  "integrity" "sha512-LN4fydt9TqhZhThkZIVQnF9cwjU3qmUH9h78Mx/K7d3VvfRqqwthLwJEUOEL0QPZ0XQmNN7be5Ggit5+4dq3Bw=="
  "resolved" "https://registry.npmjs.org/node-stream-zip/-/node-stream-zip-1.15.0.tgz"
  "version" "1.15.0"

"normalize-path@^2.1.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"npm-run-path@^2.0.0":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npm-run-path@^4.0.1":
  "integrity" "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw=="
  "resolved" "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"nullthrows@^1.1.1":
  "integrity" "sha512-2vPPEi+Z7WqML2jZYddDIfy5Dqb0r2fze2zTxNNknZaFpVHU3mFB3R+DWeJWGVx0ecvttSGlJTI+WG+8Z4cDWw=="
  "resolved" "https://registry.npmjs.org/nullthrows/-/nullthrows-1.1.1.tgz"
  "version" "1.1.1"

"nwsapi@^2.2.0":
  "integrity" "sha512-h2AatdwYH+JHiZpv7pt/gSX1XoRGb7L/qSIeuqA6GwYoF9w1vP1cw42TO0aI2pNyshRK5893hNSl+1//vHK7hQ=="
  "resolved" "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.0.tgz"
  "version" "2.2.0"

"ob1@0.66.2":
  "integrity" "sha512-RFewnL/RjE0qQBOuM+2bbY96zmJPIge/aDtsiDbLSb+MOiK8CReAhBHDgL+zrA3F1hQk00lMWpUwYcep750plA=="
  "resolved" "https://registry.npmjs.org/ob1/-/ob1-0.66.2.tgz"
  "version" "0.66.2"

"object-assign@^4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "https://registry.npmjs.org/object-copy/-/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-inspect@^1.11.0", "object-inspect@^1.9.0":
  "integrity" "sha512-jp7ikS6Sd3GxQfZJPyH3cjcbJF6GZPClgdV+EFygjFLQ5FmW/dRUnTd9PQ9k0JhoNDabWFbpF1yCdSWCC6gexg=="
  "resolved" "https://registry.npmjs.org/object-inspect/-/object-inspect-1.11.0.tgz"
  "version" "1.11.0"

"object-keys@^1.0.12", "object-keys@^1.1.1":
  "integrity" "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="
  "resolved" "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@^1.0.0":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "https://registry.npmjs.org/object-visit/-/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.assign@^4.1.0", "object.assign@^4.1.2":
  "integrity" "sha512-ixT2L5THXsApyiUPYKmW+2EHpXXe5Ii3M+f4e+aJFAHao5amFRW6J0OO6c/LU8Be47utCx2GL89hxGB6XSmKuQ=="
  "resolved" "https://registry.npmjs.org/object.assign/-/object.assign-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "has-symbols" "^1.0.1"
    "object-keys" "^1.1.1"

"object.entries@^1.1.4":
  "integrity" "sha512-h4LWKWE+wKQGhtMjZEBud7uLGhqyLwj8fpHOarZhD2uY3C9cRtk57VQ89ke3moByLXMedqs3XCHzyb4AmA2DjA=="
  "resolved" "https://registry.npmjs.org/object.entries/-/object.entries-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.2"

"object.fromentries@^2.0.4":
  "integrity" "sha512-EsFBshs5RUUpQEY1D4q/m59kMfz4YJvxuNCJcv/jWwOJr34EaVnG11ZrZa0UHB3wnzV1wx8m58T4hQL8IuNXlQ=="
  "resolved" "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.0-next.2"
    "has" "^1.0.3"

"object.pick@^1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"object.values@^1.1.4":
  "integrity" "sha512-TnGo7j4XSnKQoK3MfvkzqKCi0nVe/D9I9IjwTNYdb/fxYHpjrluHVOgw0AF6jrRFGMPHdfuidR09tIDiIvnaSg=="
  "resolved" "https://registry.npmjs.org/object.values/-/object.values-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.2"

"on-finished@~2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA=="
  "resolved" "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^2.0.0":
  "integrity" "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ="
  "resolved" "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"onetime@^5.1.2":
  "integrity" "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg=="
  "resolved" "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"open@^6.2.0":
  "integrity" "sha512-IFenVPgF70fSm1keSd2iDBIDIBZkroLeuffXq+wKTzTJlBpesFWojV9lb8mzOfaAzM1sr7HQHuO0vtV0zYekGg=="
  "resolved" "https://registry.npmjs.org/open/-/open-6.4.0.tgz"
  "version" "6.4.0"
  dependencies:
    "is-wsl" "^1.1.0"

"optionator@^0.8.1":
  "integrity" "sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA=="
  "resolved" "https://registry.npmjs.org/optionator/-/optionator-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "deep-is" "~0.1.3"
    "fast-levenshtein" "~2.0.6"
    "levn" "~0.3.0"
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"
    "word-wrap" "~1.2.3"

"optionator@^0.9.1":
  "integrity" "sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw=="
  "resolved" "https://registry.npmjs.org/optionator/-/optionator-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.3"

"options@>=0.0.5":
  "integrity" "sha1-7CLTEoBrtT5zF3Pnza788cZDEo8="
  "resolved" "https://registry.npmjs.org/options/-/options-0.0.6.tgz"
  "version" "0.0.6"

"ora@^3.4.0":
  "integrity" "sha512-eNwHudNbO1folBP3JsZ19v9azXWtQZjICdr3Q0TDPIaeBQ3mXLrh54wM+er0+hSp+dWKf+Z8KM58CYzEyIYxYg=="
  "resolved" "https://registry.npmjs.org/ora/-/ora-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "chalk" "^2.4.2"
    "cli-cursor" "^2.1.0"
    "cli-spinners" "^2.0.0"
    "log-symbols" "^2.2.0"
    "strip-ansi" "^5.2.0"
    "wcwidth" "^1.0.1"

"os-tmpdir@^1.0.0":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"p-each-series@^2.1.0":
  "integrity" "sha512-ycIL2+1V32th+8scbpTvyHNaHe02z0sjgh91XXjAk+ZeXoPN4Z46DVUnzdso0aX4KckKw0FNNFHdjZ2UsZvxiA=="
  "resolved" "https://registry.npmjs.org/p-each-series/-/p-each-series-2.2.0.tgz"
  "version" "2.2.0"

"p-finally@^1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-limit@^2.0.0", "p-limit@^2.2.0":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^3.0.0":
  "integrity" "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-limit" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-try@^2.0.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-json@^4.0.0":
  "integrity" "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA="
  "resolved" "https://registry.npmjs.org/parse-json/-/parse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"

"parse5@6.0.1":
  "integrity" "sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw=="
  "resolved" "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz"
  "version" "6.0.1"

"parseurl@~1.3.3":
  "integrity" "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="
  "resolved" "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascalcase@^0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "https://registry.npmjs.org/pascalcase/-/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-exists@^3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^2.0.0", "path-key@^2.0.1":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.6":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-type@^4.0.0":
  "integrity" "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw=="
  "resolved" "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  "version" "4.0.0"

"picomatch@^2.0.4", "picomatch@^2.2.3":
  "integrity" "sha512-lY1Q/PiJGC2zOv/z391WOTD+Z02bCgsFfvxoXXf6h7kv9o+WmsmzYqrAwY63sNgOxE4xEdq0WyUnXfKeBrSvYw=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.0.tgz"
  "version" "2.3.0"

"pify@^4.0.1":
  "integrity" "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="
  "resolved" "https://registry.npmjs.org/pify/-/pify-4.0.1.tgz"
  "version" "4.0.1"

"pirates@^4.0.0", "pirates@^4.0.1":
  "integrity" "sha512-WuNqLTbMI3tmfef2TKxlQmAiLHKtFhlsCZnPIpuv2Ow0RDVO8lfy1Opf4NUzlMXLjPl+Men7AuVdX6TA+s+uGA=="
  "resolved" "https://registry.npmjs.org/pirates/-/pirates-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "node-modules-regexp" "^1.0.0"

"pkg-dir@^3.0.0":
  "integrity" "sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw=="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "find-up" "^3.0.0"

"pkg-dir@^4.2.0":
  "integrity" "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ=="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"plist@^3.0.1", "plist@^3.0.2":
  "integrity" "sha512-ksrr8y9+nXOxQB2osVNqrgvX/XQPOXaU4BQMKjYq8PvaY1U18mo+fKgBSwzK+luSyinOuPae956lSVcBwxlAMg=="
  "resolved" "https://registry.npmjs.org/plist/-/plist-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "base64-js" "^1.5.1"
    "xmlbuilder" "^9.0.7"

"posix-character-classes@^0.1.0":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "https://registry.npmjs.org/posix-character-classes/-/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"prelude-ls@^1.2.1":
  "integrity" "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g=="
  "resolved" "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"prelude-ls@~1.1.2":
  "integrity" "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="
  "resolved" "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"prettier-linter-helpers@^1.0.0":
  "integrity" "sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w=="
  "resolved" "https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "fast-diff" "^1.1.2"

"prettier@^2.0.2", "prettier@>= 1.13.0":
  "integrity" "sha512-DsEPLY1dE5HF3BxCRBmD4uYZ+5DCbvatnolqTqcxEgKVZnL2kUfyu7b8pPQ5+hTBkdhU9SLUmK0/pHb07RE4WQ=="
  "resolved" "https://registry.npmjs.org/prettier/-/prettier-2.4.0.tgz"
  "version" "2.4.0"

"pretty-format@^26.5.2":
  "integrity" "sha512-7AeGuCYNGmycyQbCqd/3PWH4eOoX/OiCa0uphp57NVTeAGdJGaAliecxwBDHYQCIvrW7aDBZCYeNTP/WX69mkg=="
  "resolved" "https://registry.npmjs.org/pretty-format/-/pretty-format-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "ansi-regex" "^5.0.0"
    "ansi-styles" "^4.0.0"
    "react-is" "^17.0.1"

"pretty-format@^26.6.2":
  "integrity" "sha512-7AeGuCYNGmycyQbCqd/3PWH4eOoX/OiCa0uphp57NVTeAGdJGaAliecxwBDHYQCIvrW7aDBZCYeNTP/WX69mkg=="
  "resolved" "https://registry.npmjs.org/pretty-format/-/pretty-format-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "ansi-regex" "^5.0.0"
    "ansi-styles" "^4.0.0"
    "react-is" "^17.0.1"

"pretty-format@^27.2.0":
  "integrity" "sha512-KyJdmgBkMscLqo8A7K77omgLx5PWPiXJswtTtFV7XgVZv2+qPk6UivpXXO+5k6ZEbWIbLoKdx1pZ6ldINzbwTA=="
  "resolved" "https://registry.npmjs.org/pretty-format/-/pretty-format-27.2.0.tgz"
  "version" "27.2.0"
  dependencies:
    "@jest/types" "^27.1.1"
    "ansi-regex" "^5.0.0"
    "ansi-styles" "^5.0.0"
    "react-is" "^17.0.1"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"progress@^2.0.0":
  "integrity" "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA=="
  "resolved" "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz"
  "version" "2.0.3"

"promise@^8.0.3":
  "integrity" "sha512-W04AqnILOL/sPRXziNicCjSNRruLAuIHEOVBazepu0545DDNGYHz7ar9ZgZ1fMU8/MA4mVxp5rkBWRi6OXIy3Q=="
  "resolved" "https://registry.npmjs.org/promise/-/promise-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "asap" "~2.0.6"

"prompts@^2.0.1", "prompts@^2.4.0":
  "integrity" "sha512-EQyfIuO2hPDsX1L/blblV+H7I0knhgAd82cVneCwcdND9B8AuCDuRcBH6yIcG4dFzlOUqbazQqwGjx5xmsNLuQ=="
  "resolved" "https://registry.npmjs.org/prompts/-/prompts-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "kleur" "^3.0.3"
    "sisteransi" "^1.0.5"

"prop-types@^15.7.2":
  "integrity" "sha512-8QQikdH7//R2vurIJSutZ1smHYTcLpRWEOlHnzcWHmBYrOGUysKwSsrC89BCiFj3CbrfJ/nXFdJepOVrY1GCHQ=="
  "resolved" "https://registry.npmjs.org/prop-types/-/prop-types-15.7.2.tgz"
  "version" "15.7.2"
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"
    "react-is" "^16.8.1"

"psl@^1.1.33":
  "integrity" "sha512-RIdOzyoavK+hA18OGGWDqUTsCLhtA7IcZ/6NCs4fFJaHBDab+pDDmDIByWFRQJq2Cd7r1OoQxBGKOaztq+hjIQ=="
  "resolved" "https://registry.npmjs.org/psl/-/psl-1.8.0.tgz"
  "version" "1.8.0"

"pump@^3.0.0":
  "integrity" "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww=="
  "resolved" "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"punycode@^2.1.0", "punycode@^2.1.1":
  "integrity" "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz"
  "version" "2.1.1"

"queue-microtask@^1.2.2":
  "integrity" "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="
  "resolved" "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"range-parser@~1.2.1":
  "integrity" "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="
  "resolved" "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"react-devtools-core@^4.6.0":
  "integrity" "sha512-Kg/LhDkdt9J0ned1D1RfeuSdX4cKW83/valJLYswGdsYc0g2msmD0kfYozsaRacjDoZwcKlxGOES1wrkET5O6Q=="
  "resolved" "https://registry.npmjs.org/react-devtools-core/-/react-devtools-core-4.18.0.tgz"
  "version" "4.18.0"
  dependencies:
    "shell-quote" "^1.6.1"
    "ws" "^7"

"react-is@^16.12.0 || ^17.0.0", "react-is@^17.0.1", "react-is@^17.0.2":
  "integrity" "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz"
  "version" "17.0.2"

"react-is@^16.8.1":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-native-codegen@^0.0.7":
  "integrity" "sha512-dwNgR8zJ3ALr480QnAmpTiqvFo+rDtq6V5oCggKhYFlRjzOmVSFn3YD41u8ltvKS5G2nQ8gCs2vReFFnRGLYng=="
  "resolved" "https://registry.npmjs.org/react-native-codegen/-/react-native-codegen-0.0.7.tgz"
  "version" "0.0.7"
  dependencies:
    "flow-parser" "^0.121.0"
    "jscodeshift" "^0.11.0"
    "nullthrows" "^1.1.1"

"react-native-pax-library@^1.0.5":
  "integrity" "sha512-gUA9x0doxs7QYV92LVzppW8uFRXLzyKuzKv69MqOMLICTHiPMqY82I2QY77lXAOwTJFrqO06pUZT0aTxsI6wnA=="
  "resolved" "https://registry.npmjs.org/react-native-pax-library/-/react-native-pax-library-1.0.5.tgz"
  "version" "1.0.5"

"react-native@>=0.65.0-rc.0 || 0.0.0-*", "react-native@0.65.1":
  "integrity" "sha512-0UOVSnlssweQZjuaUtzViCifE/4tXm8oRbxwakopc8GavPu9vLulde145GOw6QVYiOy4iL50f+2XXRdX9NmMeQ=="
  "resolved" "https://registry.npmjs.org/react-native/-/react-native-0.65.1.tgz"
  "version" "0.65.1"
  dependencies:
    "@jest/create-cache-key-function" "^27.0.1"
    "@react-native-community/cli" "^6.0.0"
    "@react-native-community/cli-platform-android" "^6.0.0"
    "@react-native-community/cli-platform-ios" "^6.0.0"
    "@react-native/assets" "1.0.0"
    "@react-native/normalize-color" "1.0.0"
    "@react-native/polyfills" "1.0.0"
    "abort-controller" "^3.0.0"
    "anser" "^1.4.9"
    "base64-js" "^1.1.2"
    "event-target-shim" "^5.0.1"
    "hermes-engine" "~0.8.1"
    "invariant" "^2.2.4"
    "jsc-android" "^250230.2.1"
    "metro-babel-register" "0.66.2"
    "metro-react-native-babel-transformer" "0.66.2"
    "metro-runtime" "0.66.2"
    "metro-source-map" "0.66.2"
    "nullthrows" "^1.1.1"
    "pretty-format" "^26.5.2"
    "promise" "^8.0.3"
    "prop-types" "^15.7.2"
    "react-devtools-core" "^4.6.0"
    "react-refresh" "^0.4.0"
    "regenerator-runtime" "^0.13.2"
    "scheduler" "^0.20.2"
    "stacktrace-parser" "^0.1.3"
    "use-subscription" "^1.0.0"
    "whatwg-fetch" "^3.0.0"
    "ws" "^6.1.4"

"react-refresh@^0.4.0":
  "integrity" "sha512-Hwln1VNuGl/6bVwnd0Xdn1e84gT/8T9aYNL+HAKDArLCS7LWjwr7StE30IEYbIkx0Vi3vs+coQxe+SQDbGbbpA=="
  "resolved" "https://registry.npmjs.org/react-refresh/-/react-refresh-0.4.3.tgz"
  "version" "0.4.3"

"react-shallow-renderer@^16.13.1":
  "integrity" "sha512-rkIMcQi01/+kxiTE9D3fdS959U1g7gs+/rborw++42m1O9FAQiNI/UNRZExVUoAOprn4umcXf+pFRou8i4zuBg=="
  "resolved" "https://registry.npmjs.org/react-shallow-renderer/-/react-shallow-renderer-16.14.1.tgz"
  "version" "16.14.1"
  dependencies:
    "object-assign" "^4.1.1"
    "react-is" "^16.12.0 || ^17.0.0"

"react-test-renderer@17.0.2":
  "integrity" "sha512-yaQ9cB89c17PUb0x6UfWRs7kQCorVdHlutU1boVPEsB8IDZH6n9tHxMacc3y0JoXOJUsZb/t/Mb8FUWMKaM7iQ=="
  "resolved" "https://registry.npmjs.org/react-test-renderer/-/react-test-renderer-17.0.2.tgz"
  "version" "17.0.2"
  dependencies:
    "object-assign" "^4.1.1"
    "react-is" "^17.0.2"
    "react-shallow-renderer" "^16.13.1"
    "scheduler" "^0.20.2"

"react@^16.0.0 || ^17.0.0", "react@^16.8.0 || ^17.0.0", "react@17.0.2":
  "integrity" "sha512-gnhPt75i/dq/z3/6q/0asP78D0u592D5L1pd7M8P+dck6Fu/jJeL6iVVK23fptSUZj8Vjf++7wXA8UNclGQcbA=="
  "resolved" "https://registry.npmjs.org/react/-/react-17.0.2.tgz"
  "version" "17.0.2"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"readable-stream@~2.3.6":
  "integrity" "sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"recast@^0.20.3":
  "integrity" "sha512-E5qICoPoNL4yU0H0NoBDntNB0Q5oMSNh9usFctYniLBluTthi3RsQVBXIJNbApOlvSwW/RGxIuokPcAc59J5fQ=="
  "resolved" "https://registry.npmjs.org/recast/-/recast-0.20.5.tgz"
  "version" "0.20.5"
  dependencies:
    "ast-types" "0.14.2"
    "esprima" "~4.0.0"
    "source-map" "~0.6.1"
    "tslib" "^2.0.1"

"regenerate-unicode-properties@^8.2.0":
  "integrity" "sha512-F9DjY1vKLo/tPePDycuH3dn9H1OTPIkVD9Kz4LODu+F2C75mgjAJ7x/gwy6ZcSNRAAkhNlJSOHRe8k3p+K9WhA=="
  "resolved" "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-8.2.0.tgz"
  "version" "8.2.0"
  dependencies:
    "regenerate" "^1.4.0"

"regenerate@^1.4.0":
  "integrity" "sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A=="
  "resolved" "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.13.2", "regenerator-runtime@^0.13.4":
  "integrity" "sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz"
  "version" "0.13.9"

"regenerator-transform@^0.14.2":
  "integrity" "sha512-eOf6vka5IO151Jfsw2NO9WpGX58W6wWmefK3I1zEGr0lOD0u8rwPaNqQL1aRxUaxLeKO3ArNh3VYg1KbaD+FFw=="
  "resolved" "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.14.5.tgz"
  "version" "0.14.5"
  dependencies:
    "@babel/runtime" "^7.8.4"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A=="
  "resolved" "https://registry.npmjs.org/regex-not/-/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexp.prototype.flags@^1.3.1":
  "integrity" "sha512-JiBdRBq91WlY7uRJ0ds7R+dU02i6LKi8r3BuQhNXn+kmeLN+EfHhfjqMRis1zJxnlu88hq/4dx0P2OP3APRTOA=="
  "resolved" "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"regexpp@^3.1.0":
  "integrity" "sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg=="
  "resolved" "https://registry.npmjs.org/regexpp/-/regexpp-3.2.0.tgz"
  "version" "3.2.0"

"regexpu-core@^4.7.1":
  "integrity" "sha512-ywH2VUraA44DZQuRKzARmw6S66mr48pQVva4LBeRhcOltJ6hExvWly5ZjFLYo67xbIxb6W1q4bAGtgfEl20zfQ=="
  "resolved" "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.7.1.tgz"
  "version" "4.7.1"
  dependencies:
    "regenerate" "^1.4.0"
    "regenerate-unicode-properties" "^8.2.0"
    "regjsgen" "^0.5.1"
    "regjsparser" "^0.6.4"
    "unicode-match-property-ecmascript" "^1.0.4"
    "unicode-match-property-value-ecmascript" "^1.2.0"

"regjsgen@^0.5.1":
  "integrity" "sha512-OFFT3MfrH90xIW8OOSyUrk6QHD5E9JOTeGodiJeBS3J6IwlgzJMNE/1bZklWz5oTg+9dCMyEetclvCVXOPoN3A=="
  "resolved" "https://registry.npmjs.org/regjsgen/-/regjsgen-0.5.2.tgz"
  "version" "0.5.2"

"regjsparser@^0.6.4":
  "integrity" "sha512-ZqbNRz1SNjLAiYuwY0zoXW8Ne675IX5q+YHioAGbCw4X96Mjl2+dcX9B2ciaeyYjViDAfvIjFpQjJgLttTEERQ=="
  "resolved" "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.9.tgz"
  "version" "0.6.9"
  dependencies:
    "jsesc" "~0.5.0"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="
  "resolved" "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"repeat-element@^1.1.2":
  "integrity" "sha512-LFiNfRcSu7KK3evMyYOuCzv3L10TW7yC1G2/+StMjK8Y6Vqd2MG7r/Qjw4ghtuCOjFvlnms/iMmLqpvW/ES/WQ=="
  "resolved" "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.4.tgz"
  "version" "1.1.4"

"repeat-string@^1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="
  "resolved" "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"require-main-filename@^2.0.0":
  "integrity" "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg=="
  "resolved" "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"resolve-cwd@^3.0.0":
  "integrity" "sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg=="
  "resolved" "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "resolve-from" "^5.0.0"

"resolve-from@^3.0.0":
  "integrity" "sha1-six699nWiBvItuZTM17rywoYh0g="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-from@^5.0.0":
  "integrity" "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
  "version" "5.0.0"

"resolve-url@^0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.12.0", "resolve@^1.14.2", "resolve@^1.20.0":
  "integrity" "sha512-wENBPt4ySzg4ybFQW2TT1zMQucPK95HSh/nq2CFTZVOGut2+pQvSsgtda4d26YrYcr067wjbmzOG8byDPBX63A=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.20.0.tgz"
  "version" "1.20.0"
  dependencies:
    "is-core-module" "^2.2.0"
    "path-parse" "^1.0.6"

"resolve@^2.0.0-next.3":
  "integrity" "sha512-W8LucSynKUIDu9ylraa7ueVZ7hc0uAgJBxVsQSKOXOyle8a93qXhcz+XAXZ8bIq2d6i4Ehddn6Evt+0/UwKk6Q=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.3.tgz"
  "version" "2.0.0-next.3"
  dependencies:
    "is-core-module" "^2.2.0"
    "path-parse" "^1.0.6"

"restore-cursor@^2.0.0":
  "integrity" "sha1-n37ih/gv0ybU/RYpI9YhKe7g368="
  "resolved" "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg=="
  "resolved" "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz"
  "version" "0.1.15"

"reusify@^1.0.4":
  "integrity" "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw=="
  "resolved" "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  "version" "1.0.4"

"rimraf@^2.5.4":
  "integrity" "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w=="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^3.0.0", "rimraf@^3.0.2":
  "integrity" "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA=="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rimraf@~2.2.6":
  "integrity" "sha1-5Dm+Kq7jJzIZUnMPmaiSnk/FBYI="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-2.2.8.tgz"
  "version" "2.2.8"

"rsvp@^4.8.4":
  "integrity" "sha512-nfMOlASu9OnRJo1mbEk2cz0D56a1MBNrJ7orjRZQG10XDyuvwksKbuXNp6qa+kbn839HwjwhBzhFmdsaEAfauA=="
  "resolved" "https://registry.npmjs.org/rsvp/-/rsvp-4.8.5.tgz"
  "version" "4.8.5"

"run-parallel@^1.1.9":
  "integrity" "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  "resolved" "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-regex@^1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "https://registry.npmjs.org/safe-regex/-/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@>= 2.1.2 < 3":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sane@^4.0.3":
  "integrity" "sha512-hhbzAgTIX8O7SHfp2c8/kREfEn4qO/9q8C9beyY6+tvZ87EpoZ3i1RIEvp27YBswnNbY9mWd6paKVmKbAgLfZA=="
  "resolved" "https://registry.npmjs.org/sane/-/sane-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    "anymatch" "^2.0.0"
    "capture-exit" "^2.0.0"
    "exec-sh" "^0.3.2"
    "execa" "^1.0.0"
    "fb-watchman" "^2.0.0"
    "micromatch" "^3.1.4"
    "minimist" "^1.1.1"
    "walker" "~1.0.5"

"sax@^1.2.1":
  "integrity" "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="
  "resolved" "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz"
  "version" "1.2.4"

"saxes@^5.0.1":
  "integrity" "sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw=="
  "resolved" "https://registry.npmjs.org/saxes/-/saxes-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "xmlchars" "^2.2.0"

"scheduler@^0.20.2":
  "integrity" "sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ=="
  "resolved" "https://registry.npmjs.org/scheduler/-/scheduler-0.20.2.tgz"
  "version" "0.20.2"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"semver@^5.5.0":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^5.6.0":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.0.0", "semver@^6.1.1", "semver@^6.1.2", "semver@^6.3.0":
  "integrity" "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^7.2.1":
  "integrity" "sha512-PoeGJYh8HK4BTO/a9Tf6ZG3veo/A7ZVsYrSA6J8ny9nb3B1VrpkuN+z9OE5wfE5p6H4LchYZsegiQgbJD94ZFQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@^7.3.2":
  "integrity" "sha512-PoeGJYh8HK4BTO/a9Tf6ZG3veo/A7ZVsYrSA6J8ny9nb3B1VrpkuN+z9OE5wfE5p6H4LchYZsegiQgbJD94ZFQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@^7.3.5":
  "integrity" "sha512-PoeGJYh8HK4BTO/a9Tf6ZG3veo/A7ZVsYrSA6J8ny9nb3B1VrpkuN+z9OE5wfE5p6H4LchYZsegiQgbJD94ZFQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@7.0.0":
  "integrity" "sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.0.0.tgz"
  "version" "7.0.0"

"send@0.17.1":
  "integrity" "sha512-BsVKsiGcQMFwT8UxypobUKyv7irCNRHk1T0G680vk88yf6LBByGcZJOTJCrTP2xVN6yI+XjPJcNuE3V4fT9sAg=="
  "resolved" "https://registry.npmjs.org/send/-/send-0.17.1.tgz"
  "version" "0.17.1"
  dependencies:
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "destroy" "~1.0.4"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "~1.7.2"
    "mime" "1.6.0"
    "ms" "2.1.1"
    "on-finished" "~2.3.0"
    "range-parser" "~1.2.1"
    "statuses" "~1.5.0"

"serialize-error@^2.1.0":
  "integrity" "sha1-ULZ51WNc34Rme9yOWa9OW4HV9go="
  "resolved" "https://registry.npmjs.org/serialize-error/-/serialize-error-2.1.0.tgz"
  "version" "2.1.0"

"serve-static@^1.13.1":
  "integrity" "sha512-JMrvUwE54emCYWlTI+hGrGv5I8dEwmco/00EvkzIIsR7MqrHonbD9pO2MOfFnpFntl7ecpZs+3mW+XbQZu9QCg=="
  "resolved" "https://registry.npmjs.org/serve-static/-/serve-static-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.17.1"

"set-blocking@^2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw=="
  "resolved" "https://registry.npmjs.org/set-value/-/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setprototypeof@1.1.1":
  "integrity" "sha512-JvdAWfbXeIGaZ9cILp38HntZSFSo3mWg6xGcJJsd+d4aRMOqauag1C63dJfDw7OaMYwEbHMOxEZ1lqVRYP2OAw=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.1.tgz"
  "version" "1.1.1"

"shallow-clone@^3.0.0":
  "integrity" "sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA=="
  "resolved" "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^6.0.2"

"shebang-command@^1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shell-quote@^1.6.1", "shell-quote@1.6.1":
  "integrity" "sha1-9HgZSczkAmlxJ0MOo7PFR29IF2c="
  "resolved" "https://registry.npmjs.org/shell-quote/-/shell-quote-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "array-filter" "~0.0.0"
    "array-map" "~0.0.0"
    "array-reduce" "~0.0.0"
    "jsonify" "~0.0.0"

"side-channel@^1.0.4":
  "integrity" "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw=="
  "resolved" "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.0", "signal-exit@^3.0.2", "signal-exit@^3.0.3":
  "integrity" "sha512-VUJ49FC8U1OxwZLxIbTTrDvLnf/6TDgxZcK8wxR8zs13xpx7xbG60ndBlhNrFi2EMuFRoeDoJO7wthSLq42EjA=="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.3.tgz"
  "version" "3.0.3"

"simple-plist@^1.0.0":
  "integrity" "sha512-pKMCVKvZbZTsqYR6RKgLfBHkh2cV89GXcA/0CVPje3sOiNOnXA8+rp/ciAMZ7JRaUdLzlEM6JFfUn+fS6Nt3hg=="
  "resolved" "https://registry.npmjs.org/simple-plist/-/simple-plist-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "bplist-creator" "0.0.8"
    "bplist-parser" "0.2.0"
    "plist" "^3.0.1"

"sisteransi@^1.0.5":
  "integrity" "sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg=="
  "resolved" "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz"
  "version" "1.0.5"

"slash@^3.0.0":
  "integrity" "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q=="
  "resolved" "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  "version" "3.0.0"

"slice-ansi@^2.0.0":
  "integrity" "sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ=="
  "resolved" "https://registry.npmjs.org/slice-ansi/-/slice-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "astral-regex" "^1.0.0"
    "is-fullwidth-code-point" "^2.0.0"

"slice-ansi@^4.0.0":
  "integrity" "sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ=="
  "resolved" "https://registry.npmjs.org/slice-ansi/-/slice-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"snapdragon-node@^2.0.1":
  "integrity" "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw=="
  "resolved" "https://registry.npmjs.org/snapdragon-node/-/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ=="
  "resolved" "https://registry.npmjs.org/snapdragon-util/-/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg=="
  "resolved" "https://registry.npmjs.org/snapdragon/-/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"source-map-resolve@^0.5.0":
  "integrity" "sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw=="
  "resolved" "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@^0.5.16", "source-map-support@^0.5.6":
  "integrity" "sha512-n1lZZ8Ve4ksRqizaBQgxXDgKwttHDhyfQjA6YZZn8+AroHbsIz+JjwxQDxbp+7y5OYCI8t1Yk7etjD9CRd2hIw=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.20.tgz"
  "version" "0.5.20"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-url@^0.4.0":
  "integrity" "sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw=="
  "resolved" "https://registry.npmjs.org/source-map-url/-/source-map-url-0.4.1.tgz"
  "version" "0.4.1"

"source-map@^0.5.0", "source-map@^0.5.6":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "integrity" "sha512-CkCj6giN3S+n9qrYiBTX5gystlENnRW5jZeNLHpe6aue+SrHcG5VYwujhW9s4dY31mEGsxBDrHR6oI69fTXsaQ=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.7.3.tgz"
  "version" "0.7.3"

"source-map@~0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw=="
  "resolved" "https://registry.npmjs.org/split-string/-/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"stack-utils@^2.0.3":
  "integrity" "sha512-gL//fkxfWUsIlFL2Tl42Cl6+HFALEaB1FU76I/Fy+oZjRreP7OPMXFlGbxM7NQsI0ZpUfw76sHnv0WNYuTb7Iw=="
  "resolved" "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "escape-string-regexp" "^2.0.0"

"stackframe@^1.1.1":
  "integrity" "sha512-GrdeshiRmS1YLMYgzF16olf2jJ/IzxXY9lhKOskuVziubpTYcYqyOwYeJKzQkwy7uN0fYSsbsC4RQaXf9LCrYA=="
  "resolved" "https://registry.npmjs.org/stackframe/-/stackframe-1.2.0.tgz"
  "version" "1.2.0"

"stacktrace-parser@^0.1.3":
  "integrity" "sha512-KJP1OCML99+8fhOHxwwzyWrlUuVX5GQ0ZpJTd1DFXhdkrvg1szxfHhawXUZ3g9TkXORQd4/WG68jMlQZ2p8wlg=="
  "resolved" "https://registry.npmjs.org/stacktrace-parser/-/stacktrace-parser-0.1.10.tgz"
  "version" "0.1.10"
  dependencies:
    "type-fest" "^0.7.1"

"static-extend@^0.1.1":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "https://registry.npmjs.org/static-extend/-/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@>= 1.5.0 < 2", "statuses@~1.5.0":
  "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz"
  "version" "1.5.0"

"stream-buffers@~2.2.0":
  "integrity" "sha1-kdX1Ew0c75bc+n9yaUUYh0HQnuQ="
  "resolved" "https://registry.npmjs.org/stream-buffers/-/stream-buffers-2.2.0.tgz"
  "version" "2.2.0"

"string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-length@^4.0.1":
  "integrity" "sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ=="
  "resolved" "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "char-regex" "^1.0.2"
    "strip-ansi" "^6.0.0"

"string-width@^4.1.0", "string-width@^4.2.0":
  "integrity" "sha512-XBJbT3N4JhVumXE0eoLU9DCjcaF92KLNqTmFCnG1pf8duUxFGwtP6AD6nkjw9a3IdiRtL3E2w3JDiE/xi3vOeA=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.0"

"string.prototype.matchall@^4.0.5":
  "integrity" "sha512-Z5ZaXO0svs0M2xd/6By3qpeKpLKd9mO4v4q3oMEQrk8Ck4xOD5d5XeBOOjGrmVZZ/AHB1S0CgG4N5r1G9N3E2Q=="
  "resolved" "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.5.tgz"
  "version" "4.0.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.18.2"
    "get-intrinsic" "^1.1.1"
    "has-symbols" "^1.0.2"
    "internal-slot" "^1.0.3"
    "regexp.prototype.flags" "^1.3.1"
    "side-channel" "^1.0.4"

"string.prototype.trimend@^1.0.4":
  "integrity" "sha512-y9xCjw1P23Awk8EvTpcyL2NIr1j7wJ39f+k6lvRnSMz+mz9CGz9NYPelDk42kOz6+ql8xjfK8oYzy3jAP5QU5A=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"string.prototype.trimstart@^1.0.4":
  "integrity" "sha512-jh6e984OBfvxS50tdY2nRZnoC5/mLFKOREQfw8t5yytkoUsJRNxvI/E39qu1sD0OtWI3OC0XgKSmcWwziwYuZw=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"strip-ansi@^5.0.0":
  "integrity" "sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^5.2.0":
  "integrity" "sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^6.0.0":
  "integrity" "sha512-AuvKTrTfQNYNIctbR1K/YGTR1756GycPsg7b9bdV9Duqur4gv6aKqHXah67Z8ImS7WEz5QVcOtlfW2rZEugt6w=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "ansi-regex" "^5.0.0"

"strip-bom@^4.0.0":
  "integrity" "sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w=="
  "resolved" "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz"
  "version" "4.0.0"

"strip-eof@^1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "https://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA=="
  "resolved" "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-json-comments@^3.1.0", "strip-json-comments@^3.1.1":
  "integrity" "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="
  "resolved" "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"sudo-prompt@^9.0.0":
  "integrity" "sha512-Mu7R0g4ig9TUuGSxJavny5Rv0egCEtpZRNMrZaYS1vxkiIxGiGUwoezU3LazIQ+KE04hTrTfNPgxU5gzi7F5Pw=="
  "resolved" "https://registry.npmjs.org/sudo-prompt/-/sudo-prompt-9.2.1.tgz"
  "version" "9.2.1"

"supports-color@^5.3.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.0.0", "supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@^8.0.0":
  "integrity" "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  "version" "8.1.1"
  dependencies:
    "has-flag" "^4.0.0"

"supports-hyperlinks@^2.0.0":
  "integrity" "sha512-6sXEzV5+I5j8Bmq9/vUphGRM/RJNT9SCURJLjwfOg51heRtguGWDzcaBlgAzKhQa0EVNpPEKzQuBwZ8S8WaCeQ=="
  "resolved" "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "has-flag" "^4.0.0"
    "supports-color" "^7.0.0"

"symbol-tree@^3.2.4":
  "integrity" "sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw=="
  "resolved" "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz"
  "version" "3.2.4"

"table@^6.0.9":
  "integrity" "sha512-ZGum47Yi6KOOFDE8m223td53ath2enHcYLgOCjGr5ngu8bdIARQk6mN/wRMv4yMRcHnCSnHbCEha4sobQx5yWg=="
  "resolved" "https://registry.npmjs.org/table/-/table-6.7.1.tgz"
  "version" "6.7.1"
  dependencies:
    "ajv" "^8.0.1"
    "lodash.clonedeep" "^4.5.0"
    "lodash.truncate" "^4.4.2"
    "slice-ansi" "^4.0.0"
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"

"temp@^0.8.1", "temp@0.8.3":
  "integrity" "sha1-4Ma8TSa5AxJEEOT+2BEDAU38H1k="
  "resolved" "https://registry.npmjs.org/temp/-/temp-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "os-tmpdir" "^1.0.0"
    "rimraf" "~2.2.6"

"terminal-link@^2.0.0":
  "integrity" "sha512-un0FmiRUQNr5PJqy9kP7c40F5BOfpGlYTrxonDChEZB7pzZxRNp/bt+ymiy9/npwXya9KH99nJ/GXFIiUkYGFQ=="
  "resolved" "https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "supports-hyperlinks" "^2.0.0"

"test-exclude@^6.0.0":
  "integrity" "sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w=="
  "resolved" "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    "glob" "^7.1.4"
    "minimatch" "^3.0.4"

"text-table@^0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"throat@^5.0.0":
  "integrity" "sha512-fcwX4mndzpLQKBS1DVYhGAcYaYt7vsHNIvQV+WXMvnow5cgjPphq5CaayLaGsjRdSCKZFNGt7/GYAuXaNOiYCA=="
  "resolved" "https://registry.npmjs.org/throat/-/throat-5.0.0.tgz"
  "version" "5.0.0"

"throat@^6.0.1":
  "integrity" "sha512-8hmiGIJMDlwjg7dlJ4yKGLK8EsYqKgPWbG3b4wjJddKNwc7N7Dpn08Df4szr/sZdMVeOstrdYSsqzX6BYbcB+w=="
  "resolved" "https://registry.npmjs.org/throat/-/throat-6.0.1.tgz"
  "version" "6.0.1"

"through2@^2.0.1":
  "integrity" "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ=="
  "resolved" "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"tmpl@1.0.x":
  "integrity" "sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw=="
  "resolved" "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz"
  "version" "1.0.5"

"to-fast-properties@^2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "https://registry.npmjs.org/to-object-path/-/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw=="
  "resolved" "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toidentifier@1.0.0":
  "integrity" "sha512-yaOH/Pk/VEhBWWTlhI+qXxDFXlejDGcQipMlyxda9nthulaxLZUNcUqFxokp0vcYnvteJln5FNQDRrxj3YcbVw=="
  "resolved" "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.0.tgz"
  "version" "1.0.0"

"tough-cookie@^4.0.0":
  "integrity" "sha512-tHdtEpQCMrc1YLrMaqXXcj6AxhYi/xgit6mZu1+EDWUn+qhUf8wMQoFIy9NXuq23zAwtcB0t/MjACGR18pcRbg=="
  "resolved" "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "psl" "^1.1.33"
    "punycode" "^2.1.1"
    "universalify" "^0.1.2"

"tr46@^2.1.0":
  "integrity" "sha512-15Ih7phfcdP5YxqiB+iDtLoaTz4Nd35+IiAv0kQ5FNKHzXgdWqPoTIqEDDJmXceQt4JZk6lVPT8lnDlPpGDppw=="
  "resolved" "https://registry.npmjs.org/tr46/-/tr46-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "punycode" "^2.1.1"

"tslib@^1.8.1":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^2.0.1":
  "integrity" "sha512-77EbyPPpMz+FRFRuAFlWMtmgUWGe9UOG2Z25NqCwiIjRhOf5iKGuzSe5P2w1laq+FkRy4p+PCuVkJSGkzTEKVw=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.3.1.tgz"
  "version" "2.3.1"

"tsutils@^3.21.0":
  "integrity" "sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA=="
  "resolved" "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz"
  "version" "3.21.0"
  dependencies:
    "tslib" "^1.8.1"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew=="
  "resolved" "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-check@~0.3.2":
  "integrity" "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I="
  "resolved" "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "~1.1.2"

"type-detect@4.0.8":
  "integrity" "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g=="
  "resolved" "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz"
  "version" "4.0.8"

"type-fest@^0.20.2":
  "integrity" "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
  "version" "0.20.2"

"type-fest@^0.21.3":
  "integrity" "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-fest@^0.7.1":
  "integrity" "sha512-Ne2YiiGN8bmrmJJEuTWTLJR32nh/JdL1+PSicowtNb0WFpn59GK8/lfD61bVtzguz7b3PBt74nxpv/Pw5po5Rg=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.7.1.tgz"
  "version" "0.7.1"

"typedarray-to-buffer@^3.1.5":
  "integrity" "sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q=="
  "resolved" "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz"
  "version" "3.1.5"
  dependencies:
    "is-typedarray" "^1.0.0"

"typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta":
  "integrity" "sha512-4xfscpisVgqqDfPaJo5vkd+Qd/ItkoagnHpufr+i2QCHBsNYp+G7UAoyFl8aPtx879u38wPV65rZ8qbGZijalA=="
  "resolved" "https://registry.npmjs.org/typescript/-/typescript-4.4.3.tgz"
  "version" "4.4.3"

"uglify-es@^3.1.9":
  "integrity" "sha512-r+MU0rfv4L/0eeW3xZrd16t4NZfK8Ld4SWVglYBb7ez5uXFWHuVRs6xCTrf1yirs9a4j4Y27nn7SRfO6v67XsQ=="
  "resolved" "https://registry.npmjs.org/uglify-es/-/uglify-es-3.3.9.tgz"
  "version" "3.3.9"
  dependencies:
    "commander" "~2.13.0"
    "source-map" "~0.6.1"

"ultron@1.0.x":
  "integrity" "sha1-rOEWq1V80Zc4ak6I9GhTeMiy5Po="
  "resolved" "https://registry.npmjs.org/ultron/-/ultron-1.0.2.tgz"
  "version" "1.0.2"

"unbox-primitive@^1.0.1":
  "integrity" "sha512-tZU/3NqK3dA5gpE1KtyiJUrEB0lxnGkMFHptJ7q6ewdZ8s12QrODwNbhIJStmJkd1QDXa1NRA8aF2A1zk/Ypyw=="
  "resolved" "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has-bigints" "^1.0.1"
    "has-symbols" "^1.0.2"
    "which-boxed-primitive" "^1.0.2"

"unicode-canonical-property-names-ecmascript@^1.0.4":
  "integrity" "sha512-jDrNnXWHd4oHiTZnx/ZG7gtUTVp+gCcTTKr8L0HjlwphROEW3+Him+IpvC+xcJEFegapiMZyZe02CyuOnRmbnQ=="
  "resolved" "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.4.tgz"
  "version" "1.0.4"

"unicode-match-property-ecmascript@^1.0.4":
  "integrity" "sha512-L4Qoh15vTfntsn4P1zqnHulG0LdXgjSO035fEpdtp6YxXhMT51Q6vgM5lYdG/5X3MjS+k/Y9Xw4SFCY9IkR0rg=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^1.0.4"
    "unicode-property-aliases-ecmascript" "^1.0.4"

"unicode-match-property-value-ecmascript@^1.2.0":
  "integrity" "sha512-wjuQHGQVofmSJv1uVISKLE5zO2rNGzM/KCYZch/QQvez7C1hUhBIuZ701fYXExuufJFMPhv2SyL8CyoIfMLbIQ=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-1.2.0.tgz"
  "version" "1.2.0"

"unicode-property-aliases-ecmascript@^1.0.4":
  "integrity" "sha512-PqSoPh/pWetQ2phoj5RLiaqIk4kCNwoV3CI+LfGmWLKI3rE3kl1h59XpX2BjgDrmbxD9ARtQobPGU1SguCYuQg=="
  "resolved" "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.1.0.tgz"
  "version" "1.1.0"

"union-value@^1.0.0":
  "integrity" "sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg=="
  "resolved" "https://registry.npmjs.org/union-value/-/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"universalify@^0.1.0", "universalify@^0.1.2":
  "integrity" "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg=="
  "resolved" "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz"
  "version" "0.1.2"

"unpipe@~1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unset-value@^1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "https://registry.npmjs.org/unset-value/-/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "https://registry.npmjs.org/urix/-/urix-0.1.0.tgz"
  "version" "0.1.0"

"use-subscription@^1.0.0":
  "integrity" "sha512-Xv2a1P/yReAjAbhylMfFplFKj9GssgTwN7RlcTxBujFQcloStWNDQdc4g4NRWH9xS4i/FDk04vQBptAXoF3VcA=="
  "resolved" "https://registry.npmjs.org/use-subscription/-/use-subscription-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "object-assign" "^4.1.1"

"use@^3.1.0":
  "integrity" "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ=="
  "resolved" "https://registry.npmjs.org/use/-/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^3.3.2":
  "integrity" "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz"
  "version" "3.4.0"

"v8-compile-cache@^2.0.3":
  "integrity" "sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA=="
  "resolved" "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz"
  "version" "2.3.0"

"v8-to-istanbul@^8.0.0":
  "integrity" "sha512-LkmXi8UUNxnCC+JlH7/fsfsKr5AU110l+SYGJimWNkWhxbN5EyeOtm1MJ0hhvqMMOhGwBj1Fp70Yv9i+hX0QAg=="
  "resolved" "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-8.0.0.tgz"
  "version" "8.0.0"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    "convert-source-map" "^1.6.0"
    "source-map" "^0.7.3"

"vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"vlq@^1.0.0":
  "integrity" "sha512-gQpnTgkubC6hQgdIcRdYGDSDc+SaujOdyesZQMv6JlfQee/9Mp0Qhnys6WxDWvQnL5WZdT7o2Ul187aSt0Rq+w=="
  "resolved" "https://registry.npmjs.org/vlq/-/vlq-1.0.1.tgz"
  "version" "1.0.1"

"w3c-hr-time@^1.0.2":
  "integrity" "sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ=="
  "resolved" "https://registry.npmjs.org/w3c-hr-time/-/w3c-hr-time-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "browser-process-hrtime" "^1.0.0"

"w3c-xmlserializer@^2.0.0":
  "integrity" "sha512-4tzD0mF8iSiMiNs30BiLO3EpfGLZUT2MSX/G+o7ZywDzliWQ3OPtTZ0PTC3B3ca1UAf4cJMHB+2Bf56EriJuRA=="
  "resolved" "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "xml-name-validator" "^3.0.0"

"walker@^1.0.7", "walker@~1.0.5":
  "integrity" "sha1-L3+bj9ENZ3JisYqITijRlhjgKPs="
  "resolved" "https://registry.npmjs.org/walker/-/walker-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "makeerror" "1.0.x"

"wcwidth@^1.0.1":
  "integrity" "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g="
  "resolved" "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webidl-conversions@^5.0.0":
  "integrity" "sha512-VlZwKPCkYKxQgeSbH5EyngOmRp7Ww7I9rQLERETtf5ofd9pGeswWiOtogpEO850jziPRarreGxn5QIiTqpb2wA=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-5.0.0.tgz"
  "version" "5.0.0"

"webidl-conversions@^6.1.0":
  "integrity" "sha512-qBIvFLGiBpLjfwmYAaHPXsn+ho5xZnGvyGvsarywGNc8VyQJUMHJ8OBKGGrPER0okBeMDaan4mNBlgBROxuI8w=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-6.1.0.tgz"
  "version" "6.1.0"

"whatwg-encoding@^1.0.5":
  "integrity" "sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw=="
  "resolved" "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "iconv-lite" "0.4.24"

"whatwg-fetch@^3.0.0":
  "integrity" "sha512-bJlen0FcuU/0EMLrdbJ7zOnW6ITZLrZMIarMUVmdKtsGvZna8vxKYaexICWPfZ8qwf9fzNq+UEIZrnSaApt6RA=="
  "resolved" "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.2.tgz"
  "version" "3.6.2"

"whatwg-mimetype@^2.3.0":
  "integrity" "sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g=="
  "resolved" "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz"
  "version" "2.3.0"

"whatwg-url@^8.0.0", "whatwg-url@^8.5.0":
  "integrity" "sha512-gAojqb/m9Q8a5IV96E3fHJM70AzCkgt4uXYX2O7EmuyOnLrViCQlsEBmF9UQIu3/aeAIp2U17rtbpZWNntQqdg=="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.7.0.tgz"
  "version" "8.7.0"
  dependencies:
    "lodash" "^4.7.0"
    "tr46" "^2.1.0"
    "webidl-conversions" "^6.1.0"

"which-boxed-primitive@^1.0.2":
  "integrity" "sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg=="
  "resolved" "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-bigint" "^1.0.1"
    "is-boolean-object" "^1.1.0"
    "is-number-object" "^1.0.4"
    "is-string" "^1.0.5"
    "is-symbol" "^1.0.3"

"which-module@^2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "https://registry.npmjs.org/which-module/-/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@^1.2.9":
  "integrity" "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ=="
  "resolved" "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"word-wrap@^1.2.3", "word-wrap@~1.2.3":
  "integrity" "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ=="
  "resolved" "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"wrap-ansi@^6.2.0":
  "integrity" "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write-file-atomic@^2.3.0":
  "integrity" "sha512-GaETH5wwsX+GcnzhPgKcKjJ6M2Cq3/iZp1WyY/X1CSqrW+jVNM9Y7D8EC2sM4ZG/V8wZlSniJnCKWPmBYAucRQ=="
  "resolved" "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.4.3.tgz"
  "version" "2.4.3"
  dependencies:
    "graceful-fs" "^4.1.11"
    "imurmurhash" "^0.1.4"
    "signal-exit" "^3.0.2"

"write-file-atomic@^3.0.0":
  "integrity" "sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q=="
  "resolved" "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "imurmurhash" "^0.1.4"
    "is-typedarray" "^1.0.0"
    "signal-exit" "^3.0.2"
    "typedarray-to-buffer" "^3.1.5"

"ws@^1.1.0":
  "integrity" "sha512-o3KqipXNUdS7wpQzBHSe180lBGO60SoK0yVo3CYJgb2MkobuWuBX6dhkYP5ORCLd55y+SaflMOV5fqAB53ux4w=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "options" ">=0.0.5"
    "ultron" "1.0.x"

"ws@^1.1.5":
  "integrity" "sha512-o3KqipXNUdS7wpQzBHSe180lBGO60SoK0yVo3CYJgb2MkobuWuBX6dhkYP5ORCLd55y+SaflMOV5fqAB53ux4w=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "options" ">=0.0.5"
    "ultron" "1.0.x"

"ws@^6.1.4":
  "integrity" "sha512-zmhltoSR8u1cnDsD43TX59mzoMZsLKqUweyYBAIvTngR3shc0W6aOZylZmq/7hqyVxPdi+5Ud2QInblgyE72fw=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-6.2.2.tgz"
  "version" "6.2.2"
  dependencies:
    "async-limiter" "~1.0.0"

"ws@^7", "ws@^7.4.6":
  "integrity" "sha512-BAkMFcAzl8as1G/hArkxOxq3G7pjUqQ3gzYbLL0/5zNkph70e+lCoxBGnm6AW1+/aiNeV4fnKqZ8m4GZewmH2w=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-7.5.5.tgz"
  "version" "7.5.5"

"xcode@^2.0.0":
  "integrity" "sha512-uCrmPITrqTEzhn0TtT57fJaNaw8YJs1aCzs+P/QqxsDbvPZSv7XMPPwXrKvHtD6pLjBM/NaVwraWJm8q83Y4iQ=="
  "resolved" "https://registry.npmjs.org/xcode/-/xcode-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "simple-plist" "^1.0.0"
    "uuid" "^3.3.2"

"xml-name-validator@^3.0.0":
  "integrity" "sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw=="
  "resolved" "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-3.0.0.tgz"
  "version" "3.0.0"

"xmlbuilder@^9.0.7":
  "integrity" "sha1-Ey7mPS7FVlxVfiD0wi35rKaGsQ0="
  "resolved" "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-9.0.7.tgz"
  "version" "9.0.7"

"xmlchars@^2.2.0":
  "integrity" "sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw=="
  "resolved" "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz"
  "version" "2.2.0"

"xmldoc@^1.1.2":
  "integrity" "sha512-ruPC/fyPNck2BD1dpz0AZZyrEwMOrWTO5lDdIXS91rs3wtm4j+T8Rp2o+zoOYkkAxJTZRPOSnOGei1egoRmKMQ=="
  "resolved" "https://registry.npmjs.org/xmldoc/-/xmldoc-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "sax" "^1.2.1"

"xtend@~4.0.1":
  "integrity" "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="
  "resolved" "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@^4.0.0":
  "integrity" "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ=="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-4.0.3.tgz"
  "version" "4.0.3"

"y18n@^5.0.5":
  "integrity" "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^4.0.0":
  "integrity" "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yargs-parser@^18.1.2":
  "integrity" "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz"
  "version" "18.1.3"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^20.2.2":
  "integrity" "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs@^15.1.0":
  "integrity" "sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz"
  "version" "15.4.1"
  dependencies:
    "cliui" "^6.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^4.1.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^4.2.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^18.1.2"

"yargs@^15.3.1":
  "integrity" "sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz"
  "version" "15.4.1"
  dependencies:
    "cliui" "^6.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^4.1.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^4.2.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^18.1.2"

"yargs@^16.0.3":
  "integrity" "sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz"
  "version" "16.2.0"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"
