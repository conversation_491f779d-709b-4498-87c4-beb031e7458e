import { useState } from 'react';
import { useAppStore } from '../store/useAppStore';

type WorkflowStep = 
  | 'customer-arrives'
  | 'cashier-opens-pos'
  | 'enter-amount'
  | 'customer-card'
  | 'terminal-reads'
  | 'stripe-processes'
  | 'payment-result'
  | 'generate-receipt'
  | 'print-receipt'
  | 'customer-receipt'
  | 'transaction-complete'
  | 'show-error'
  | 'retry-cancel';

interface WorkflowState {
  currentStep: WorkflowStep;
  amount: number;
  paymentStatus: 'pending' | 'success' | 'failed' | null;
  transactionId: string | null;
  receipt: string | null;
  errorMessage: string | null;
}

export function POSWorkflow() {
  const [workflow, setWorkflow] = useState<WorkflowState>({
    currentStep: 'customer-arrives',
    amount: 0,
    paymentStatus: null,
    transactionId: null,
    receipt: null,
    errorMessage: null
  });

  const { simulatePayment, isLoading, currentTransaction } = useAppStore();

  const stepConfig = {
    'customer-arrives': {
      title: 'Customer arrives',
      description: 'Welcome! Ready to process payment',
      icon: '👋',
      color: 'bg-blue-100 text-blue-800',
      action: 'Next: Open POS'
    },
    'cashier-opens-pos': {
      title: 'Cashier opens POS',
      description: 'POS terminal is ready for transaction',
      icon: '💻',
      color: 'bg-gray-800 text-white',
      action: 'Next: Enter Amount'
    },
    'enter-amount': {
      title: 'Cashier enters amount',
      description: 'Enter the transaction amount',
      icon: '💰',
      color: 'bg-gray-800 text-white',
      action: 'Enter Amount'
    },
    'customer-card': {
      title: 'Customer inserts/taps card',
      description: 'Customer presents payment card',
      icon: '💳',
      color: 'bg-gray-800 text-white',
      action: 'Next: Read Card'
    },
    'terminal-reads': {
      title: 'Terminal reads card data',
      description: 'Processing card information securely',
      icon: '📱',
      color: 'bg-gray-800 text-white',
      action: 'Next: Process Payment'
    },
    'stripe-processes': {
      title: 'Stripe processes payment',
      description: 'Payment being processed...',
      icon: '⚡',
      color: 'bg-gray-800 text-white',
      action: 'Processing...'
    },
    'payment-result': {
      title: 'Payment successful?',
      description: 'Checking payment result',
      icon: '❓',
      color: 'bg-yellow-100 text-yellow-800',
      action: 'Check Result'
    },
    'generate-receipt': {
      title: 'Generate receipt',
      description: 'Creating transaction receipt',
      icon: '🧾',
      color: 'bg-gray-800 text-white',
      action: 'Generate Receipt'
    },
    'print-receipt': {
      title: 'Print receipt',
      description: 'Printing customer receipt',
      icon: '🖨️',
      color: 'bg-gray-800 text-white',
      action: 'Print Receipt'
    },
    'customer-receipt': {
      title: 'Customer takes receipt',
      description: 'Transaction completed successfully',
      icon: '📄',
      color: 'bg-gray-800 text-white',
      action: 'Complete'
    },
    'transaction-complete': {
      title: 'Transaction complete',
      description: 'Thank you for your business!',
      icon: '✅',
      color: 'bg-green-100 text-green-800',
      action: 'New Transaction'
    },
    'show-error': {
      title: 'Show error message',
      description: 'Payment was declined',
      icon: '❌',
      color: 'bg-red-100 text-red-800',
      action: 'Retry or Cancel'
    },
    'retry-cancel': {
      title: 'Retry or cancel',
      description: 'Choose next action',
      icon: '🔄',
      color: 'bg-gray-800 text-white',
      action: 'Choose Action'
    }
  };

  const nextStep = () => {
    switch (workflow.currentStep) {
      case 'customer-arrives':
        setWorkflow(prev => ({ ...prev, currentStep: 'cashier-opens-pos' }));
        break;
      case 'cashier-opens-pos':
        setWorkflow(prev => ({ ...prev, currentStep: 'enter-amount' }));
        break;
      case 'enter-amount':
        if (workflow.amount > 0) {
          setWorkflow(prev => ({ ...prev, currentStep: 'customer-card' }));
        }
        break;
      case 'customer-card':
        setWorkflow(prev => ({ ...prev, currentStep: 'terminal-reads' }));
        break;
      case 'terminal-reads':
        setWorkflow(prev => ({ ...prev, currentStep: 'stripe-processes' }));
        processPayment();
        break;
      case 'stripe-processes':
        setWorkflow(prev => ({ ...prev, currentStep: 'payment-result' }));
        break;
      case 'payment-result':
        if (workflow.paymentStatus === 'success') {
          setWorkflow(prev => ({ ...prev, currentStep: 'generate-receipt' }));
        } else {
          setWorkflow(prev => ({ ...prev, currentStep: 'show-error' }));
        }
        break;
      case 'generate-receipt':
        generateReceipt();
        setWorkflow(prev => ({ ...prev, currentStep: 'print-receipt' }));
        break;
      case 'print-receipt':
        setWorkflow(prev => ({ ...prev, currentStep: 'customer-receipt' }));
        break;
      case 'customer-receipt':
        setWorkflow(prev => ({ ...prev, currentStep: 'transaction-complete' }));
        break;
      case 'transaction-complete':
        resetWorkflow();
        break;
      case 'show-error':
        setWorkflow(prev => ({ ...prev, currentStep: 'retry-cancel' }));
        break;
      case 'retry-cancel':
        setWorkflow(prev => ({ ...prev, currentStep: 'enter-amount' }));
        break;
    }
  };

  const processPayment = async () => {
    try {
      await simulatePayment(workflow.amount * 100, 'success');
      setWorkflow(prev => ({ 
        ...prev, 
        paymentStatus: 'success',
        transactionId: currentTransaction?._id || null
      }));
    } catch (err) {
      setWorkflow(prev => ({ 
        ...prev, 
        paymentStatus: 'failed',
        errorMessage: 'Payment declined'
      }));
    }
  };

  const generateReceipt = async () => {
    if (workflow.transactionId) {
      try {
        const response = await fetch(`/api/v1/receipts/${workflow.transactionId}`);
        const data = await response.json();
        if (data.success) {
          setWorkflow(prev => ({ ...prev, receipt: data.data.customerReceipt }));
        }
      } catch (err) {
        console.error('Failed to generate receipt:', err);
      }
    }
  };

  const resetWorkflow = () => {
    setWorkflow({
      currentStep: 'customer-arrives',
      amount: 0,
      paymentStatus: null,
      transactionId: null,
      receipt: null,
      errorMessage: null
    });
  };

  const currentConfig = stepConfig[workflow.currentStep];

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-6 text-white">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
              <span className="text-xl">🔄</span>
            </div>
            <div>
              <h2 className="text-2xl font-bold">POS Transaction Workflow</h2>
              <p className="text-indigo-100">Step-by-step payment processing</p>
            </div>
          </div>
        </div>

        <div className="p-8">
          {/* Current Step Display */}
          <div className="text-center mb-8">
            <div className={`inline-flex items-center space-x-3 px-6 py-4 rounded-2xl ${currentConfig.color} text-lg font-semibold`}>
              <span className="text-2xl">{currentConfig.icon}</span>
              <span>{currentConfig.title}</span>
            </div>
            <p className="text-slate-600 mt-3 text-lg">{currentConfig.description}</p>
          </div>

          {/* Step-specific Content */}
          {workflow.currentStep === 'enter-amount' && (
            <div className="max-w-md mx-auto mb-8">
              <label className="block text-sm font-semibold text-slate-700 mb-2">
                Transaction Amount
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 font-medium">$</span>
                <input
                  type="number"
                  value={workflow.amount || ''}
                  onChange={(e) => setWorkflow(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                  className="w-full pl-8 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-lg font-medium text-center"
                  placeholder="0.00"
                  min="0.01"
                  step="0.01"
                />
              </div>
            </div>
          )}

          {workflow.currentStep === 'payment-result' && (
            <div className="text-center mb-8">
              <div className="flex justify-center space-x-8">
                <button
                  onClick={() => setWorkflow(prev => ({ ...prev, paymentStatus: 'success' }))}
                  className="bg-green-600 text-white px-6 py-3 rounded-xl hover:bg-green-700 transition-colors font-semibold"
                >
                  ✅ Success
                </button>
                <button
                  onClick={() => setWorkflow(prev => ({ ...prev, paymentStatus: 'failed' }))}
                  className="bg-red-600 text-white px-6 py-3 rounded-xl hover:bg-red-700 transition-colors font-semibold"
                >
                  ❌ Failed
                </button>
              </div>
            </div>
          )}

          {workflow.currentStep === 'show-error' && workflow.errorMessage && (
            <div className="max-w-md mx-auto mb-8 p-4 bg-red-50 border border-red-200 text-red-700 rounded-xl text-center">
              <div className="flex items-center justify-center space-x-2">
                <span className="text-xl">⚠️</span>
                <span className="font-medium">{workflow.errorMessage}</span>
              </div>
            </div>
          )}

          {workflow.currentStep === 'retry-cancel' && (
            <div className="text-center mb-8">
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => setWorkflow(prev => ({ ...prev, currentStep: 'enter-amount', paymentStatus: null, errorMessage: null }))}
                  className="bg-blue-600 text-white px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors font-semibold"
                >
                  🔄 Retry Payment
                </button>
                <button
                  onClick={resetWorkflow}
                  className="bg-gray-600 text-white px-6 py-3 rounded-xl hover:bg-gray-700 transition-colors font-semibold"
                >
                  ❌ Cancel Transaction
                </button>
              </div>
            </div>
          )}

          {workflow.receipt && workflow.currentStep === 'print-receipt' && (
            <div className="max-w-md mx-auto mb-8 bg-slate-50 rounded-xl p-4">
              <h3 className="font-bold text-slate-800 mb-2 text-center">Receipt Preview</h3>
              <div className="bg-white p-4 rounded-lg border border-slate-200">
                <pre className="font-mono text-xs text-slate-800 whitespace-pre-wrap">
                  {workflow.receipt}
                </pre>
              </div>
            </div>
          )}

          {/* Action Button */}
          <div className="text-center">
            <button
              onClick={nextStep}
              disabled={isLoading || (workflow.currentStep === 'enter-amount' && workflow.amount <= 0)}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 px-8 rounded-xl hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold text-lg shadow-lg hover:shadow-xl"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <span>▶️</span>
                  <span>{currentConfig.action}</span>
                </div>
              )}
            </button>
          </div>

          {/* Progress Indicator */}
          <div className="mt-8 text-center text-sm text-slate-500">
            Step {Object.keys(stepConfig).indexOf(workflow.currentStep) + 1} of {Object.keys(stepConfig).length}
          </div>
        </div>
      </div>
    </div>
  );
}
