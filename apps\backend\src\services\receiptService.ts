import { ITransaction } from '../models/Transaction.mongo';
import { logger } from '../config/logger';

export interface MerchantInfo {
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  phone: string;
  email?: string;
  website?: string;
  taxId?: string;
}

export interface ReceiptData {
  transactionId: string;
  amount: number;
  status: string;
  timestamp: Date;
  paymentMethod?: string;
  cardLast4?: string;
  authCode?: string;
  protocolCode?: string;
  merchantInfo: MerchantInfo;
  customerCopy: boolean;
}

export interface PrinterConfig {
  width: number; // characters per line
  paperType: 'thermal' | 'impact';
  encoding: 'utf8' | 'ascii';
}

export class ReceiptService {
  private static defaultMerchantInfo: MerchantInfo = {
    name: 'Demo Store',
    address: '123 Main Street',
    city: 'Anytown',
    state: 'ST',
    zipCode: '12345',
    phone: '(*************',
    email: '<EMAIL>',
    website: 'www.demostore.com',
    taxId: 'TAX123456789'
  };

  private static printerConfig: PrinterConfig = {
    width: 32,
    paperType: 'thermal',
    encoding: 'utf8'
  };

  static generateReceipt(transaction: ITransaction, customerCopy: boolean = true): string {
    try {
      const receiptData: ReceiptData = {
        transactionId: transaction._id?.toString() || '',
        amount: transaction.amount,
        status: transaction.status,
        timestamp: new Date(transaction.createdAt || new Date()),
        paymentMethod: 'Credit Card',
        cardLast4: '****',
        authCode: this.generateAuthCode(),
        protocolCode: transaction.protocolCode,
        merchantInfo: this.defaultMerchantInfo,
        customerCopy
      };

      return this.formatReceipt(receiptData);
    } catch (error) {
      logger.error('Error generating receipt:', error);
      throw new Error('Failed to generate receipt');
    }
  }

  static generateMerchantReceipt(transaction: ITransaction): string {
    return this.generateReceipt(transaction, false);
  }

  static generateCustomerReceipt(transaction: ITransaction): string {
    return this.generateReceipt(transaction, true);
  }

  private static formatReceipt(data: ReceiptData): string {
    const width = this.printerConfig.width;
    const lines: string[] = [];

    // Header
    lines.push(this.centerText('', width, '='));
    lines.push(this.centerText(data.merchantInfo.name.toUpperCase(), width));
    lines.push(this.centerText(data.merchantInfo.address, width));
    lines.push(this.centerText(`${data.merchantInfo.city}, ${data.merchantInfo.state} ${data.merchantInfo.zipCode}`, width));
    lines.push(this.centerText(data.merchantInfo.phone, width));
    if (data.merchantInfo.website) {
      lines.push(this.centerText(data.merchantInfo.website, width));
    }
    lines.push(this.centerText('', width, '='));
    lines.push('');

    // Transaction details
    lines.push(this.leftRightText('Date:', this.formatDate(data.timestamp), width));
    lines.push(this.leftRightText('Time:', this.formatTime(data.timestamp), width));
    lines.push(this.leftRightText('Trans ID:', data.transactionId.slice(-8), width));
    if (data.authCode) {
      lines.push(this.leftRightText('Auth Code:', data.authCode, width));
    }
    if (data.protocolCode) {
      lines.push(this.leftRightText('Protocol:', data.protocolCode, width));
    }
    lines.push('');

    // Payment details
    lines.push(this.centerText('PAYMENT DETAILS', width, '-'));
    lines.push(this.leftRightText('Payment Method:', data.paymentMethod || 'Credit Card', width));
    if (data.cardLast4) {
      lines.push(this.leftRightText('Card:', `****${data.cardLast4}`, width));
    }
    lines.push('');

    // Amount
    lines.push(this.centerText('', width, '-'));
    lines.push(this.leftRightText('AMOUNT:', `$${(data.amount / 100).toFixed(2)}`, width));
    lines.push(this.centerText('', width, '-'));
    lines.push('');

    // Status
    const statusText = data.status === 'success' ? 'APPROVED' : 'DECLINED';
    lines.push(this.centerText(`*** ${statusText} ***`, width));
    lines.push('');

    // Copy type
    const copyType = data.customerCopy ? 'CUSTOMER COPY' : 'MERCHANT COPY';
    lines.push(this.centerText(copyType, width));
    lines.push('');

    // Footer
    if (data.status === 'success') {
      lines.push(this.centerText('Thank you for your business!', width));
      lines.push(this.centerText('Please keep this receipt', width));
      lines.push(this.centerText('for your records', width));
    } else {
      lines.push(this.centerText('Transaction declined', width));
      lines.push(this.centerText('Please try another payment method', width));
    }
    lines.push('');
    lines.push(this.centerText('', width, '='));
    lines.push('');

    return lines.join('\n');
  }

  private static centerText(text: string, width: number, fillChar: string = ' '): string {
    if (text.length >= width) return text.substring(0, width);
    const padding = width - text.length;
    const leftPad = Math.floor(padding / 2);
    const rightPad = padding - leftPad;
    return fillChar.repeat(leftPad) + text + fillChar.repeat(rightPad);
  }

  private static leftRightText(left: string, right: string, width: number): string {
    const totalTextLength = left.length + right.length;
    if (totalTextLength >= width) {
      return (left + right).substring(0, width);
    }
    const spaces = width - totalTextLength;
    return left + ' '.repeat(spaces) + right;
  }

  private static formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    });
  }

  private static formatTime(date: Date): string {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  }

  private static generateAuthCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }
}

// Legacy function for backward compatibility
export const generateReceipt = (txn: any) => {
  return ReceiptService.generateReceipt(txn);
};
