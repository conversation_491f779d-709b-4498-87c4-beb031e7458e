import Fastify from 'fastify';
import { env } from './config/env';
import { logger } from './config/logger';
import { connectDatabase } from './config/database';

import paymentRoutes from './modules/payment.routes';
import stripeRoutes from './modules/stripe.routes';
import transactionRoutes from './modules/transaction.routes';
import protocolRoutes from './modules/protocol.routes';

const isDevelopment = env.NODE_ENV === 'development';

export async function createApp() {
  const app = Fastify({
    logger: logger,
    trustProxy: true,
    disableRequestLogging: !isDevelopment,
  });

  await app.register(import('@fastify/helmet'), {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  });

  await app.register(import('@fastify/cors'), {
    origin: env.CORS_ORIGIN,
    credentials: true,
  });

  await app.register(import('@fastify/rate-limit'), {
    max: env.RATE_LIMIT_MAX,
    timeWindow: env.RATE_LIMIT_WINDOW,
  });

  app.addHook('onRequest', async (request) => {
    request.log.info({ url: request.url, method: request.method }, 'Incoming request');
  });

  app.addHook('onResponse', async (request, reply) => {
    request.log.info(
      {
        url: request.url,
        method: request.method,
        statusCode: reply.statusCode,
        responseTime: reply.getResponseTime()
      },
      'Request completed'
    );
  });

  app.setErrorHandler(async (error, request, reply) => {
    request.log.error({ error, url: request.url, method: request.method }, 'Request error');

    if (error.validation) {
      return reply.status(400).send({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Request validation failed',
        details: error.validation,
      });
    }

    if (error.statusCode && error.statusCode < 500) {
      return reply.status(error.statusCode).send({
        success: false,
        error: error.code || 'CLIENT_ERROR',
        message: error.message,
      });
    }

    return reply.status(500).send({
      success: false,
      error: 'INTERNAL_SERVER_ERROR',
      message: isDevelopment ? error.message : 'An unexpected error occurred',
    });
  });

  app.setNotFoundHandler(async (request, reply) => {
    return reply.status(404).send({
      success: false,
      error: 'NOT_FOUND',
      message: `Route ${request.method} ${request.url} not found`,
    });
  });

  app.get('/health', async () => {
    return {
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: env.NODE_ENV,
      }
    };
  });

  await app.register(paymentRoutes, { prefix: '/api/v1' });
  await app.register(stripeRoutes, { prefix: '/api/v1' });
  await app.register(transactionRoutes, { prefix: '/api/v1' });
  await app.register(protocolRoutes, { prefix: '/api/v1' });

  // Import and register receipt routes
  const { receiptRoutes } = await import('./modules/receipt.routes');
  await app.register(receiptRoutes, { prefix: '/api/v1' });

  return app;
}

export async function startServer() {
  try {
    await connectDatabase();
    const app = await createApp();

    await app.listen({
      port: env.PORT,
      host: env.NODE_ENV === 'production' ? '0.0.0.0' : 'localhost'
    });

    logger.info(`Server listening on port ${env.PORT} in ${env.NODE_ENV} mode`);
    return app;
  } catch (error) {
    logger.error({ error }, 'Failed to start server');
    process.exit(1);
  }
}
