import Fastify, { FastifyInstance, FastifyServerOptions } from 'fastify';
import sensible from '@fastify/sensible';
import autoload from '@fastify/autoload';
import path from 'path';
import { logger } from './lib/logger';
import { connectDB } from './config/db';
import { loadEnv } from './config/env';

// Load environment variables
loadEnv();

export function build(opts: FastifyServerOptions = {}): FastifyInstance {
  const app = Fastify({ ...opts, logger: logger });

  // Connect to Database
  connectDB().catch(err => {
    app.log.error('Failed to connect to database on startup:', err);
    // Depending on policy, you might want to exit here if DB is critical
    // process.exit(1);
  });

  // Register sensible plugin (adds useful utilities like httpErrors, assert, etc.)
  app.register(sensible);

  // Autoload plugins (e.g., database connectors, authentication)
  app.register(autoload, {
    dir: path.join(__dirname, 'plugins'),
    options: { /* options for plugins */ },
    // ignorePattern: /.*test.(js|ts)/, // Example: ignore test files
    // encapsulate: false, // If you want plugins to share scope, but usually true is better
  });

  // Autoload routes (from modules)
  app.register(autoload, {
    dir: path.join(__dirname, 'modules'),
    options: { prefix: '/api/v1' }, // Prefix all routes with /api/v1
    // ignorePattern: /.*(model|service|validation|types|test).(js|ts)/, // Only load route files
    routeParams: true, // Enable route parameters in file names like `user/:id.ts`
    matchFilter: (filePath) => { // More precise filtering for route files
        return filePath.endsWith('.routes.ts') || filePath.endsWith('.routes.js');
    }
  });


  app.get('/', async (request, reply) => {
    reply.send({ healthcheck: 'OK', message: 'PAX POS Backend is running!' });
  });

  // Graceful shutdown
  const listeners = ['SIGINT', 'SIGTERM'];
  listeners.forEach((signal) => {
    process.on(signal, async () => {
      app.log.info(`Received ${signal}, closing server...`);
      await app.close();
      // Add any other cleanup tasks here, like disconnecting DB if not handled by app.close hooks
      app.log.info('Server closed.');
      process.exit(0);
    });
  });

  process.on('unhandledRejection', (reason, promise) => {
    app.log.error('Unhandled Rejection at:', promise, 'reason:', reason);
    // Application specific logging, throwing an error, or other logic here
  });

  process.on('uncaughtException', (error) => {
    app.log.error('Uncaught Exception thrown:', error);
    // process.exit(1); // It's generally recommended to restart the process on uncaught exceptions
  });


  return app;
}
