import { FastifyRequest, FastifyReply } from 'fastify';
import <PERSON><PERSON> from 'stripe';
import Transaction from '../models/Transaction.mongo';
import { ReceiptService } from '../services/receiptService';
import { logger } from '../config/logger';
import { env } from '../config/env';

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-04-10',
});

interface ProcessPaymentBody {
  amount: number;
  currency: string;
  payment_method?: string;
  payment_method_types?: string[];
  confirm?: boolean;
  return_url?: string;
  customer_email?: string;
  description?: string;
}

interface RefundPaymentBody {
  payment_intent_id: string;
  amount?: number;
  reason?: 'duplicate' | 'fraudulent' | 'requested_by_customer';
}

export const realTimePaymentController = {
  // Process real-time payment
  async processPayment(
    request: FastifyRequest<{ Body: ProcessPaymentBody }>,
    reply: FastifyReply
  ) {
    try {
      const {
        amount,
        currency = 'usd',
        payment_method_types = ['card'],
        confirm = false,
        return_url,
        customer_email,
        description
      } = request.body;

      // Validate amount
      if (!amount || amount < 50) { // Minimum 50 cents
        return reply.status(400).send({
          success: false,
          error: 'Amount must be at least $0.50'
        });
      }

      if (amount > 99999999) { // Maximum $999,999.99
        return reply.status(400).send({
          success: false,
          error: 'Amount exceeds maximum limit'
        });
      }

      // Create payment intent
      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount,
        currency,
        payment_method_types,
        confirm,
        metadata: {
          source: 'pax_a920_pro_pos',
          terminal_id: 'PAX_001',
          timestamp: new Date().toISOString(),
        }
      };

      if (return_url) {
        paymentIntentParams.return_url = return_url;
      }

      if (customer_email) {
        paymentIntentParams.receipt_email = customer_email;
      }

      if (description) {
        paymentIntentParams.description = description;
      }

      const paymentIntent = await stripe.paymentIntents.create(paymentIntentParams);

      // Create transaction record
      const transaction = new Transaction({
        amount,
        currency,
        status: paymentIntent.status === 'succeeded' ? 'success' : 'pending',
        stripePaymentIntentId: paymentIntent.id,
        paymentMethod: 'card',
        metadata: {
          stripe_payment_intent: paymentIntent.id,
          stripe_status: paymentIntent.status,
          customer_email,
          description
        }
      });

      await transaction.save();

      logger.info(`Payment intent created: ${paymentIntent.id} for amount: ${amount}`);

      // If payment is confirmed and succeeded, generate receipt
      let receiptData = null;
      if (paymentIntent.status === 'succeeded') {
        try {
          receiptData = {
            customerReceipt: ReceiptService.generateCustomerReceipt(transaction),
            merchantReceipt: ReceiptService.generateMerchantReceipt(transaction)
          };
        } catch (receiptError) {
          logger.error('Receipt generation failed:', receiptError);
        }
      }

      return reply.send({
        success: true,
        data: {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          client_secret: paymentIntent.client_secret,
          transaction_id: transaction._id,
          payment_method: paymentIntent.payment_method,
          receipt: receiptData
        }
      });

    } catch (error) {
      logger.error('Payment processing error:', error);
      
      if (error instanceof Stripe.errors.StripeError) {
        return reply.status(400).send({
          success: false,
          error: error.message,
          type: error.type,
          code: error.code
        });
      }

      return reply.status(500).send({
        success: false,
        error: 'Payment processing failed'
      });
    }
  },

  // Confirm payment (for card present transactions)
  async confirmPayment(
    request: FastifyRequest<{ Body: { payment_intent_id: string; payment_method?: string } }>,
    reply: FastifyReply
  ) {
    try {
      const { payment_intent_id, payment_method } = request.body;

      const confirmParams: Stripe.PaymentIntentConfirmParams = {};
      
      if (payment_method) {
        confirmParams.payment_method = payment_method;
      }

      const paymentIntent = await stripe.paymentIntents.confirm(
        payment_intent_id,
        confirmParams
      );

      // Update transaction record
      const transaction = await Transaction.findOne({ 
        stripePaymentIntentId: payment_intent_id 
      });

      if (transaction) {
        transaction.status = paymentIntent.status === 'succeeded' ? 'success' : 'failed';
        transaction.metadata = {
          ...transaction.metadata,
          stripe_status: paymentIntent.status,
          confirmed_at: new Date().toISOString()
        };
        await transaction.save();
      }

      logger.info(`Payment confirmed: ${payment_intent_id} - Status: ${paymentIntent.status}`);

      return reply.send({
        success: true,
        data: {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          payment_method: paymentIntent.payment_method
        }
      });

    } catch (error) {
      logger.error('Payment confirmation error:', error);
      
      if (error instanceof Stripe.errors.StripeError) {
        return reply.status(400).send({
          success: false,
          error: error.message,
          type: error.type
        });
      }

      return reply.status(500).send({
        success: false,
        error: 'Payment confirmation failed'
      });
    }
  },

  // Refund payment
  async refundPayment(
    request: FastifyRequest<{ Body: RefundPaymentBody }>,
    reply: FastifyReply
  ) {
    try {
      const { payment_intent_id, amount, reason = 'requested_by_customer' } = request.body;

      const refundParams: Stripe.RefundCreateParams = {
        payment_intent: payment_intent_id,
        reason
      };

      if (amount) {
        refundParams.amount = amount;
      }

      const refund = await stripe.refunds.create(refundParams);

      // Update transaction record
      const transaction = await Transaction.findOne({ 
        stripePaymentIntentId: payment_intent_id 
      });

      if (transaction) {
        transaction.status = 'refunded';
        transaction.metadata = {
          ...transaction.metadata,
          refund_id: refund.id,
          refund_amount: refund.amount,
          refund_reason: reason,
          refunded_at: new Date().toISOString()
        };
        await transaction.save();
      }

      logger.info(`Refund processed: ${refund.id} for payment: ${payment_intent_id}`);

      return reply.send({
        success: true,
        data: {
          refund_id: refund.id,
          amount: refund.amount,
          currency: refund.currency,
          status: refund.status,
          reason: refund.reason
        }
      });

    } catch (error) {
      logger.error('Refund processing error:', error);
      
      if (error instanceof Stripe.errors.StripeError) {
        return reply.status(400).send({
          success: false,
          error: error.message,
          type: error.type
        });
      }

      return reply.status(500).send({
        success: false,
        error: 'Refund processing failed'
      });
    }
  },

  // Get payment status
  async getPaymentStatus(
    request: FastifyRequest<{ Params: { payment_intent_id: string } }>,
    reply: FastifyReply
  ) {
    try {
      const { payment_intent_id } = request.params;

      const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id);

      const transaction = await Transaction.findOne({ 
        stripePaymentIntentId: payment_intent_id 
      });

      return reply.send({
        success: true,
        data: {
          payment_intent: {
            id: paymentIntent.id,
            status: paymentIntent.status,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
            payment_method: paymentIntent.payment_method
          },
          transaction: transaction ? {
            id: transaction._id,
            status: transaction.status,
            created_at: transaction.createdAt
          } : null
        }
      });

    } catch (error) {
      logger.error('Payment status retrieval error:', error);
      
      if (error instanceof Stripe.errors.StripeError) {
        return reply.status(400).send({
          success: false,
          error: error.message,
          type: error.type
        });
      }

      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve payment status'
      });
    }
  },

  // Cancel payment intent
  async cancelPayment(
    request: FastifyRequest<{ Body: { payment_intent_id: string } }>,
    reply: FastifyReply
  ) {
    try {
      const { payment_intent_id } = request.body;

      const paymentIntent = await stripe.paymentIntents.cancel(payment_intent_id);

      // Update transaction record
      const transaction = await Transaction.findOne({ 
        stripePaymentIntentId: payment_intent_id 
      });

      if (transaction) {
        transaction.status = 'cancelled';
        transaction.metadata = {
          ...transaction.metadata,
          cancelled_at: new Date().toISOString()
        };
        await transaction.save();
      }

      logger.info(`Payment cancelled: ${payment_intent_id}`);

      return reply.send({
        success: true,
        data: {
          id: paymentIntent.id,
          status: paymentIntent.status
        }
      });

    } catch (error) {
      logger.error('Payment cancellation error:', error);
      
      if (error instanceof Stripe.errors.StripeError) {
        return reply.status(400).send({
          success: false,
          error: error.message,
          type: error.type
        });
      }

      return reply.status(500).send({
        success: false,
        error: 'Payment cancellation failed'
      });
    }
  }
};
