import { FastifyInstance } from 'fastify';
import { receiptController } from '../controllers/receiptController';

export async function receiptRoutes(fastify: FastifyInstance) {
  // Generate receipt for a transaction
  fastify.get('/receipts/:transactionId', {
    schema: {
      params: {
        type: 'object',
        properties: {
          transactionId: { type: 'string' }
        },
        required: ['transactionId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                transactionId: { type: 'string' },
                customerReceipt: { type: 'string' },
                merchantReceipt: { type: 'string' },
                timestamp: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, receiptController.generateReceipt);

  // Print receipt
  fastify.post('/receipts/print', {
    schema: {
      body: {
        type: 'object',
        properties: {
          transactionId: { type: 'string' },
          copies: { type: 'number', minimum: 1, maximum: 5, default: 1 },
          customerCopy: { type: 'boolean', default: true }
        },
        required: ['transactionId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                transactionId: { type: 'string' },
                printJobs: { type: 'array' },
                message: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, receiptController.printReceipt);

  // Get receipt in different formats
  fastify.get('/receipts/:transactionId/formats', {
    schema: {
      params: {
        type: 'object',
        properties: {
          transactionId: { type: 'string' }
        },
        required: ['transactionId']
      }
    }
  }, receiptController.getReceiptFormats);

  // Email receipt
  fastify.post('/receipts/email', {
    schema: {
      body: {
        type: 'object',
        properties: {
          transactionId: { type: 'string' },
          email: { type: 'string', format: 'email' },
          customerCopy: { type: 'boolean', default: true }
        },
        required: ['transactionId', 'email']
      }
    }
  }, receiptController.emailReceipt);
}
