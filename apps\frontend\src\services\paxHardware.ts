
// PAX Terminal Hardware Integration Service
// This service provides access to PAX terminal hardware features
// Based on PAX Neptune Service, POSitive Integration, and Stripe compatibility
import { PAXNeptuneSDK, PAXHardwareConfig, CardData as PAXCardData } from './paxNeptuneSDK';

export interface PAXTerminalInfo {
  serialNumber: string;
  model: string;
  firmwareVersion: string;
  batteryLevel: number;
  wifiConnected: boolean;
  bluetoothEnabled: boolean;
  nfcEnabled: boolean;
  emvEnabled: boolean;
  ipAddress?: string;
  ptid: string;
}

export interface CardReadResult {
  success: boolean;
  cardData?: {
    pan: string;
    expiryDate: string;
    cardholderName?: string;
    cardType: string;
    track1?: string;
    track2?: string;
    emvData?: any;
  };
  paymentMethod: 'chip_and_pin' | 'chip_and_signature' | 'contactless_chip' | 'contactless_mobile' | 'magnetic_stripe' | 'nfc_tap' | 'mobile_wallet';
  error?: string;
}

export interface PrinterStatus {
  available: boolean;
  paperLevel: 'full' | 'low' | 'empty';
  temperature: 'normal' | 'hot' | 'overheated';
  error?: string;
}

export class PAXHardwareInterface {
  private terminalInfo: PAXTerminalInfo;
  private isConnected: boolean = false;
  private neptuneSDK: PAXNeptuneSDK | null = null;

  constructor() {
    // Initialize with your actual PAX A920 Pro specifications
    this.terminalInfo = {
      serialNumber: '1853944350',
      model: 'PAX A920PC9',
      firmwareVersion: 'PayDroid_10.0.0_Sagittarius_V11-1.29_20240913',
      batteryLevel: 76,
      wifiConnected: true,
      bluetoothEnabled: true,
      nfcEnabled: true,
      emvEnabled: true,
      ipAddress: '**************',
      ptid: '1853944359'
    };

    // Initialize Neptune SDK
    const config: PAXHardwareConfig = {
      terminalId: this.terminalInfo.serialNumber,
      merchantId: 'MERCHANT_001',
      enableDebugMode: true,
      timeout: 30000
    };
    this.neptuneSDK = new PAXNeptuneSDK(config);
  }
  
  // Initialize hardware connection
  public async initialize(): Promise<boolean> {
    try {
      // Initialize Neptune SDK first
      if (this.neptuneSDK) {
        const neptuneInitialized = await this.neptuneSDK.initialize();
        if (neptuneInitialized) {
          console.log('Neptune SDK initialized successfully');
          this.isConnected = true;

          // Update terminal info from Neptune SDK
          const neptuneTerminalInfo = await this.neptuneSDK.getTerminalInfo();
          if (neptuneTerminalInfo) {
            this.terminalInfo = {
              ...this.terminalInfo,
              model: neptuneTerminalInfo.model,
              serialNumber: neptuneTerminalInfo.serialNumber,
              firmwareVersion: neptuneTerminalInfo.firmwareVersion,
              batteryLevel: neptuneTerminalInfo.batteryLevel || this.terminalInfo.batteryLevel
            };
          }
          return true;
        }
      }

      // Fallback to legacy hardware detection
      if (this.isPAXTerminal()) {
        await this.connectToHardware();
        this.isConnected = true;
        return true;
      } else {
        // Simulation mode for development
        console.log('Running in simulation mode - PAX hardware not detected');
        this.isConnected = false;
        return false;
      }
    } catch (error) {
      console.error('Failed to initialize PAX hardware:', error);
      this.isConnected = false;
      return false;
    }
  }
  
  // Check if running on actual PAX terminal
  private isPAXTerminal(): boolean {
    // Check for PAX-specific APIs or user agent
    return (
      typeof (window as any).PAX !== 'undefined' ||
      navigator.userAgent.includes('PAX') ||
      navigator.userAgent.includes('PayDroid') ||
      // Check for Android WebView on PAX
      (navigator.userAgent.includes('Android') && navigator.userAgent.includes('wv'))
    );
  }
  
  // Connect to PAX hardware APIs
  // Based on PAX Neptune SDK documentation and research
  // This service now uses the PAXNeptuneSDK wrapper for better hardware integration
  private async connectToHardware(): Promise<void> {
    // Initialize PAX SDK if available
    if ((window as any).PAX) {
      await (window as any).PAX.initialize();
    }
    
    // Update terminal info from hardware
    await this.updateTerminalInfo();
  }
  
  // Update terminal information from hardware
  private async updateTerminalInfo(): Promise<void> {
    try {
      if ((window as any).PAX && (window as any).PAX.getTerminalInfo) {
        const hwInfo = await (window as any).PAX.getTerminalInfo();
        this.terminalInfo = { ...this.terminalInfo, ...hwInfo };
      }
    } catch (error) {
      console.warn('Could not retrieve hardware terminal info:', error);
    }
  }
  
  // Get terminal information
  public getTerminalInfo(): PAXTerminalInfo {
    return { ...this.terminalInfo };
  }
  
  // Read card data
  public async readCard(timeoutMs: number = 30000): Promise<CardReadResult> {
    if (!this.isConnected) {
      return this.simulateCardRead();
    }

    try {
      // Use Neptune SDK for card reading
      if (this.neptuneSDK && this.neptuneSDK.isHardwareAvailable()) {
        return new Promise((resolve) => {
          this.neptuneSDK!.startCardReading(
            (cardData: PAXCardData) => {
              resolve({
                success: true,
                cardData: {
                  pan: cardData.pan || '',
                  expiryDate: cardData.expiryDate || '',
                  cardholderName: cardData.cardholderName,
                  cardType: this.detectCardType(cardData.pan || ''),
                  track1: cardData.track1,
                  track2: cardData.track2,
                  emvData: cardData.emvData
                },
                paymentMethod: cardData.contactless ? 'contactless_chip' : 'chip_and_pin'
              });
            },
            (error: string) => {
              resolve({
                success: false,
                error,
                paymentMethod: 'chip_and_pin'
              });
            },
            timeoutMs
          );
        });
      }

      // Fallback to legacy PAX API
      if ((window as any).PAX && (window as any).PAX.readCard) {
        const result = await (window as any).PAX.readCard({
          timeout: timeoutMs,
          supportedMethods: ['chip', 'contactless', 'swipe', 'manual']
        });

        return {
          success: true,
          cardData: result.cardData,
          paymentMethod: this.mapPaymentMethod(result.entryMode)
        };
      }

      throw new Error('PAX card reader API not available');
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Card read failed',
        paymentMethod: 'chip_and_pin'
      };
    }
  }
  
  // Simulate card read for development/testing
  private simulateCardRead(): CardReadResult {
    // Simulate different card types and payment methods
    const methods: Array<CardReadResult['paymentMethod']> = [
      'chip_and_pin', 'contactless_chip', 'nfc_tap', 'magnetic_stripe'
    ];
    
    const cardTypes = ['VISA', 'MASTERCARD', 'AMEX', 'DISCOVER'];
    const randomMethod = methods[Math.floor(Math.random() * methods.length)];
    const randomCardType = cardTypes[Math.floor(Math.random() * cardTypes.length)];
    
    return {
      success: true,
      cardData: {
        pan: '****************',
        expiryDate: '12/25',
        cardholderName: 'TEST CARDHOLDER',
        cardType: randomCardType,
        track2: '****************=2512101123456789'
      },
      paymentMethod: randomMethod
    };
  }
  
  // Map PAX entry mode to our payment method
  private mapPaymentMethod(entryMode: string): CardReadResult['paymentMethod'] {
    switch (entryMode?.toLowerCase()) {
      case 'chip':
      case 'icc':
        return 'chip_and_pin';
      case 'contactless':
      case 'ctls':
        return 'contactless_chip';
      case 'nfc':
        return 'nfc_tap';
      case 'swipe':
      case 'mag':
        return 'magnetic_stripe';
      case 'manual':
      case 'keyed':
        return 'chip_and_pin'; // Default to chip for manual entry
      default:
        return 'chip_and_pin';
    }
  }

  // Detect card type from PAN
  private detectCardType(pan: string): string {
    if (!pan) return 'UNKNOWN';

    const firstDigit = pan.charAt(0);
    const firstTwoDigits = pan.substring(0, 2);
    const firstFourDigits = pan.substring(0, 4);

    // Visa
    if (firstDigit === '4') {
      return 'VISA';
    }

    // Mastercard
    if (firstDigit === '5' || (firstTwoDigits >= '22' && firstTwoDigits <= '27')) {
      return 'MASTERCARD';
    }

    // American Express
    if (firstTwoDigits === '34' || firstTwoDigits === '37') {
      return 'AMEX';
    }

    // Discover
    if (firstTwoDigits === '60' || firstFourDigits === '6011' ||
        (firstTwoDigits >= '64' && firstTwoDigits <= '65')) {
      return 'DISCOVER';
    }

    // Diners Club
    if (firstTwoDigits === '30' || firstTwoDigits === '36' || firstTwoDigits === '38') {
      return 'DINERS';
    }

    // JCB
    if (firstFourDigits >= '3528' && firstFourDigits <= '3589') {
      return 'JCB';
    }

    return 'UNKNOWN';
  }

  // Check printer status
  public async getPrinterStatus(): Promise<PrinterStatus> {
    if (!this.isConnected) {
      return {
        available: true,
        paperLevel: 'full',
        temperature: 'normal'
      };
    }
    
    try {
      if ((window as any).PAX && (window as any).PAX.getPrinterStatus) {
        const status = await (window as any).PAX.getPrinterStatus();
        return {
          available: status.available,
          paperLevel: status.paperLevel || 'full',
          temperature: status.temperature || 'normal'
        };
      }
      
      return {
        available: true,
        paperLevel: 'full',
        temperature: 'normal'
      };
    } catch (error) {
      return {
        available: false,
        paperLevel: 'empty',
        temperature: 'normal',
        error: error instanceof Error ? error.message : 'Printer check failed'
      };
    }
  }
  
  // Print receipt
  public async printReceipt(receiptText: string): Promise<boolean> {
    if (!this.isConnected) {
      console.log('Simulated receipt print:', receiptText);
      return true;
    }
    
    try {
      if ((window as any).PAX && (window as any).PAX.print) {
        await (window as any).PAX.print({
          text: receiptText,
          fontSize: 'normal',
          alignment: 'left'
        });
        return true;
      }
      
      // Fallback: try to use browser print
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>Receipt</title>
              <style>
                body { font-family: monospace; font-size: 12px; margin: 0; padding: 10px; }
                pre { white-space: pre-wrap; margin: 0; }
              </style>
            </head>
            <body>
              <pre>${receiptText}</pre>
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Print failed:', error);
      return false;
    }
  }
  
  // Get battery level
  public async getBatteryLevel(): Promise<number> {
    if (!this.isConnected) {
      return this.terminalInfo.batteryLevel;
    }
    
    try {
      if ((window as any).PAX && (window as any).PAX.getBatteryLevel) {
        const level = await (window as any).PAX.getBatteryLevel();
        this.terminalInfo.batteryLevel = level;
        return level;
      }
      
      return this.terminalInfo.batteryLevel;
    } catch (error) {
      return this.terminalInfo.batteryLevel;
    }
  }
  
  // Check network connectivity
  public async checkConnectivity(): Promise<{wifi: boolean, mobile: boolean, online: boolean}> {
    const online = navigator.onLine;
    
    if (!this.isConnected) {
      return {
        wifi: this.terminalInfo.wifiConnected,
        mobile: false,
        online
      };
    }
    
    try {
      if ((window as any).PAX && (window as any).PAX.getNetworkStatus) {
        const status = await (window as any).PAX.getNetworkStatus();
        return {
          wifi: status.wifi || false,
          mobile: status.mobile || false,
          online: status.online || online
        };
      }
      
      return {
        wifi: this.terminalInfo.wifiConnected,
        mobile: false,
        online
      };
    } catch (error) {
      return {
        wifi: false,
        mobile: false,
        online
      };
    }
  }
  
  // Beep for user feedback
  public async beep(type: 'success' | 'error' | 'warning' = 'success'): Promise<void> {
    if (!this.isConnected) {
      // Fallback to browser beep
      if (typeof Audio !== 'undefined') {
        try {
          const audio = new Audio(`data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT`);
          audio.play();
        } catch (e) {
          // Silent fail
        }
      }
      return;
    }
    
    try {
      if ((window as any).PAX && (window as any).PAX.beep) {
        await (window as any).PAX.beep({ type });
      }
    } catch (error) {
      // Silent fail
    }
  }
  
  // Display message on terminal screen
  public async displayMessage(message: string, timeoutMs: number = 3000): Promise<void> {
    if (!this.isConnected) {
      console.log('Terminal message:', message);
      return;
    }
    
    try {
      if ((window as any).PAX && (window as any).PAX.displayMessage) {
        await (window as any).PAX.displayMessage({
          message,
          timeout: timeoutMs
        });
      }
    } catch (error) {
      console.warn('Could not display message on terminal:', error);
    }
  }
}

// Export singleton instance
export const paxHardware = new PAXHardwareInterface();
