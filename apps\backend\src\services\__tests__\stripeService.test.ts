import { describe, it, expect, vi, beforeEach } from 'vitest';
import { StripeService } from '../stripeService';
import { createMockConnectionToken, createMockStripePaymentIntent } from '../../test/helpers';

// Mock Stripe
const mockStripe = {
  terminal: {
    connectionTokens: {
      create: vi.fn(),
    },
  },
  paymentIntents: {
    create: vi.fn(),
    capture: vi.fn(),
    retrieve: vi.fn(),
  },
};

vi.mock('stripe', () => {
  return {
    default: vi.fn(() => mockStripe),
  };
});

describe('StripeService', () => {
  let stripeService: StripeService;

  beforeEach(() => {
    stripeService = new StripeService();
    vi.clearAllMocks();
  });

  describe('createConnectionToken', () => {
    it('should create connection token successfully', async () => {
      const mockToken = createMockConnectionToken();
      mockStripe.terminal.connectionTokens.create.mockResolvedValue(mockToken);

      const result = await stripeService.createConnectionToken();

      expect(result).toEqual(mockToken);
      expect(mockStripe.terminal.connectionTokens.create).toHaveBeenCalledOnce();
    });

    it('should throw error when Stripe fails', async () => {
      mockStripe.terminal.connectionTokens.create.mockRejectedValue(
        new Error('Stripe API error')
      );

      await expect(stripeService.createConnectionToken()).rejects.toThrow(
        'Failed to create Stripe connection token'
      );
    });
  });

  describe('createPaymentIntent', () => {
    it('should create payment intent successfully', async () => {
      const mockIntent = createMockStripePaymentIntent();
      mockStripe.paymentIntents.create.mockResolvedValue(mockIntent);

      const params = {
        amount: 1000,
        currency: 'usd',
        metadata: { test: true },
      };

      const result = await stripeService.createPaymentIntent(params);

      expect(result).toEqual(mockIntent);
      expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith({
        amount: 1000,
        currency: 'usd',
        payment_method_types: ['card_present'],
        capture_method: 'manual',
        metadata: { test: true },
      });
    });

    it('should throw error for invalid amount', async () => {
      const params = {
        amount: -100,
        currency: 'usd',
      };

      await expect(stripeService.createPaymentIntent(params)).rejects.toThrow(
        'Invalid payment intent parameters'
      );
    });

    it('should throw error for invalid currency', async () => {
      const params = {
        amount: 1000,
        currency: 'invalid',
      };

      await expect(stripeService.createPaymentIntent(params)).rejects.toThrow(
        'Invalid payment intent parameters'
      );
    });

    it('should handle Stripe API errors', async () => {
      mockStripe.paymentIntents.create.mockRejectedValue(
        new Error('Stripe API error')
      );

      const params = {
        amount: 1000,
        currency: 'usd',
      };

      await expect(stripeService.createPaymentIntent(params)).rejects.toThrow(
        'Failed to create payment intent'
      );
    });
  });

  describe('capturePaymentIntent', () => {
    it('should capture payment intent successfully', async () => {
      const mockIntent = createMockStripePaymentIntent({ status: 'succeeded' });
      mockStripe.paymentIntents.capture.mockResolvedValue(mockIntent);

      const params = {
        paymentIntentId: 'pi_test_123',
      };

      const result = await stripeService.capturePaymentIntent(params);

      expect(result).toEqual(mockIntent);
      expect(mockStripe.paymentIntents.capture).toHaveBeenCalledWith(
        'pi_test_123',
        {}
      );
    });

    it('should capture with specific amount', async () => {
      const mockIntent = createMockStripePaymentIntent({ status: 'succeeded' });
      mockStripe.paymentIntents.capture.mockResolvedValue(mockIntent);

      const params = {
        paymentIntentId: 'pi_test_123',
        amountToCapture: 500,
      };

      const result = await stripeService.capturePaymentIntent(params);

      expect(result).toEqual(mockIntent);
      expect(mockStripe.paymentIntents.capture).toHaveBeenCalledWith(
        'pi_test_123',
        { amount_to_capture: 500 }
      );
    });

    it('should throw error for invalid payment intent ID', async () => {
      const params = {
        paymentIntentId: '',
      };

      await expect(stripeService.capturePaymentIntent(params)).rejects.toThrow(
        'Invalid capture parameters'
      );
    });

    it('should handle Stripe API errors', async () => {
      mockStripe.paymentIntents.capture.mockRejectedValue(
        new Error('Stripe API error')
      );

      const params = {
        paymentIntentId: 'pi_test_123',
      };

      await expect(stripeService.capturePaymentIntent(params)).rejects.toThrow(
        'Failed to capture payment intent'
      );
    });
  });

  describe('retrievePaymentIntent', () => {
    it('should retrieve payment intent successfully', async () => {
      const mockIntent = createMockStripePaymentIntent();
      mockStripe.paymentIntents.retrieve.mockResolvedValue(mockIntent);

      const result = await stripeService.retrievePaymentIntent('pi_test_123');

      expect(result).toEqual(mockIntent);
      expect(mockStripe.paymentIntents.retrieve).toHaveBeenCalledWith('pi_test_123');
    });

    it('should handle Stripe API errors', async () => {
      mockStripe.paymentIntents.retrieve.mockRejectedValue(
        new Error('Stripe API error')
      );

      await expect(
        stripeService.retrievePaymentIntent('pi_test_123')
      ).rejects.toThrow('Failed to retrieve payment intent');
    });
  });
});
