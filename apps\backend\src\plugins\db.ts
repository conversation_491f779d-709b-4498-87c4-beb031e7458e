import fp from 'fastify-plugin';
import mongoose from 'mongoose';
import { connectDB, disconnectDB } from '../config/db'; // Assuming connectDB handles internal state
import logger from '../lib/logger';

declare module 'fastify' {
  interface FastifyInstance {
    mongo: typeof mongoose;
  }
}

/**
 * This plugin connects to MongoDB using Mongoose.
 * It also decorates Fastify instance with `mongo` for accessing Mongoose.
 */
async function dbConnector(fastify: any, options: any) {
  try {
    // The connectDB function already handles the connection logic and logging
    // await connectDB(); // connectDB is called in app.ts to ensure it's attempted early

    // Decorate Fastify instance with Mongoose
    // This assumes connectDB has successfully established a connection or is managing it.
    // For a plugin, you might want it to own the connection initiation.
    // However, since connectDB is called in app.ts, we just provide the mongoose instance.
    if (mongoose.connection.readyState !== 1 && mongoose.connection.readyState !== 2) {
        logger.warn('DB plugin loaded, but Mongoose not yet connected/connecting. Connection is managed by app.ts startup.');
    }
    fastify.decorate('mongo', mongoose);


    // Register a hook to close the Mongoose connection when Fastify server closes
    fastify.addHook('onClose', async (instance: any, done: any) => {
      logger.info('Fastify server closing, ensuring MongoDB connection is closed.');
      await disconnectDB(); // Ensure disconnectDB handles already disconnected state gracefully
      logger.info('MongoDB connection status checked during Fastify onClose hook.');
      done();
    });

  } catch (err) {
    logger.error('Error in DB connector plugin during setup:', err);
    // Depending on your error handling strategy, you might rethrow or exit
    // throw err;
  }
}

export default fp(dbConnector, {
  name: 'dbConnector',
  // dependencies: [] // list any Fastify plugin dependencies
});
