version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: pax-pos-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-admin123}
      MONGO_INITDB_DATABASE: pax_pos_system
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - pax-pos-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend Service
  backend:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile
    container_name: pax-pos-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3001
      MONGO_URI: mongodb://admin:${MONGO_ROOT_PASSWORD:-admin123}@mongodb:27017/pax_pos_system?authSource=admin
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_PUBLISHABLE_KEY: ${STRIPE_PUBLISHABLE_KEY}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}
      MOCK_BANK_URL: http://mock-bank:3002/financial-message
      LOG_LEVEL: ${LOG_LEVEL:-info}
      JWT_SECRET: ${JWT_SECRET}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost}
      RATE_LIMIT_MAX: ${RATE_LIMIT_MAX:-100}
      RATE_LIMIT_WINDOW: ${RATE_LIMIT_WINDOW:-900000}
    ports:
      - "3001:3001"
    depends_on:
      mongodb:
        condition: service_healthy
      mock-bank:
        condition: service_healthy
    networks:
      - pax-pos-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Mock Bank Service
  mock-bank:
    build:
      context: .
      dockerfile: apps/mock-bank/Dockerfile
    container_name: pax-pos-mock-bank
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3002
    ports:
      - "3002:3002"
    networks:
      - pax-pos-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3002/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Frontend Service
  frontend:
    build:
      context: .
      dockerfile: apps/frontend/Dockerfile
    container_name: pax-pos-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - pax-pos-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  mongodb_data:
    driver: local

networks:
  pax-pos-network:
    driver: bridge
