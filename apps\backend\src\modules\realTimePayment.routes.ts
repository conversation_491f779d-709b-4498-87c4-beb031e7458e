import { FastifyInstance } from 'fastify';
import { realTimePaymentController } from '../controllers/realTimePaymentController';

export async function realTimePaymentRoutes(fastify: FastifyInstance) {
  // Process real-time payment
  fastify.post('/payments/process', {
    schema: {
      body: {
        type: 'object',
        properties: {
          amount: { type: 'number', minimum: 50, maximum: 99999999 },
          currency: { type: 'string', default: 'usd' },
          payment_method_types: { 
            type: 'array', 
            items: { type: 'string' },
            default: ['card']
          },
          confirm: { type: 'boolean', default: false },
          return_url: { type: 'string', format: 'uri' },
          customer_email: { type: 'string', format: 'email' },
          description: { type: 'string', maxLength: 1000 }
        },
        required: ['amount']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                status: { type: 'string' },
                amount: { type: 'number' },
                currency: { type: 'string' },
                client_secret: { type: 'string' },
                transaction_id: { type: 'string' },
                payment_method: { type: ['string', 'null'] },
                receipt: { type: ['object', 'null'] }
              }
            }
          }
        }
      }
    }
  }, realTimePaymentController.processPayment);

  // Confirm payment
  fastify.post('/payments/confirm', {
    schema: {
      body: {
        type: 'object',
        properties: {
          payment_intent_id: { type: 'string' },
          payment_method: { type: 'string' }
        },
        required: ['payment_intent_id']
      }
    }
  }, realTimePaymentController.confirmPayment);

  // Refund payment
  fastify.post('/payments/refund', {
    schema: {
      body: {
        type: 'object',
        properties: {
          payment_intent_id: { type: 'string' },
          amount: { type: 'number', minimum: 1 },
          reason: { 
            type: 'string', 
            enum: ['duplicate', 'fraudulent', 'requested_by_customer'],
            default: 'requested_by_customer'
          }
        },
        required: ['payment_intent_id']
      }
    }
  }, realTimePaymentController.refundPayment);

  // Get payment status
  fastify.get('/payments/:payment_intent_id/status', {
    schema: {
      params: {
        type: 'object',
        properties: {
          payment_intent_id: { type: 'string' }
        },
        required: ['payment_intent_id']
      }
    }
  }, realTimePaymentController.getPaymentStatus);

  // Cancel payment
  fastify.post('/payments/cancel', {
    schema: {
      body: {
        type: 'object',
        properties: {
          payment_intent_id: { type: 'string' }
        },
        required: ['payment_intent_id']
      }
    }
  }, realTimePaymentController.cancelPayment);
}
