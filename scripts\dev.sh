#!/bin/bash

# PAX POS System Development Script
# This script helps with development tasks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to install dependencies
install_deps() {
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed"
}

# Function to build all packages
build_all() {
    print_status "Building all packages..."
    npm run build
    print_success "Build completed"
}

# Function to start development servers
start_dev() {
    print_status "Starting development servers..."
    
    # Check if .env.development exists
    if [ ! -f ".env.development" ]; then
        print_warning ".env.development not found. Creating from template..."
        cp .env.example .env.development
        
        # Update for development
        sed -i 's/NODE_ENV=production/NODE_ENV=development/' .env.development
        sed -i 's/CORS_ORIGIN=http:\/\/localhost/CORS_ORIGIN=http:\/\/localhost:3000/' .env.development
        sed -i 's/MOCK_BANK_URL=http:\/\/localhost:3002\/financial-message/MOCK_BANK_URL=http:\/\/localhost:3002\/financial-message/' .env.development
        
        print_warning "Please update .env.development with your Stripe keys and other configuration"
    fi
    
    # Start MongoDB in Docker for development
    print_status "Starting MongoDB for development..."
    docker-compose -f docker-compose.dev.yml up -d mongodb
    
    # Wait for MongoDB to be ready
    print_status "Waiting for MongoDB to be ready..."
    sleep 10
    
    # Start all development servers in parallel
    print_status "Starting development servers in parallel..."
    npm run dev
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    npm run test
    print_success "Tests completed"
}

# Function to run linting
run_lint() {
    print_status "Running linting..."
    npm run lint
    print_success "Linting completed"
}

# Function to format code
format_code() {
    print_status "Formatting code..."
    npm run format
    print_success "Code formatted"
}

# Function to clean build artifacts
clean() {
    print_status "Cleaning build artifacts..."
    
    # Remove dist directories
    find . -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
    
    # Remove log files
    find . -name "*.log" -type f -delete 2>/dev/null || true
    
    print_success "Clean completed"
}

# Function to reset development environment
reset_dev() {
    print_status "Resetting development environment..."
    
    # Stop any running containers
    docker-compose -f docker-compose.dev.yml down --volumes --remove-orphans 2>/dev/null || true
    
    # Clean build artifacts
    clean
    
    # Reinstall dependencies
    install_deps
    
    # Rebuild everything
    build_all
    
    print_success "Development environment reset completed"
}

# Function to show help
show_help() {
    echo "PAX POS Development Script"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  install     Install dependencies"
    echo "  build       Build all packages"
    echo "  dev         Start development servers"
    echo "  test        Run tests"
    echo "  lint        Run linting"
    echo "  format      Format code"
    echo "  clean       Clean build artifacts"
    echo "  reset       Reset development environment"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev      # Start development servers"
    echo "  $0 test     # Run all tests"
    echo "  $0 reset    # Reset and reinstall everything"
}

# Main function
main() {
    case "${1:-dev}" in
        "install")
            install_deps
            ;;
        "build")
            build_all
            ;;
        "dev")
            start_dev
            ;;
        "test")
            run_tests
            ;;
        "lint")
            run_lint
            ;;
        "format")
            format_code
            ;;
        "clean")
            clean
            ;;
        "reset")
            reset_dev
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
