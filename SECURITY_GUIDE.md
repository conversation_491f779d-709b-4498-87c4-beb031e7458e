# 🔒 PAX A920 Pro POS Terminal - Security Guide

## 🛡️ Security Architecture Overview

### **PCI DSS Compliance Requirements**

This POS terminal must comply with PCI DSS (Payment Card Industry Data Security Standard) Level 1 requirements:

#### **1. Network Security**
- **Firewall Configuration**: Implement and maintain firewall rules
- **Network Segmentation**: Isolate POS network from other systems
- **VPN Access**: Secure remote access for maintenance
- **Intrusion Detection**: Monitor for unauthorized access

#### **2. Data Protection**
- **Encryption at Rest**: All sensitive data encrypted using AES-256
- **Encryption in Transit**: TLS 1.3 for all communications
- **Key Management**: Hardware Security Module (HSM) for key storage
- **Data Masking**: PAN (Primary Account Number) masking in logs

#### **3. Access Control**
- **Multi-Factor Authentication**: Required for all administrative access
- **Role-Based Access**: Principle of least privilege
- **Session Management**: Automatic timeout and secure session handling
- **Audit Logging**: Comprehensive logging of all access attempts

### **Terminal-Specific Security Measures**

#### **1. Hardware Security**
```bash
# Secure boot configuration
SECURE_BOOT=enabled
TPM_ENABLED=true
DISK_ENCRYPTION=LUKS2

# Hardware tamper detection
TAMPER_DETECTION=enabled
CASE_INTRUSION_ALERT=enabled
```

#### **2. Operating System Hardening**
```bash
# Disable unnecessary services
systemctl disable bluetooth
systemctl disable wifi
systemctl disable ssh  # Only enable for maintenance

# File system permissions
chmod 700 /opt/pax-pos
chown pax-user:pax-group /opt/pax-pos

# Kernel hardening
echo "kernel.dmesg_restrict = 1" >> /etc/sysctl.conf
echo "net.ipv4.conf.all.send_redirects = 0" >> /etc/sysctl.conf
```

#### **3. Application Security**

##### **Environment Variables Security**
```bash
# Production environment file (.env.production)
NODE_ENV=production

# Encrypted database connection
MONGO_URI="mongodb+srv://encrypted_user:<EMAIL>/pos_prod?ssl=true&authSource=admin"

# Stripe production keys (encrypted)
STRIPE_SECRET_KEY="sk_live_encrypted_key"
STRIPE_PUBLISHABLE_KEY="pk_live_encrypted_key"
STRIPE_WEBHOOK_SECRET="whsec_encrypted_webhook_secret"

# Strong JWT secret (256-bit)
JWT_SECRET="your_256_bit_secret_key_here_must_be_very_long_and_random"

# Security headers
CORS_ORIGIN="https://your-secure-domain.com"
RATE_LIMIT_MAX=50
RATE_LIMIT_WINDOW=900000

# Logging
LOG_LEVEL=warn
AUDIT_LOG_ENABLED=true
```

##### **Code Security Measures**
```typescript
// Input validation and sanitization
import { z } from 'zod';
import rateLimit from '@fastify/rate-limit';
import helmet from '@fastify/helmet';

// Rate limiting
app.register(rateLimit, {
  max: 100,
  timeWindow: '15 minutes'
});

// Security headers
app.register(helmet, {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
});

// Input validation schema
const paymentSchema = z.object({
  amount: z.number().min(1).max(999999),
  currency: z.string().length(3),
  paymentMethod: z.enum(['card', 'cash']),
});
```

## 🏗️ Production Deployment Architecture

### **1. Container Security**
```dockerfile
# Multi-stage build for security
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
RUN addgroup -g 1001 -S pax-user && \
    adduser -S pax-user -u 1001

# Security updates
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init

USER pax-user
WORKDIR /app
COPY --from=builder --chown=pax-user:pax-user /app .

EXPOSE 3001
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/server.js"]
```

### **2. Kubernetes Deployment**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pax-pos-terminal
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pax-pos
  template:
    metadata:
      labels:
        app: pax-pos
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: pax-pos
        image: pax-pos:latest
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        resources:
          limits:
            memory: "512Mi"
            cpu: "500m"
          requests:
            memory: "256Mi"
            cpu: "250m"
        env:
        - name: NODE_ENV
          value: "production"
        - name: MONGO_URI
          valueFrom:
            secretKeyRef:
              name: pax-secrets
              key: mongo-uri
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: tmp
        emptyDir: {}
      - name: logs
        emptyDir: {}
```

### **3. Network Security**
```yaml
# Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: pax-pos-network-policy
spec:
  podSelector:
    matchLabels:
      app: pax-pos
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: pax-namespace
    ports:
    - protocol: TCP
      port: 3001
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS only
    - protocol: TCP
      port: 27017  # MongoDB
```

## 📦 Terminal Bundling Strategy

### **1. Embedded Linux Distribution**
```bash
# Custom Yocto Linux build
DISTRO = "pax-pos-distro"
MACHINE = "pax-a920-pro"

# Security features
DISTRO_FEATURES_append = " pam systemd"
EXTRA_IMAGE_FEATURES += "read-only-rootfs"

# Package selection
IMAGE_INSTALL_append = " \
    nodejs \
    mongodb \
    nginx \
    fail2ban \
    iptables \
    openssl \
    "
```

### **2. Application Packaging**
```bash
#!/bin/bash
# build-terminal.sh

# Build all services
npm run build:all

# Create application bundle
mkdir -p /opt/pax-pos/{backend,frontend,database}

# Copy built applications
cp -r apps/backend/dist/* /opt/pax-pos/backend/
cp -r apps/frontend/dist/* /opt/pax-pos/frontend/
cp -r apps/mock-bank/dist/* /opt/pax-pos/mock-bank/

# Set permissions
chown -R pax-user:pax-group /opt/pax-pos
chmod -R 750 /opt/pax-pos

# Create systemd services
cp scripts/systemd/*.service /etc/systemd/system/
systemctl enable pax-pos-backend
systemctl enable pax-pos-frontend
systemctl enable pax-pos-database
```

### **3. Security Hardening Script**
```bash
#!/bin/bash
# harden-terminal.sh

# Disable unnecessary services
systemctl disable bluetooth
systemctl disable wifi
systemctl disable cups
systemctl disable avahi-daemon

# Configure firewall
iptables -F
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT
iptables -A INPUT -i lo -j ACCEPT
iptables -A INPUT -p tcp --dport 3001 -j ACCEPT
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# Save firewall rules
iptables-save > /etc/iptables/rules.v4

# Configure fail2ban
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log

[pax-pos]
enabled = true
port = 3001
logpath = /opt/pax-pos/logs/access.log
maxretry = 5
EOF

# Enable services
systemctl enable fail2ban
systemctl enable iptables
```

## 🔐 Encryption Implementation

### **1. Database Encryption**
```typescript
// Field-level encryption for sensitive data
import { createCipher, createDecipher } from 'crypto';

class EncryptionService {
  private static key = process.env.ENCRYPTION_KEY;
  
  static encrypt(text: string): string {
    const cipher = createCipher('aes-256-cbc', this.key);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }
  
  static decrypt(encryptedText: string): string {
    const decipher = createDecipher('aes-256-cbc', this.key);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
}
```

### **2. Communication Security**
```typescript
// TLS configuration
const tlsOptions = {
  key: fs.readFileSync('/etc/ssl/private/pax-pos.key'),
  cert: fs.readFileSync('/etc/ssl/certs/pax-pos.crt'),
  ca: fs.readFileSync('/etc/ssl/certs/ca-bundle.crt'),
  secureProtocol: 'TLSv1_3_method',
  ciphers: 'ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256',
  honorCipherOrder: true,
};
```

## 🚨 Monitoring & Alerting

### **1. Security Monitoring**
```typescript
// Security event logging
class SecurityLogger {
  static logSecurityEvent(event: SecurityEvent) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event: event.type,
      severity: event.severity,
      source: event.source,
      details: event.details,
      userId: event.userId,
      sessionId: event.sessionId,
    };
    
    // Send to SIEM system
    this.sendToSIEM(logEntry);
    
    // Local logging
    logger.warn('Security Event', logEntry);
  }
}
```

### **2. Health Monitoring**
```typescript
// System health checks
app.get('/health', async (request, reply) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      database: await checkDatabaseHealth(),
      stripe: await checkStripeHealth(),
      printer: await checkPrinterHealth(),
    },
    security: {
      certificateExpiry: getCertificateExpiry(),
      lastSecurityScan: getLastSecurityScan(),
    }
  };
  
  return reply.send(health);
});
```

This security guide ensures your PAX A920 Pro POS terminal meets enterprise-grade security standards while maintaining PCI DSS compliance for payment processing.
