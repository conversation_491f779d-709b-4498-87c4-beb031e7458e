{
  "name": "backend",
  "version": "0.1.0",
  "private": true,
  "main": "dist/server.js",
  "scripts": {
    "build": "tsc -p tsconfig.build.json",
    "dev": "tsc-watch --onSuccess \"node dist/server.js\" --onFailure \"echo BE Build Failed\"",
    "start": "node dist/server.js",
    "lint": "eslint . --ext .ts",
    "test": "vitest run"
  },
  "dependencies": {
    // To be added: fastify, mongoose, pino, dotenv, etc.
    "shared-types": "workspace:*"
  },
  "devDependencies": {
    "typescript": "^5.4.5",
    "tsc-watch": "^6.2.0",
    "eslint": "^8.57.0",
    "eslint-config-custom": "workspace:*",
    "tsconfig-custom": "workspace:*",
    "@types/node": "^20.12.7",
    "vitest": "^1.5.0" // For testing
  }
}
