{"name": "backend", "version": "1.0.0", "private": true, "main": "dist/server.js", "scripts": {"build": "tsc -p tsconfig.build.json", "dev": "tsc-watch --onSuccess \"node dist/server.js\" --onFailure \"echo Build Failed\"", "start": "node dist/server.js", "start:prod": "NODE_ENV=production node dist/server.js", "lint": "eslint . --ext .ts --fix", "lint:check": "eslint . --ext .ts", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "clean": "rm -rf dist"}, "dependencies": {"fastify": "^4.26.2", "@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^9.1.0", "mongoose": "^8.3.0", "stripe": "^15.1.0", "pino": "^8.19.0", "pino-pretty": "^11.0.0", "dotenv": "^16.4.5", "zod": "^3.22.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "axios": "^1.6.8"}, "devDependencies": {"typescript": "^5.4.5", "tsc-watch": "^6.2.0", "eslint": "^8.57.0", "@types/node": "^20.12.7", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "vitest": "^1.5.0", "@vitest/coverage-v8": "^1.5.0"}}