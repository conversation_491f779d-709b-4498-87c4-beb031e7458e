import fp from 'fastify-plugin';
import { FastifyInstance, FastifyError } from 'fastify';
import { env } from '../config/env';
import logger from '../lib/logger';

interface AppError extends FastifyError {
  errors?: any; // For validation errors or more detailed error payloads
}

/**
 * Centralized error handler for Fastify.
 * It catches errors from routes and other plugins and formats them into a standard JSON response.
 */
async function errorHandlerPlugin(fastify: FastifyInstance) {
  fastify.setErrorHandler((error: AppError, request, reply) => {
    const { validation, statusCode } = error;

    // Log the error
    // Avoid logging validation errors in full detail in production if they contain sensitive input
    if (statusCode && statusCode >= 500) {
      logger.error({ err: error, requestId: request.id }, `Server Error: ${error.message}`);
    } else if (validation) {
      logger.warn({ err: error, requestId: request.id, validationErrors: error.errors }, `Validation Error: ${error.message}`);
    } else {
      logger.info({ err: error, requestId: request.id }, `Client Error: ${error.message}`);
    }

    if (validation) {
      // Handle validation errors specifically (e.g., from fastify-type-provider-zod or @fastify/sensible assertions)
      reply.status(400).send({
        success: false,
        message: 'Validation Error',
        error: {
          code: error.code || 'VALIDATION_ERROR',
          details: error.errors || error.message, // 'errors' usually comes from Zod or similar
        },
      });
      return;
    }

    const effectiveStatusCode = statusCode && statusCode >= 400 && statusCode < 600 ? statusCode : 500;

    const responsePayload: Record<string, any> = {
      success: false,
      message: error.message || 'An unexpected error occurred.',
      error: {
        code: error.code || 'UNEXPECTED_ERROR',
      },
    };

    // Add stack trace in development, but not in production for security
    if (env.NODE_ENV !== 'production' && effectiveStatusCode >= 500) {
      responsePayload.error.stack = error.stack;
    }
    if (error.errors) {
        responsePayload.error.details = error.errors;
    }


    reply.status(effectiveStatusCode).send(responsePayload);
  });
}

export default fp(errorHandlerPlugin, {
  name: 'errorHandler',
  // Ensure this plugin is loaded after others that might throw errors
  // but before your routes if you want it to catch everything.
  // Fastify's `sensible` plugin also provides its own error handling,
  // so you might customize this to work with or replace parts of it.
});
