{"name": "example", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"react": "17.0.2", "react-native": "0.65.1", "react-native-pax-library": "^1.0.7"}, "devDependencies": {"@babel/core": "^7.15.5", "@babel/runtime": "^7.15.4", "@react-native-community/eslint-config": "^3.0.1", "babel-jest": "^27.2.0", "eslint": "^7.32.0", "jest": "^27.2.0", "metro-react-native-babel-preset": "^0.66.2", "react-native-codegen": "^0.0.7", "react-test-renderer": "17.0.2"}, "jest": {"preset": "react-native"}}