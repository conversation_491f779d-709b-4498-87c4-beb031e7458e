# PAX A920 Pro WebView Wrapper

This Android app provides a WebView wrapper that loads your React POS web application and provides JavaScript bridges to access PAX A920 Pro hardware features.

## Setup Instructions

### 1. Prerequisites
- Android Studio installed
- PAX A920 Pro terminal
- Your React web app running (either locally or deployed)

### 2. Get PAX Neptune SDK
1. Contact PAX Technology to get the Neptune SDK files:
   - `neptune-lite.aar`
   - `device-config.aar` 
   - `libDeviceConfig.so`

2. Place the files:
   - Put `.aar` files in `app/libs/` folder
   - Put `libDeviceConfig.so` in `app/src/main/jniLibs/armeabi-v7a/` folder

### 3. Configure the App
1. Open `MainActivity.java` and update the `WEB_APP_URL`:
   ```java
   // For development (your computer's IP)
   private static final String WEB_APP_URL = "http://*************:5173";
   
   // For production
   private static final String WEB_APP_URL = "https://your-domain.com";
   ```

2. Update `app/build.gradle` to include Neptune SDK:
   ```gradle
   dependencies {
       // ... existing dependencies
       implementation files('libs/neptune-lite.aar')
       implementation files('libs/device-config.aar')
   }
   ```

### 4. Build and Install
1. Open project in Android Studio
2. Connect PAX A920 Pro via USB or use ADB over network
3. Build and run the app

### 5. Update Your React Web App
Your React app will now have access to `window.Android` object with these methods:

```javascript
// Check if running in PAX terminal
if (window.Android) {
    console.log('Running on PAX Terminal');
    
    // Get terminal info
    const info = JSON.parse(window.Android.getTerminalInfo());
    
    // Start card reading
    window.Android.startCardReading('onCardReadComplete');
    
    // Print text
    window.Android.printText('Hello World', '{}');
    
    // Print receipt
    const receiptLines = JSON.stringify([
        { text: 'Receipt Header', config: { alignment: 'center', bold: true } },
        { text: 'Amount: $10.00', config: { fontSize: 'large' } }
    ]);
    window.Android.printReceipt(receiptLines);
    
    // Beep
    window.Android.beep(500);
}
```

## JavaScript Bridge API

### Terminal Information
- `getTerminalInfo()` - Returns JSON string with terminal details
- `getBatteryLevel()` - Returns battery level (0-100)
- `isHardwareAvailable()` - Returns boolean
- `getHardwareStatus()` - Returns JSON string with hardware status

### Card Reader
- `startCardReading(callbackName)` - Start reading card, calls JS function when done
- `stopCardReading()` - Stop card reading

### Printer
- `printText(text, configJson)` - Print simple text
- `printReceipt(receiptLinesJson)` - Print formatted receipt

### Other Hardware
- `beep(duration)` - Play beep sound
- `takePicture(callbackName)` - Take picture with camera
- `getLocation(callbackName)` - Get GPS location

### Utility
- `log(level, message)` - Log to Android console

## Development Tips

1. **Testing without PAX hardware**: The bridge includes simulation mode for development
2. **Debugging**: Use Chrome DevTools to debug your web app: `chrome://inspect`
3. **Network access**: Make sure your PAX terminal can access your development server
4. **HTTPS**: For production, use HTTPS for your web app

## File Structure
```
android-webview-wrapper/
├── app/
│   ├── build.gradle
│   ├── src/main/
│   │   ├── AndroidManifest.xml
│   │   ├── java/com/yourcompany/paxpos/
│   │   │   ├── MainActivity.java
│   │   │   └── PAXHardwareBridge.java
│   │   ├── res/layout/
│   │   │   └── activity_main.xml
│   │   └── jniLibs/armeabi-v7a/
│   │       └── libDeviceConfig.so
│   └── libs/
│       ├── neptune-lite.aar
│       └── device-config.aar
└── build.gradle
```

## Troubleshooting

1. **App crashes on startup**: Check if Neptune SDK files are properly placed
2. **Can't access web app**: Verify network connectivity and URL
3. **Hardware not working**: Ensure PAX permissions are granted
4. **JavaScript errors**: Check browser console in Chrome DevTools

## Next Steps

1. Get Neptune SDK from PAX Technology
2. Update the bridge code to use real Neptune APIs instead of simulation
3. Test all hardware features
4. Deploy your React app and update the URL
5. Build release APK for production use
