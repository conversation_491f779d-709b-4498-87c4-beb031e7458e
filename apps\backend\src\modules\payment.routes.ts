import { FastifyInstance } from 'fastify';
import { createTransaction } from '../services/transactionService';
import { addLog } from '../services/logService';

export default async function (app: FastifyInstance) {
  app.post('/payment/simulate', async (request, reply) => {
    const { amount, status } = request.body as { amount: number; status: 'success' | 'failure' };
    if (typeof amount !== 'number' || !['success', 'failure'].includes(status)) {
      return reply.status(400).send({ error: 'Invalid input' });
    }
    const txn = createTransaction(amount, status);
    addLog('stripe', `Simulated payment: ${status} for amount ${amount}`);
    reply.send(txn);
  });
}
