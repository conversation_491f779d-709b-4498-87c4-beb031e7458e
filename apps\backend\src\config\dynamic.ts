import { env } from './env';

// Dynamic configuration that can be updated at runtime
export interface DynamicConfig {
  terminal: {
    id: string;
    model: string;
    location: string;
    timezone: string;
  };
  merchant: {
    id: string;
    name: string;
    address: string;
    phone: string;
    email: string;
  };
  payment: {
    currency: string;
    timeout: number;
    retryAttempts: number;
    minimumAmount: number;
    maximumAmount: number;
  };
  receipt: {
    enabled: boolean;
    copies: number;
    includeSignature: boolean;
    footerText: string;
  };
  features: {
    cardReader: boolean;
    printer: boolean;
    audio: boolean;
    vibration: boolean;
    manualEntry: boolean;
  };
  ui: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    fontSize: 'small' | 'medium' | 'large';
    orientation: 'portrait' | 'landscape' | 'auto';
  };
}

// Default configuration
const defaultConfig: DynamicConfig = {
  terminal: {
    id: env.TERMINAL_ID || 'PAX_A920_001',
    model: 'PAX A920 Pro',
    location: 'Store Location',
    timezone: 'America/New_York'
  },
  merchant: {
    id: env.MERCHANT_ID || 'MERCHANT_001',
    name: 'Your Business Name',
    address: '123 Business St, City, State 12345',
    phone: '(*************',
    email: '<EMAIL>'
  },
  payment: {
    currency: env.DEFAULT_CURRENCY || 'usd',
    timeout: parseInt(env.PAYMENT_TIMEOUT || '30000'),
    retryAttempts: parseInt(env.RETRY_ATTEMPTS || '3'),
    minimumAmount: parseInt(env.MIN_AMOUNT || '50'), // 50 cents
    maximumAmount: parseInt(env.MAX_AMOUNT || '99999999') // $999,999.99
  },
  receipt: {
    enabled: env.RECEIPT_ENABLED !== 'false',
    copies: parseInt(env.RECEIPT_COPIES || '2'),
    includeSignature: env.RECEIPT_SIGNATURE !== 'false',
    footerText: env.RECEIPT_FOOTER || 'Thank you for your business!'
  },
  features: {
    cardReader: env.CARD_READER_ENABLED !== 'false',
    printer: env.PRINTER_ENABLED !== 'false',
    audio: env.AUDIO_ENABLED !== 'false',
    vibration: env.VIBRATION_ENABLED !== 'false',
    manualEntry: env.MANUAL_ENTRY_ENABLED !== 'false'
  },
  ui: {
    theme: (env.UI_THEME as 'light' | 'dark' | 'auto') || 'auto',
    language: env.UI_LANGUAGE || 'en',
    fontSize: (env.UI_FONT_SIZE as 'small' | 'medium' | 'large') || 'medium',
    orientation: (env.UI_ORIENTATION as 'portrait' | 'landscape' | 'auto') || 'portrait'
  }
};

// Runtime configuration storage
let currentConfig: DynamicConfig = { ...defaultConfig };

// Configuration management class
export class ConfigManager {
  static getConfig(): DynamicConfig {
    return { ...currentConfig };
  }

  static updateConfig(updates: Partial<DynamicConfig>): void {
    currentConfig = this.mergeDeep(currentConfig, updates);
  }

  static resetConfig(): void {
    currentConfig = { ...defaultConfig };
  }

  static getTerminalInfo() {
    return {
      id: currentConfig.terminal.id,
      model: currentConfig.terminal.model,
      location: currentConfig.terminal.location,
      timezone: currentConfig.terminal.timezone,
      merchantId: currentConfig.merchant.id,
      merchantName: currentConfig.merchant.name
    };
  }

  static getPaymentConfig() {
    return {
      currency: currentConfig.payment.currency,
      timeout: currentConfig.payment.timeout,
      retryAttempts: currentConfig.payment.retryAttempts,
      minimumAmount: currentConfig.payment.minimumAmount,
      maximumAmount: currentConfig.payment.maximumAmount
    };
  }

  static getReceiptConfig() {
    return {
      enabled: currentConfig.receipt.enabled,
      copies: currentConfig.receipt.copies,
      includeSignature: currentConfig.receipt.includeSignature,
      footerText: currentConfig.receipt.footerText
    };
  }

  static getFeatureFlags() {
    return { ...currentConfig.features };
  }

  static getUIConfig() {
    return { ...currentConfig.ui };
  }

  // Deep merge utility
  private static mergeDeep(target: any, source: any): any {
    const output = { ...target };
    
    if (this.isObject(target) && this.isObject(source)) {
      Object.keys(source).forEach(key => {
        if (this.isObject(source[key])) {
          if (!(key in target)) {
            Object.assign(output, { [key]: source[key] });
          } else {
            output[key] = this.mergeDeep(target[key], source[key]);
          }
        } else {
          Object.assign(output, { [key]: source[key] });
        }
      });
    }
    
    return output;
  }

  private static isObject(item: any): boolean {
    return item && typeof item === 'object' && !Array.isArray(item);
  }

  // Validate configuration
  static validateConfig(config: Partial<DynamicConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (config.payment) {
      if (config.payment.minimumAmount && config.payment.minimumAmount < 1) {
        errors.push('Minimum amount must be at least 1 cent');
      }
      if (config.payment.maximumAmount && config.payment.maximumAmount > 99999999) {
        errors.push('Maximum amount cannot exceed $999,999.99');
      }
      if (config.payment.timeout && (config.payment.timeout < 5000 || config.payment.timeout > 120000)) {
        errors.push('Payment timeout must be between 5 and 120 seconds');
      }
    }

    if (config.terminal?.id && !/^[A-Z0-9_]+$/.test(config.terminal.id)) {
      errors.push('Terminal ID must contain only uppercase letters, numbers, and underscores');
    }

    if (config.merchant?.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(config.merchant.email)) {
      errors.push('Invalid merchant email format');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // Load configuration from external source (database, file, etc.)
  static async loadFromSource(source: 'database' | 'file' | 'api'): Promise<void> {
    try {
      switch (source) {
        case 'database':
          // Load from MongoDB
          // const config = await ConfigModel.findOne({ type: 'terminal' });
          // if (config) this.updateConfig(config.data);
          break;
        
        case 'file':
          // Load from JSON file
          // const fs = await import('fs/promises');
          // const configFile = await fs.readFile('./config.json', 'utf8');
          // const config = JSON.parse(configFile);
          // this.updateConfig(config);
          break;
        
        case 'api':
          // Load from remote API
          // const response = await fetch('/api/config');
          // const config = await response.json();
          // this.updateConfig(config);
          break;
      }
    } catch (error) {
      console.warn(`Failed to load configuration from ${source}:`, error);
    }
  }

  // Save configuration to external source
  static async saveToSource(source: 'database' | 'file' | 'api'): Promise<void> {
    try {
      switch (source) {
        case 'database':
          // Save to MongoDB
          // await ConfigModel.updateOne(
          //   { type: 'terminal' },
          //   { data: currentConfig },
          //   { upsert: true }
          // );
          break;
        
        case 'file':
          // Save to JSON file
          // const fs = await import('fs/promises');
          // await fs.writeFile('./config.json', JSON.stringify(currentConfig, null, 2));
          break;
        
        case 'api':
          // Save to remote API
          // await fetch('/api/config', {
          //   method: 'POST',
          //   headers: { 'Content-Type': 'application/json' },
          //   body: JSON.stringify(currentConfig)
          // });
          break;
      }
    } catch (error) {
      console.error(`Failed to save configuration to ${source}:`, error);
    }
  }
}

// Export default configuration
export { defaultConfig };
export default ConfigManager;
