import { FastifyRequest, FastifyReply } from 'fastify';
import { protocolService } from '../services/protocolService';
import { z } from 'zod';
import { logger } from '../config/logger';

const controllerLogger = logger.child({ module: 'protocol-controller' });

const triggerProtocolSchema = z.object({
  protocolEventCode: z.enum(['101.1', '101.3', '101.5', '101.8']),
  transactionId: z.string(),
  stripePaymentIntentId: z.string().optional(),
  amount: z.number().positive(),
  metadata: z.record(z.any()).optional(),
});

export const triggerProtocol = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const validatedBody = triggerProtocolSchema.parse(request.body);

    controllerLogger.info({
      protocolCode: validatedBody.protocolEventCode,
      transactionId: validatedBody.transactionId
    }, 'Triggering protocol message');

    const response = await protocolService.sendProtocolMessage(validatedBody);

    return reply.send({
      success: true,
      data: {
        protocolCode: validatedBody.protocolEventCode,
        description: protocolService.getProtocolDescription(validatedBody.protocolEventCode),
        bankResponse: response,
        transactionId: validatedBody.transactionId
      }
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      controllerLogger.warn({ error: error.errors }, 'Invalid protocol trigger request');
      return reply.status(400).send({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid request parameters',
        details: error.errors
      });
    }

    controllerLogger.error({ error }, 'Failed to trigger protocol');
    return reply.status(500).send({
      success: false,
      error: 'PROTOCOL_ERROR',
      message: 'Failed to trigger protocol message'
    });
  }
};
