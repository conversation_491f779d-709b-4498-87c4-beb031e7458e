import { FastifyRequest, FastifyReply } from 'fastify';
import { buildProtocolMessage } from '../services/protocolService';
import { addLog } from '../services/logService';

export const triggerProtocol = async (request: FastifyRequest, reply: FastifyReply) => {
  const { protocol, data } = request.body as any;
  if (!protocol || typeof data !== 'object') {
    return reply.status(400).send({ error: 'Invalid input' });
  }
  const msg = buildProtocolMessage(protocol, data);
  addLog('bank', `Protocol ${protocol} triggered: ${JSON.stringify(msg)}`);
  return reply.send(msg);
};
