import { build } from './app';
import { env } from './config/env';

const app = build({
  // You can pass Fastify server options here if needed for server.ts specifically
  // For example, if you want different logger settings for local dev vs deployed
});

const start = async () => {
  try {
    const port = env.PORT || 3000; // Default to 3000 if PORT not in env
    await app.listen({ port: port, host: '0.0.0.0' }); // Listen on all available network interfaces
    app.log.info(`Server listening on port ${port}`);
    // console.log(app.printRoutes()); // Useful for debugging routes
  } catch (err) {
    app.log.error(err);
    process.exit(1);
  }
};

start();
