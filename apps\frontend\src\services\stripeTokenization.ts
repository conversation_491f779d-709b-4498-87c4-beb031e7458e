/**
 * Stripe Tokenization Service
 * 
 * Handles PCI-compliant tokenization of card data with Stripe
 * Converts encrypted card data to Stripe payment methods
 */

import { SecureCardData } from './customCardReader';

export interface StripeTokenizationResult {
  success: boolean;
  paymentMethod?: any;
  token?: string;
  error?: string;
}

export interface StripePaymentMethodData {
  type: 'card';
  card: {
    token?: string;
    encrypted_data?: string;
    ksn?: string;
  };
  billing_details?: {
    name?: string;
    email?: string;
    address?: {
      line1?: string;
      city?: string;
      state?: string;
      postal_code?: string;
      country?: string;
    };
  };
  metadata?: Record<string, string>;
}

export class StripeTokenizationService {
  private stripe: any = null;
  private publishableKey: string;

  constructor(publishableKey: string) {
    this.publishableKey = publishableKey;
    this.initializeStripe();
  }

  /**
   * Initialize Stripe SDK
   */
  private async initializeStripe(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && (window as any).Stripe) {
        this.stripe = (window as any).Stripe(this.publishableKey);
        console.log('Stripe SDK initialized for tokenization');
      } else {
        throw new Error('Stripe SDK not loaded');
      }
    } catch (error) {
      console.error('Failed to initialize Stripe SDK:', error);
    }
  }

  /**
   * Create Stripe payment method from encrypted card data
   */
  async createPaymentMethodFromEncryptedData(
    cardData: SecureCardData,
    billingDetails?: any
  ): Promise<StripeTokenizationResult> {
    try {
      if (!this.stripe) {
        throw new Error('Stripe not initialized');
      }

      // For encrypted card data from PAX terminal, we need to use Stripe's
      // encrypted card data API or send to backend for processing
      const paymentMethodData: StripePaymentMethodData = {
        type: 'card',
        card: {
          encrypted_data: cardData.encryptedPan,
          ksn: cardData.ksn
        },
        billing_details: {
          name: cardData.cardholderName,
          ...billingDetails
        },
        metadata: {
          terminal_id: cardData.terminalId,
          card_type: cardData.cardType || 'unknown',
          card_brand: cardData.cardBrand || 'unknown',
          sequence_number: cardData.sequenceNumber || '',
          emv_aid: cardData.emvData?.aid || '',
          emv_cryptogram: cardData.emvData?.cryptogram || ''
        }
      };

      // Send encrypted data to backend for Stripe processing
      const result = await this.processEncryptedCardData(paymentMethodData);
      
      return {
        success: true,
        paymentMethod: result.paymentMethod,
        token: result.token
      };

    } catch (error) {
      console.error('Failed to create payment method from encrypted data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Tokenization failed'
      };
    }
  }

  /**
   * Send encrypted card data to backend for Stripe processing
   */
  private async processEncryptedCardData(paymentMethodData: StripePaymentMethodData): Promise<any> {
    try {
      const response = await fetch('/api/v1/stripe/create-payment-method-encrypted', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          payment_method_data: paymentMethodData,
          encryption_type: 'pax_terminal',
          terminal_type: 'A920_Pro'
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Backend processing failed');
      }

      return result.data;
    } catch (error) {
      console.error('Backend processing failed:', error);
      throw error;
    }
  }

  /**
   * Create payment method for manual card entry (fallback)
   */
  async createPaymentMethodFromManualEntry(
    cardNumber: string,
    expiryMonth: number,
    expiryYear: number,
    cvc: string,
    cardholderName?: string,
    billingDetails?: any
  ): Promise<StripeTokenizationResult> {
    try {
      if (!this.stripe) {
        throw new Error('Stripe not initialized');
      }

      const result = await this.stripe.createPaymentMethod({
        type: 'card',
        card: {
          number: cardNumber,
          exp_month: expiryMonth,
          exp_year: expiryYear,
          cvc: cvc
        },
        billing_details: {
          name: cardholderName,
          ...billingDetails
        }
      });

      if (result.error) {
        return {
          success: false,
          error: result.error.message
        };
      }

      return {
        success: true,
        paymentMethod: result.paymentMethod
      };

    } catch (error) {
      console.error('Manual payment method creation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Manual tokenization failed'
      };
    }
  }

  /**
   * Create payment intent with payment method
   */
  async createPaymentIntent(
    amount: number,
    currency: string = 'usd',
    paymentMethodId: string,
    metadata?: Record<string, string>
  ): Promise<any> {
    try {
      const response = await fetch('/api/v1/stripe/payment-intents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          currency,
          payment_method: paymentMethodId,
          confirmation_method: 'manual',
          confirm: true,
          metadata: {
            source: 'pax_terminal',
            ...metadata
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;

    } catch (error) {
      console.error('Payment intent creation failed:', error);
      throw error;
    }
  }

  /**
   * Confirm payment intent (for 3D Secure or additional authentication)
   */
  async confirmPaymentIntent(
    paymentIntentClientSecret: string,
    paymentMethodId?: string
  ): Promise<any> {
    try {
      if (!this.stripe) {
        throw new Error('Stripe not initialized');
      }

      const result = await this.stripe.confirmCardPayment(
        paymentIntentClientSecret,
        paymentMethodId ? { payment_method: paymentMethodId } : undefined
      );

      return result;

    } catch (error) {
      console.error('Payment confirmation failed:', error);
      throw error;
    }
  }

  /**
   * Handle EMV transaction with Stripe
   */
  async processEMVTransaction(
    cardData: SecureCardData,
    amount: number,
    currency: string = 'usd'
  ): Promise<any> {
    try {
      // Create payment method from EMV data
      const tokenResult = await this.createPaymentMethodFromEncryptedData(cardData);
      
      if (!tokenResult.success || !tokenResult.paymentMethod) {
        throw new Error(tokenResult.error || 'Failed to create payment method');
      }

      // Create and confirm payment intent
      const paymentIntent = await this.createPaymentIntent(
        amount,
        currency,
        tokenResult.paymentMethod.id,
        {
          emv_aid: cardData.emvData?.aid || '',
          emv_cryptogram: cardData.emvData?.cryptogram || '',
          emv_cryptogram_type: cardData.emvData?.cryptogramType || '',
          terminal_id: cardData.terminalId,
          card_type: cardData.cardType || 'unknown'
        }
      );

      return {
        success: true,
        paymentIntent: paymentIntent.data,
        paymentMethod: tokenResult.paymentMethod
      };

    } catch (error) {
      console.error('EMV transaction processing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'EMV processing failed'
      };
    }
  }

  /**
   * Validate card data before tokenization
   */
  validateCardData(cardData: SecureCardData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!cardData.encryptedPan && !cardData.maskedPan) {
      errors.push('Card number is required');
    }

    if (!cardData.expiryDate || !/^\d{4}$/.test(cardData.expiryDate)) {
      errors.push('Valid expiry date is required (MMYY format)');
    }

    if (cardData.expiryDate) {
      const month = parseInt(cardData.expiryDate.substring(0, 2));
      const year = parseInt('20' + cardData.expiryDate.substring(2, 4));
      const now = new Date();
      const expiry = new Date(year, month - 1);
      
      if (month < 1 || month > 12) {
        errors.push('Invalid expiry month');
      }
      
      if (expiry < now) {
        errors.push('Card has expired');
      }
    }

    if (!cardData.terminalId) {
      errors.push('Terminal ID is required');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get supported card brands
   */
  getSupportedCardBrands(): string[] {
    return ['visa', 'mastercard', 'amex', 'discover'];
  }

  /**
   * Check if Stripe is ready
   */
  isReady(): boolean {
    return !!this.stripe;
  }
}
