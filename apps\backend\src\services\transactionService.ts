import Transaction, { ITransaction } from '../models/Transaction.mongo';
import { logger } from '../config/logger';
import { z } from 'zod';

const transactionLogger = logger.child({ module: 'transaction-service' });

const createTransactionSchema = z.object({
  amount: z.number().positive(),
  status: z.enum(['success', 'failure', 'pending']),
  protocolCode: z.string().optional(),
  stripePaymentIntentId: z.string().optional(),
  receiptUrl: z.string().url().optional(),
  metadata: z.record(z.any()).optional(),
});

export class TransactionService {
  async createTransaction(data: z.infer<typeof createTransactionSchema>): Promise<ITransaction> {
    try {
      const validatedData = createTransactionSchema.parse(data);
      const now = new Date().toISOString();

      transactionLogger.info({ amount: validatedData.amount, status: validatedData.status }, 'Creating transaction');

      const transaction = new Transaction({
        ...validatedData,
        createdAt: now,
        updatedAt: now,
      });

      await transaction.save();
      transactionLogger.info({ transactionId: transaction._id }, 'Transaction created successfully');

      return transaction;
    } catch (error) {
      if (error instanceof z.ZodError) {
        transactionLogger.error({ error: error.errors }, 'Invalid transaction data');
        throw new Error('Invalid transaction data');
      }
      transactionLogger.error({ error }, 'Failed to create transaction');
      throw new Error('Failed to create transaction');
    }
  }

  async getTransactions(limit: number = 50, offset: number = 0): Promise<ITransaction[]> {
    try {
      transactionLogger.info({ limit, offset }, 'Fetching transactions');

      const transactions = await Transaction
        .find()
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(offset)
        .lean();

      return transactions;
    } catch (error) {
      transactionLogger.error({ error }, 'Failed to fetch transactions');
      throw new Error('Failed to fetch transactions');
    }
  }

  async getTransactionById(id: string): Promise<ITransaction | null> {
    try {
      transactionLogger.info({ transactionId: id }, 'Fetching transaction by ID');

      const transaction = await Transaction.findById(id).lean();
      return transaction;
    } catch (error) {
      transactionLogger.error({ error, transactionId: id }, 'Failed to fetch transaction');
      throw new Error('Failed to fetch transaction');
    }
  }

  async updateTransaction(id: string, updates: Partial<ITransaction>): Promise<ITransaction | null> {
    try {
      transactionLogger.info({ transactionId: id }, 'Updating transaction');

      const transaction = await Transaction.findByIdAndUpdate(
        id,
        { ...updates, updatedAt: new Date().toISOString() },
        { new: true, runValidators: true }
      ).lean();

      if (transaction) {
        transactionLogger.info({ transactionId: id }, 'Transaction updated successfully');
      }

      return transaction;
    } catch (error) {
      transactionLogger.error({ error, transactionId: id }, 'Failed to update transaction');
      throw new Error('Failed to update transaction');
    }
  }
}

export const transactionService = new TransactionService();
