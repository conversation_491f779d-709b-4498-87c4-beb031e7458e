import Transaction from '../models/Transaction.mongo';

export const createTransaction = async (data: Partial<Record<string, any>>) => {
  const now = new Date().toISOString();
  const txn = new Transaction({
    ...data,
    createdAt: now,
    updatedAt: now,
  });
  await txn.save();
  return txn;
};

export const getTransactions = async () => {
  return Transaction.find().sort({ createdAt: -1 });
};
