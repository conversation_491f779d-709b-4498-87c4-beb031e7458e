import { FastifyRequest, FastifyReply } from 'fastify';
import { transactionService } from '../services/transactionService';
import { generateReceipt } from '../services/receiptService';
import { z } from 'zod';
import { logger } from '../config/logger';

const controllerLogger = logger.child({ module: 'transaction-controller' });

const createTransactionSchema = z.object({
  amount: z.number().positive(),
  status: z.enum(['success', 'failure', 'pending']),
  protocolCode: z.string().optional(),
  stripePaymentIntentId: z.string().optional(),
  receiptUrl: z.string().url().optional(),
  metadata: z.record(z.any()).optional(),
});

const listTransactionsSchema = z.object({
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(100)).optional(),
  offset: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(0)).optional(),
});

export const createTxn = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const validatedBody = createTransactionSchema.parse(request.body);
    controllerLogger.info({ amount: validatedBody.amount, status: validatedBody.status }, 'Creating transaction');

    const transaction = await transactionService.createTransaction(validatedBody);
    const receipt = generateReceipt(transaction);

    return reply.status(201).send({
      success: true,
      data: {
        transaction,
        receipt
      }
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      controllerLogger.warn({ error: error.errors }, 'Invalid transaction request');
      return reply.status(400).send({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid request parameters',
        details: error.errors
      });
    }

    controllerLogger.error({ error }, 'Failed to create transaction');
    return reply.status(500).send({
      success: false,
      error: 'TRANSACTION_CREATE_ERROR',
      message: 'Failed to create transaction'
    });
  }
};

export const listTxns = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const validatedQuery = listTransactionsSchema.parse(request.query);
    const limit = validatedQuery.limit || 50;
    const offset = validatedQuery.offset || 0;

    controllerLogger.info({ limit, offset }, 'Fetching transactions');

    const transactions = await transactionService.getTransactions(limit, offset);

    return reply.send({
      success: true,
      data: {
        transactions,
        pagination: {
          limit,
          offset,
          count: transactions.length
        }
      }
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      controllerLogger.warn({ error: error.errors }, 'Invalid list transactions request');
      return reply.status(400).send({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid query parameters',
        details: error.errors
      });
    }

    controllerLogger.error({ error }, 'Failed to fetch transactions');
    return reply.status(500).send({
      success: false,
      error: 'TRANSACTION_FETCH_ERROR',
      message: 'Failed to fetch transactions'
    });
  }
};

export const getTxnById = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { id } = request.params as { id: string };

    if (!id) {
      return reply.status(400).send({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Transaction ID is required'
      });
    }

    controllerLogger.info({ transactionId: id }, 'Fetching transaction by ID');

    const transaction = await transactionService.getTransactionById(id);

    if (!transaction) {
      return reply.status(404).send({
        success: false,
        error: 'NOT_FOUND',
        message: 'Transaction not found'
      });
    }

    return reply.send({
      success: true,
      data: transaction
    });
  } catch (error) {
    controllerLogger.error({ error }, 'Failed to fetch transaction by ID');
    return reply.status(500).send({
      success: false,
      error: 'TRANSACTION_FETCH_ERROR',
      message: 'Failed to fetch transaction'
    });
  }
};
