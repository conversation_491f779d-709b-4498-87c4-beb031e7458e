import { FastifyRequest, FastifyReply } from 'fastify';
import { createTransaction, getTransactions } from '../services/transactionService';
import { generateReceipt } from '../services/receiptService';

export const createTxn = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const txn = await createTransaction(request.body as any);
    const receipt = generateReceipt(txn);
    return reply.status(201).send({ status: true, data: txn, receipt });
  } catch (err: any) {
    return reply.status(500).send({ status: false, error: err.message });
  }
};

export const listTxns = async (_request: FastifyRequest, reply: FastifyReply) => {
  try {
    const txns = await getTransactions();
    return reply.send({ status: true, data: txns });
  } catch (err: any) {
    return reply.status(500).send({ status: false, error: err.message });
  }
};
