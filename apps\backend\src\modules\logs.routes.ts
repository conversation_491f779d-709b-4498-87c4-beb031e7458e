import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { SystemLog } from '../models/SystemLog';
import { CreateSystemLogRequest, ApiResponse, SystemLog as ISystemLog, PaginationParams } from 'shared-types';

interface GetLogsQuery extends PaginationParams {
  level?: string;
  category?: string;
  userId?: string;
  sessionId?: string;
  startDate?: string;
  endDate?: string;
}

export default async function logsRoutes(fastify: FastifyInstance) {
  // Create a new system log
  fastify.post('/logs', async (request: FastifyRequest<{ Body: CreateSystemLogRequest }>, reply: FastifyReply) => {
    try {
      const logData = request.body;
      
      const systemLog = new SystemLog({
        ...logData,
        timestamp: new Date().toISOString()
      });

      const savedLog = await systemLog.save();
      
      const response: ApiResponse<ISystemLog> = {
        success: true,
        data: {
          _id: savedLog._id.toString(),
          timestamp: savedLog.timestamp,
          level: savedLog.level,
          category: savedLog.category,
          message: savedLog.message,
          details: savedLog.details,
          userId: savedLog.userId,
          sessionId: savedLog.sessionId,
          createdAt: savedLog.createdAt.toISOString(),
          updatedAt: savedLog.updatedAt.toISOString()
        }
      };

      reply.status(201).send(response);
    } catch (error) {
      console.error('Error creating system log:', error);
      const response: ApiResponse = {
        success: false,
        message: 'Failed to create system log',
        error: {
          code: 'LOG_CREATE_ERROR',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      reply.status(500).send(response);
    }
  });

  // Get system logs with pagination and filtering
  fastify.get('/logs', async (request: FastifyRequest<{ Querystring: GetLogsQuery }>, reply: FastifyReply) => {
    try {
      const {
        limit = 50,
        offset = 0,
        level,
        category,
        userId,
        sessionId,
        startDate,
        endDate
      } = request.query;

      // Build filter query
      const filter: any = {};
      
      if (level) filter.level = level;
      if (category) filter.category = category;
      if (userId) filter.userId = userId;
      if (sessionId) filter.sessionId = sessionId;
      
      if (startDate || endDate) {
        filter.timestamp = {};
        if (startDate) filter.timestamp.$gte = new Date(startDate);
        if (endDate) filter.timestamp.$lte = new Date(endDate);
      }

      // Get total count
      const total = await SystemLog.countDocuments(filter);

      // Get logs with pagination
      const logs = await SystemLog.find(filter)
        .sort({ timestamp: -1 })
        .limit(Number(limit))
        .skip(Number(offset))
        .lean();

      const formattedLogs: ISystemLog[] = logs.map(log => ({
        _id: log._id.toString(),
        timestamp: log.timestamp,
        level: log.level,
        category: log.category,
        message: log.message,
        details: log.details,
        userId: log.userId,
        sessionId: log.sessionId,
        createdAt: log.createdAt.toISOString(),
        updatedAt: log.updatedAt.toISOString()
      }));

      const response: ApiResponse<{
        logs: ISystemLog[];
        pagination: {
          limit: number;
          offset: number;
          count: number;
          total: number;
        };
      }> = {
        success: true,
        data: {
          logs: formattedLogs,
          pagination: {
            limit: Number(limit),
            offset: Number(offset),
            count: logs.length,
            total
          }
        }
      };

      reply.send(response);
    } catch (error) {
      console.error('Error fetching system logs:', error);
      const response: ApiResponse = {
        success: false,
        message: 'Failed to fetch system logs',
        error: {
          code: 'LOG_FETCH_ERROR',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      reply.status(500).send(response);
    }
  });

  // Get log by ID
  fastify.get('/logs/:id', async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      const log = await SystemLog.findById(id).lean();
      
      if (!log) {
        const response: ApiResponse = {
          success: false,
          message: 'System log not found'
        };
        return reply.status(404).send(response);
      }

      const formattedLog: ISystemLog = {
        _id: log._id.toString(),
        timestamp: log.timestamp,
        level: log.level,
        category: log.category,
        message: log.message,
        details: log.details,
        userId: log.userId,
        sessionId: log.sessionId,
        createdAt: log.createdAt.toISOString(),
        updatedAt: log.updatedAt.toISOString()
      };

      const response: ApiResponse<ISystemLog> = {
        success: true,
        data: formattedLog
      };

      reply.send(response);
    } catch (error) {
      console.error('Error fetching system log:', error);
      const response: ApiResponse = {
        success: false,
        message: 'Failed to fetch system log',
        error: {
          code: 'LOG_FETCH_ERROR',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      reply.status(500).send(response);
    }
  });

  // Delete logs older than specified date
  fastify.delete('/logs/cleanup', async (request: FastifyRequest<{ Querystring: { beforeDate?: string } }>, reply: FastifyReply) => {
    try {
      const { beforeDate } = request.query;
      
      if (!beforeDate) {
        const response: ApiResponse = {
          success: false,
          message: 'beforeDate parameter is required'
        };
        return reply.status(400).send(response);
      }

      const result = await SystemLog.deleteMany({
        timestamp: { $lt: new Date(beforeDate) }
      });

      const response: ApiResponse<{ deletedCount: number }> = {
        success: true,
        data: { deletedCount: result.deletedCount },
        message: `Deleted ${result.deletedCount} log entries`
      };

      reply.send(response);
    } catch (error) {
      console.error('Error cleaning up logs:', error);
      const response: ApiResponse = {
        success: false,
        message: 'Failed to cleanup logs',
        error: {
          code: 'LOG_CLEANUP_ERROR',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      reply.status(500).send(response);
    }
  });
}
