{
  "name": "mock-bank",
  "version": "0.1.0",
  "private": true,
  "main": "dist/index.js",
  "scripts": {
    "build": "tsc -b",
    "dev": "tsc-watch --onSuccess \"node dist/index.js\"",
    "start": "node dist/index.js",
    "lint": "eslint . --ext .ts"
  },
  "dependencies": {
    // To be added: express or fastify, body-parser (if express)
    "shared-types": "workspace:*" // To understand incoming messages
  },
  "devDependencies": {
    "typescript": "^5.4.5",
    "tsc-watch": "^6.2.0",
    "eslint": "^8.57.0",
    "eslint-config-custom": "workspace:*",
    "tsconfig-custom": "workspace:*",
    "@types/node": "^20.12.7"
  }
}
