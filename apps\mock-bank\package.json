{"name": "mock-bank", "version": "0.1.0", "private": true, "main": "dist/index.js", "scripts": {"build": "tsc -b", "dev": "tsc-watch --onSuccess \"node dist/index.js\"", "start": "node dist/index.js", "lint": "eslint . --ext .ts"}, "dependencies": {"express": "^4.19.2", "cors": "^2.8.5"}, "devDependencies": {"typescript": "^5.4.5", "tsc-watch": "^6.2.0", "eslint": "^8.57.0", "@types/node": "^20.12.7", "@types/express": "^4.17.21", "@types/cors": "^2.8.17"}}