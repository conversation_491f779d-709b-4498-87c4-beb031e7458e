/**
 * PAX Neptune SDK Wrapper/Bridge
 * 
 * This service provides a TypeScript wrapper around the PAX Neptune SDK
 * for hardware integration on PAX A920 Pro and other PAX terminals.
 * 
 * Based on research:
 * - PAX Neptune SDK (NeptuneLiteApi/NeptuneDiamondApi)
 * - Hardware access for card reader, printer, camera, GPS, etc.
 * - Android-based integration for PAX terminals
 */

export interface PAXHardwareConfig {
  terminalId: string;
  merchantId: string;
  enableDebugMode?: boolean;
  timeout?: number;
}

export interface CardData {
  track1?: string;
  track2?: string;
  track3?: string;
  pan?: string;
  expiryDate?: string;
  cardholderName?: string;
  serviceCode?: string;
  discretionaryData?: string;
  emvData?: Record<string, string>;
  contactless?: boolean;
}

export interface PrinterConfig {
  fontSize?: 'small' | 'medium' | 'large';
  alignment?: 'left' | 'center' | 'right';
  bold?: boolean;
  underline?: boolean;
}

export interface ReceiptLine {
  text: string;
  config?: PrinterConfig;
}

export interface PAXTerminalInfo {
  model: string;
  serialNumber: string;
  firmwareVersion: string;
  batteryLevel?: number;
  networkStatus?: 'connected' | 'disconnected';
}

// Declare global interfaces for PAX Neptune SDK
declare global {
  interface Window {
    // PAX Neptune SDK interfaces
    NeptuneLiteUser?: {
      getInstance(): any;
    };
    NeptuneDiamondUser?: {
      getInstance(): any;
    };
    // Android WebView interface
    Android?: {
      // Card reader functions
      startCardReading(callback: string): void;
      stopCardReading(): void;

      // Printer functions
      printText(text: string, config?: string): boolean;
      printReceipt(lines: string): boolean;

      // Hardware info
      getTerminalInfo(): string;
      getBatteryLevel(): number;
      isHardwareAvailable(): boolean;
      getHardwareStatus(): string;

      // Audio/Beep
      beep(duration: number): void;

      // Camera
      takePicture(callback: string): void;

      // GPS
      getLocation(callback: string): void;

      // Utility
      log(level: string, message: string): void;
    };

    // PAX Terminal detection
    PAXTerminal?: boolean;
    PAXModel?: string;
  }
}

export class PAXNeptuneSDK {
  private config: PAXHardwareConfig;
  private neptuneInstance: any = null;
  private isInitialized = false;
  private sessionId: string;

  constructor(config: PAXHardwareConfig) {
    this.config = config;
    this.sessionId = `pax_session_${Date.now()}`;
  }

  /**
   * Initialize the PAX Neptune SDK
   */
  async initialize(): Promise<boolean> {
    try {
      // Check for Android WebView bridge first (most common for web apps)
      if (window.Android && window.Android.isHardwareAvailable) {
        const isAvailable = window.Android.isHardwareAvailable();
        if (isAvailable) {
          this.isInitialized = true;
          this.log('info', 'PAX Android WebView bridge initialized successfully');
          return true;
        }
      }

      // Try to initialize Neptune SDK directly
      if (window.NeptuneLiteUser) {
        this.neptuneInstance = window.NeptuneLiteUser.getInstance();
        this.isInitialized = true;
        this.log('info', 'Neptune SDK initialized successfully');
        return true;
      }

      // Fallback to Android WebView interface (basic check)
      if (window.Android) {
        this.isInitialized = true;
        this.log('info', 'Android WebView interface available (basic mode)');
        return true;
      }

      this.log('warn', 'PAX hardware not available - running in simulation mode');
      return false;
    } catch (error) {
      this.log('error', 'Failed to initialize PAX SDK', { error });
      return false;
    }
  }

  /**
   * Check if hardware is available
   */
  isHardwareAvailable(): boolean {
    if (!this.isInitialized) return false;

    // Check Android WebView bridge
    if (window.Android?.isHardwareAvailable) {
      return window.Android.isHardwareAvailable();
    }

    // Check basic Android interface
    if (window.Android) return true;

    // Check Neptune SDK
    if (this.neptuneInstance) return true;

    return false;
  }

  /**
   * Get terminal information
   */
  async getTerminalInfo(): Promise<PAXTerminalInfo | null> {
    try {
      // Try Android WebView bridge first
      if (window.Android?.getTerminalInfo) {
        const infoStr = window.Android.getTerminalInfo();
        const info = JSON.parse(infoStr);
        return {
          model: info.model || 'PAX A920 Pro',
          serialNumber: info.serialNumber || this.config.terminalId,
          firmwareVersion: info.firmwareVersion || '1.0.0',
          batteryLevel: window.Android.getBatteryLevel?.() || undefined,
          networkStatus: info.networkStatus || 'connected'
        };
      }

      // Check if we're in PAX terminal environment
      if (window.PAXTerminal || window.PAXModel) {
        return {
          model: (window as any).PAXModel || 'PAX A920 Pro',
          serialNumber: this.config.terminalId,
          firmwareVersion: '1.0.0',
          batteryLevel: 85,
          networkStatus: 'connected'
        };
      }

      // Fallback simulation data
      return {
        model: 'PAX A920 Pro (Simulated)',
        serialNumber: this.config.terminalId,
        firmwareVersion: '1.0.0-sim',
        batteryLevel: 85,
        networkStatus: 'connected'
      };
    } catch (error) {
      this.log('error', 'Failed to get terminal info', { error });
      return null;
    }
  }

  /**
   * Start card reading
   */
  async startCardReading(
    onSuccess: (cardData: CardData) => void,
    onError: (error: string) => void,
    timeout: number = 30000
  ): Promise<void> {
    try {
      this.log('info', 'Starting card reading', { timeout });

      if (window.Android?.startCardReading) {
        // Create global callback functions
        (window as any).paxCardReadSuccess = (dataStr: string) => {
          try {
            const cardData = JSON.parse(dataStr);
            this.log('info', 'Card read successful');
            onSuccess(cardData);
          } catch (error) {
            this.log('error', 'Failed to parse card data', { error });
            onError('Invalid card data received');
          }
        };

        (window as any).paxCardReadError = (errorStr: string) => {
          this.log('error', 'Card reading failed', { error: errorStr });
          onError(errorStr);
        };

        window.Android.startCardReading('paxCardReadSuccess');
        
        // Set timeout
        setTimeout(() => {
          if (window.Android?.stopCardReading) {
            window.Android.stopCardReading();
          }
          onError('Card reading timeout');
        }, timeout);
        
      } else {
        // Simulation mode - auto-generate test card data after delay
        setTimeout(() => {
          const simulatedCard: CardData = {
            pan: '****************',
            expiryDate: '1225',
            cardholderName: 'TEST CARD',
            track2: '****************=25121010000000000000',
            contactless: Math.random() > 0.5
          };
          this.log('info', 'Simulated card read successful');
          onSuccess(simulatedCard);
        }, 2000);
      }
    } catch (error) {
      this.log('error', 'Failed to start card reading', { error });
      onError(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Stop card reading
   */
  stopCardReading(): void {
    try {
      if (window.Android?.stopCardReading) {
        window.Android.stopCardReading();
      }
      this.log('info', 'Card reading stopped');
    } catch (error) {
      this.log('error', 'Failed to stop card reading', { error });
    }
  }

  /**
   * Print receipt
   */
  async printReceipt(lines: ReceiptLine[]): Promise<boolean> {
    try {
      this.log('info', 'Printing receipt', { lineCount: lines.length });

      if (window.Android?.printReceipt) {
        const receiptData = JSON.stringify(lines);
        const result = window.Android.printReceipt(receiptData);
        this.log('info', 'Receipt printed', { success: result });
        return result;
      }
      
      // Fallback: browser print
      this.printReceiptFallback(lines);
      return true;
    } catch (error) {
      this.log('error', 'Failed to print receipt', { error });
      return false;
    }
  }

  /**
   * Print text with configuration
   */
  async printText(text: string, config?: PrinterConfig): Promise<boolean> {
    try {
      if (window.Android?.printText) {
        const configStr = config ? JSON.stringify(config) : undefined;
        return window.Android.printText(text, configStr);
      }
      
      // Fallback
      console.log('Print Text:', text, config);
      return true;
    } catch (error) {
      this.log('error', 'Failed to print text', { error });
      return false;
    }
  }

  /**
   * Play beep sound
   */
  beep(duration: number = 200): void {
    try {
      if (window.Android?.beep) {
        window.Android.beep(duration);
      } else {
        // Fallback: try to use Web Audio API or console
        console.log(`Beep: ${duration}ms`);
      }
    } catch (error) {
      this.log('error', 'Failed to beep', { error });
    }
  }

  /**
   * Take picture using camera
   */
  async takePicture(): Promise<string | null> {
    return new Promise((resolve, reject) => {
      try {
        if (window.Android?.takePicture) {
          (window as any).paxPictureCallback = (imageData: string) => {
            this.log('info', 'Picture taken successfully');
            resolve(imageData);
          };
          
          window.Android.takePicture('paxPictureCallback');
          
          // Timeout after 30 seconds
          setTimeout(() => {
            reject(new Error('Picture taking timeout'));
          }, 30000);
        } else {
          reject(new Error('Camera not available'));
        }
      } catch (error) {
        this.log('error', 'Failed to take picture', { error });
        reject(error);
      }
    });
  }

  /**
   * Get GPS location
   */
  async getLocation(): Promise<{ latitude: number; longitude: number } | null> {
    return new Promise((resolve, reject) => {
      try {
        if (window.Android?.getLocation) {
          (window as any).paxLocationCallback = (locationStr: string) => {
            try {
              const location = JSON.parse(locationStr);
              this.log('info', 'Location retrieved successfully');
              resolve(location);
            } catch (error) {
              reject(new Error('Invalid location data'));
            }
          };
          
          window.Android.getLocation('paxLocationCallback');
          
          // Timeout after 15 seconds
          setTimeout(() => {
            reject(new Error('Location timeout'));
          }, 15000);
        } else {
          reject(new Error('GPS not available'));
        }
      } catch (error) {
        this.log('error', 'Failed to get location', { error });
        reject(error);
      }
    });
  }

  /**
   * Fallback receipt printing using browser
   */
  private printReceiptFallback(lines: ReceiptLine[]): void {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const html = `
        <html>
          <head>
            <title>Receipt</title>
            <style>
              body { font-family: monospace; font-size: 12px; margin: 20px; }
              .center { text-align: center; }
              .right { text-align: right; }
              .bold { font-weight: bold; }
              .underline { text-decoration: underline; }
              .small { font-size: 10px; }
              .large { font-size: 14px; }
            </style>
          </head>
          <body>
            ${lines.map(line => {
              const classes = [];
              if (line.config?.alignment) classes.push(line.config.alignment);
              if (line.config?.bold) classes.push('bold');
              if (line.config?.underline) classes.push('underline');
              if (line.config?.fontSize) classes.push(line.config.fontSize);
              
              return `<div class="${classes.join(' ')}">${line.text}</div>`;
            }).join('')}
          </body>
        </html>
      `;
      
      printWindow.document.write(html);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();
    }
  }

  /**
   * Log system events
   */
  private log(level: 'info' | 'warn' | 'error' | 'debug', message: string, details?: any): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      category: 'hardware' as const,
      message: `[PAX SDK] ${message}`,
      details: {
        sessionId: this.sessionId,
        terminalId: this.config.terminalId,
        ...details
      }
    };

    // Log to console
    console[level](`[PAX SDK] ${message}`, details);

    // Send to system logs if available
    if (typeof window !== 'undefined' && (window as any).appStore?.addSystemLog) {
      (window as any).appStore.addSystemLog(logEntry);
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    try {
      this.stopCardReading();
      this.isInitialized = false;
      this.neptuneInstance = null;
      this.log('info', 'PAX SDK destroyed');
    } catch (error) {
      this.log('error', 'Failed to destroy PAX SDK', { error });
    }
  }
}
