import axios from 'axios';
import { env } from '../config/env';
import { logger } from '../config/logger';
import { z } from 'zod';

const protocolLogger = logger.child({ module: 'protocol-service' });

const protocolMessageSchema = z.object({
  protocolEventCode: z.enum(['101.1', '101.3', '101.5', '101.8']),
  transactionId: z.string(),
  stripePaymentIntentId: z.string().optional(),
  amount: z.number().positive(),
  metadata: z.record(z.any()).optional(),
});

export interface ProtocolMessage {
  protocolEventCode: '101.1' | '101.3' | '101.5' | '101.8';
  mti: string;
  transactionId: string;
  stripePaymentIntentId?: string;
  timestamp: string;
  de2_pan?: string;
  de3_processingCode?: string;
  de4_transactionAmount?: number;
  de11_stan?: string;
  de12_localTime?: string;
  de13_localDate?: string;
  de18_merchantCategoryCode?: string;
  de37_retrievalReferenceNumber?: string;
  de38_approvalCode?: string;
  de39_responseCode?: string;
  de41_cardAcceptorTerminalId?: string;
  de42_cardAcceptorIdCode?: string;
}

export class ProtocolService {
  private generateSTAN(): string {
    return Math.floor(Math.random() * 999999).toString().padStart(6, '0');
  }

  private generateRRN(): string {
    return Math.floor(Math.random() * 999999999999).toString().padStart(12, '0');
  }

  private formatTime(): string {
    const now = new Date();
    return now.toTimeString().slice(0, 8).replace(/:/g, '');
  }

  private formatDate(): string {
    const now = new Date();
    return (now.getMonth() + 1).toString().padStart(2, '0') +
           now.getDate().toString().padStart(2, '0');
  }

  private createProtocolMessage(params: z.infer<typeof protocolMessageSchema>): ProtocolMessage {
    const now = new Date();

    const baseMessage: ProtocolMessage = {
      protocolEventCode: params.protocolEventCode,
      mti: this.getMTIForProtocol(params.protocolEventCode),
      transactionId: params.transactionId,
      stripePaymentIntentId: params.stripePaymentIntentId,
      timestamp: now.toISOString(),
      de4_transactionAmount: params.amount,
      de11_stan: this.generateSTAN(),
      de12_localTime: this.formatTime(),
      de13_localDate: this.formatDate(),
      de18_merchantCategoryCode: '5999',
      de37_retrievalReferenceNumber: this.generateRRN(),
      de41_cardAcceptorTerminalId: 'PAX920PRO',
      de42_cardAcceptorIdCode: 'MERCHANT001',
    };

    switch (params.protocolEventCode) {
      case '101.1':
        baseMessage.de3_processingCode = '000000';
        break;
      case '101.3':
        baseMessage.de3_processingCode = '000000';
        baseMessage.de38_approvalCode = Math.floor(Math.random() * 999999).toString().padStart(6, '0');
        break;
      case '101.5':
        baseMessage.de3_processingCode = '200000';
        break;
      case '101.8':
        baseMessage.de3_processingCode = '920000';
        break;
    }

    return baseMessage;
  }

  private getMTIForProtocol(protocolCode: string): string {
    switch (protocolCode) {
      case '101.1': return '0100';
      case '101.3': return '0220';
      case '101.5': return '0400';
      case '101.8': return '0500';
      default: return '0200';
    }
  }

  async sendProtocolMessage(params: z.infer<typeof protocolMessageSchema>): Promise<any> {
    try {
      const validatedParams = protocolMessageSchema.parse(params);

      protocolLogger.info({
        protocolCode: validatedParams.protocolEventCode,
        transactionId: validatedParams.transactionId
      }, 'Sending protocol message to bank');

      const message = this.createProtocolMessage(validatedParams);

      const response = await axios.post(env.MOCK_BANK_URL, message, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'X-Protocol-Version': '1.0',
        },
      });

      protocolLogger.info({
        protocolCode: validatedParams.protocolEventCode,
        transactionId: validatedParams.transactionId,
        bankResponseCode: response.data?.data?.bankResponseCode
      }, 'Received response from bank');

      return response.data;
    } catch (error) {
      if (error instanceof z.ZodError) {
        protocolLogger.error({ error: error.errors }, 'Invalid protocol message parameters');
        throw new Error('Invalid protocol message parameters');
      }

      if (axios.isAxiosError(error)) {
        protocolLogger.error({
          error: error.message,
          status: error.response?.status,
          data: error.response?.data
        }, 'Bank communication error');
        throw new Error('Failed to communicate with bank');
      }

      protocolLogger.error({ error }, 'Unexpected error sending protocol message');
      throw new Error('Failed to send protocol message');
    }
  }

  getProtocolDescription(code: string): string {
    switch (code) {
      case '101.1': return 'Authorization Request';
      case '101.3': return 'Capture Request';
      case '101.5': return 'Reversal Request';
      case '101.8': return 'Settlement Request';
      default: return 'Unknown Protocol';
    }
  }
}

export const protocolService = new ProtocolService();
