import { ProtocolMessage } from '../models/ProtocolMessage';

export const buildProtocolMessage = (protocol: string, data: Partial<ProtocolMessage>): ProtocolMessage => {
  const now = new Date().toISOString();
  return {
    protocol,
    mti: data.mti || '',
    amount: data.amount || 0,
    pan: data.pan,
    stan: data.stan || Math.floor(Math.random() * 1000000).toString(),
    authCode: data.authCode,
    origStan: data.origStan,
    timestamp: now,
  };
};
