{
  "name": "shared-types",
  "version": "0.1.0",
  "private": true,
  "main": "./dist/index.js", // Points to the compiled output
  "types": "./dist/index.d.ts", // Points to the type declarations
  "scripts": {
    "build": "tsc -b", // -b uses the tsconfig.json references for building
    "lint": "eslint . --ext .ts",
    "dev": "tsc -w" // Watch mode
  },
  "devDependencies": {
    "typescript": "^5.4.5",
    "eslint": "^8.57.0",
    "eslint-config-custom": "workspace:*" // Use shared ESLint config from workspace
  }
}
