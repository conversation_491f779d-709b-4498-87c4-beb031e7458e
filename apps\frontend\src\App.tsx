
import { useState } from 'react';
import { PaymentSimulator } from './components/PaymentSimulator';
import { ProtocolTrigger } from './components/ProtocolTrigger';
import { LogViewer } from './components/LogViewer';
import { TransactionList } from './components/TransactionList';
import { Dashboard } from './components/Dashboard';

type ActiveView = 'dashboard' | 'payment' | 'protocol' | 'transactions' | 'logs';

function App() {
  const [activeView, setActiveView] = useState<ActiveView>('dashboard');

  const navigationItems = [
    { id: 'dashboard' as const, label: 'Dashboard', icon: '🏠' },
    { id: 'payment' as const, label: 'Payment', icon: '💳' },
    { id: 'protocol' as const, label: 'Protocol', icon: '🔄' },
    { id: 'transactions' as const, label: 'Transactions', icon: '📊' },
    { id: 'logs' as const, label: 'Logs', icon: '📝' },
  ];

  const renderActiveView = () => {
    switch (activeView) {
      case 'dashboard':
        return <Dashboard onNavigate={setActiveView} />;
      case 'payment':
        return <PaymentSimulator />;
      case 'protocol':
        return <ProtocolTrigger />;
      case 'transactions':
        return <TransactionList />;
      case 'logs':
        return <LogViewer />;
      default:
        return <Dashboard onNavigate={setActiveView} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 font-['Poppins']">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">P</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-slate-900">PAX A920 Pro</h1>
                <p className="text-sm text-slate-500">POS Terminal System</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-slate-600">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Online</span>
              </div>
              <div className="text-sm text-slate-500">
                {new Date().toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar Navigation */}
        <nav className="w-64 bg-white shadow-sm border-r border-slate-200">
          <div className="p-6">
            <div className="space-y-2">
              {navigationItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveView(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 ${
                    activeView === item.id
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                      : 'text-slate-600 hover:bg-slate-50 hover:text-slate-900'
                  }`}
                >
                  <span className="text-xl">{item.icon}</span>
                  <span className="font-medium">{item.label}</span>
                </button>
              ))}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 overflow-auto">
          <div className="p-6">
            {renderActiveView()}
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;
