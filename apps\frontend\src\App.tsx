import React from 'react';
import { PaymentSimulator } from './components/PaymentSimulator';
import { ProtocolTrigger } from './components/ProtocolTrigger';
import { LogViewer } from './components/LogViewer';
import { TransactionList } from './components/TransactionList';

function App() {
  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <header className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">PAX A920 Pro POS Terminal</h1>
          <p className="text-gray-600">Stripe Payment Integration with Protocol Simulation</p>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <PaymentSimulator />
          <ProtocolTrigger />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <TransactionList />
          <LogViewer />
        </div>
      </div>
    </div>
  );
}

export default App;
