import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000, // Default frontend port, backend is on 3001
    proxy: {
      // Proxy API requests to the backend during development
      // This avoids CORS issues and mimics a production setup where
      // frontend and backend might be served from the same domain.
      '/api': {
        target: 'http://localhost:3001', // Your backend server URL
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api/, ''), // if backend doesn't have /api prefix
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true, // Generate source maps for production builds
  },
  // optimizeDeps: {
  //   include: ['shared-types'], // If shared-types is a local package, Vite might need this hint
  // },
});
