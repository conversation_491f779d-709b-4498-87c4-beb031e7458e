# 🚀 PAX A920 Pro POS Terminal - Production Deployment Guide

## 📋 **Pre-Deployment Checklist**

### **1. Stripe Account Setup**
```bash
# Required Stripe Configuration
✅ Live Stripe account with business verification
✅ Live API keys (secret and publishable)
✅ Webhook endpoints configured
✅ PCI compliance documentation
✅ Bank account connected for payouts
```

### **2. Environment Configuration**
```bash
# Production Environment Variables (.env.production)
NODE_ENV=production

# Database (MongoDB Atlas recommended for production)
MONGO_URI="mongodb+srv://username:<EMAIL>/pax_pos_prod?retryWrites=true&w=majority"

# Stripe Live Keys (NEVER use test keys in production)
STRIPE_SECRET_KEY="sk_live_your_actual_live_secret_key"
STRIPE_PUBLISHABLE_KEY="pk_live_your_actual_live_publishable_key"
STRIPE_WEBHOOK_SECRET="whsec_your_actual_webhook_secret"

# Security
JWT_SECRET="your_super_secure_256_bit_jwt_secret_for_production"
ENCRYPTION_KEY="your_256_bit_encryption_key_for_sensitive_data"

# CORS (your actual domain)
CORS_ORIGIN="https://your-pos-domain.com"

# Rate Limiting (production values)
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# Logging
LOG_LEVEL=warn
AUDIT_LOG_ENABLED=true

# SSL/TLS
SSL_CERT_PATH="/etc/ssl/certs/your-domain.crt"
SSL_KEY_PATH="/etc/ssl/private/your-domain.key"

# Terminal Configuration
TERMINAL_ID="PAX_A920_001"
MERCHANT_ID="your_merchant_id"
STORE_ID="your_store_id"
```

## 🏗️ **Production Build Process**

### **1. Build All Services**
```bash
# Clean and build everything
npm run clean
npm install --production
npm run build:all

# Verify builds
ls -la apps/backend/dist/
ls -la apps/frontend/dist/
ls -la apps/mock-bank/dist/
```

### **2. Docker Production Build**
```dockerfile
# Dockerfile.production
FROM node:18-alpine AS builder

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache dumb-init

# Create app user
RUN addgroup -g 1001 -S pax-user && \
    adduser -S pax-user -u 1001

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY apps/backend/package*.json ./apps/backend/
COPY apps/frontend/package*.json ./apps/frontend/

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build applications
RUN npm run build:all

# Production stage
FROM node:18-alpine AS production

RUN apk update && apk upgrade && apk add --no-cache dumb-init

RUN addgroup -g 1001 -S pax-user && \
    adduser -S pax-user -u 1001

WORKDIR /app

# Copy built applications
COPY --from=builder --chown=pax-user:pax-user /app/apps/backend/dist ./backend/
COPY --from=builder --chown=pax-user:pax-user /app/apps/frontend/dist ./frontend/
COPY --from=builder --chown=pax-user:pax-user /app/node_modules ./node_modules/

# Security: Run as non-root user
USER pax-user

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node backend/healthcheck.js

EXPOSE 3001

ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "backend/server.js"]
```

### **3. Build Docker Image**
```bash
# Build production image
docker build -f Dockerfile.production -t pax-pos:latest .

# Tag for registry
docker tag pax-pos:latest your-registry.com/pax-pos:v1.0.0

# Push to registry
docker push your-registry.com/pax-pos:v1.0.0
```

## 🔧 **Terminal Hardware Setup**

### **1. PAX A920 Pro Configuration**
```bash
# Terminal Settings
Network: WiFi/Ethernet configured
Display: 1280x720 resolution
Printer: Thermal printer enabled
Card Reader: EMV + NFC enabled
Security: Tamper detection enabled

# Install Android APK
adb install pax-pos-terminal.apk

# Configure terminal ID
echo "PAX_A920_001" > /data/terminal_id
```

### **2. Kiosk Mode Setup**
```bash
# Android Kiosk Configuration
# Set as device owner
dpm set-device-owner com.paxpos.terminal/.DeviceAdminReceiver

# Lock to single app
am start -n com.paxpos.terminal/.MainActivity
settings put global policy_control immersive.full=com.paxpos.terminal
```

## 🌐 **Production Deployment Options**

### **Option 1: Cloud Deployment (Recommended)**

#### **AWS ECS Deployment**
```yaml
# ecs-task-definition.json
{
  "family": "pax-pos-terminal",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "pax-pos",
      "image": "your-registry.com/pax-pos:v1.0.0",
      "portMappings": [
        {
          "containerPort": 3001,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "STRIPE_SECRET_KEY",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:stripe-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/pax-pos",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### **Load Balancer Configuration**
```yaml
# Application Load Balancer
Type: Application Load Balancer
Scheme: Internet-facing
Security Groups: HTTPS only (443)
Target Groups: ECS service
Health Check: /health endpoint

# SSL Certificate
Certificate: AWS Certificate Manager
Domain: pos.yourdomain.com
```

### **Option 2: On-Premise Deployment**

#### **Docker Compose Production**
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  pax-pos:
    image: pax-pos:latest
    restart: unless-stopped
    ports:
      - "443:3001"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    volumes:
      - ./logs:/app/logs
      - ./ssl:/app/ssl:ro
    networks:
      - pax-network
    depends_on:
      - mongodb
      - redis

  mongodb:
    image: mongo:6.0
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
    volumes:
      - mongodb_data:/data/db
    networks:
      - pax-network

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - pax-network

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl:ro
    depends_on:
      - pax-pos
    networks:
      - pax-network

volumes:
  mongodb_data:
  redis_data:

networks:
  pax-network:
    driver: bridge
```

## 🔒 **Security Hardening**

### **1. SSL/TLS Configuration**
```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name pos.yourdomain.com;

    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    ssl_protocols TLSv1.3 TLSv1.2;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_prefer_server_ciphers off;
    
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    
    location / {
        proxy_pass http://pax-pos:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### **2. Firewall Configuration**
```bash
# UFW Firewall Rules
ufw default deny incoming
ufw default allow outgoing
ufw allow 22/tcp    # SSH (restrict to specific IPs)
ufw allow 80/tcp    # HTTP (redirect to HTTPS)
ufw allow 443/tcp   # HTTPS
ufw enable
```

## 📊 **Monitoring & Logging**

### **1. Application Monitoring**
```yaml
# Prometheus monitoring
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=secure_password
```

### **2. Log Management**
```bash
# Centralized logging with ELK Stack
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -e "discovery.type=single-node" \
  elasticsearch:7.17.0

docker run -d \
  --name kibana \
  -p 5601:5601 \
  --link elasticsearch:elasticsearch \
  kibana:7.17.0
```

## 🚀 **Deployment Commands**

### **1. Production Deployment**
```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 Starting PAX POS Terminal Deployment..."

# Pull latest code
git pull origin main

# Build and test
npm run build:all
npm run test

# Build Docker image
docker build -f Dockerfile.production -t pax-pos:latest .

# Stop existing containers
docker-compose -f docker-compose.prod.yml down

# Start new deployment
docker-compose -f docker-compose.prod.yml up -d

# Health check
sleep 30
curl -f http://localhost/health || exit 1

echo "✅ Deployment completed successfully!"
```

### **2. Rollback Script**
```bash
#!/bin/bash
# rollback.sh

echo "🔄 Rolling back to previous version..."

# Stop current deployment
docker-compose -f docker-compose.prod.yml down

# Restore previous image
docker tag pax-pos:previous pax-pos:latest

# Start previous version
docker-compose -f docker-compose.prod.yml up -d

echo "✅ Rollback completed!"
```

## 📱 **Terminal App Installation**

### **1. Android APK Build**
```bash
# Build Android APK for PAX A920 Pro
cd mobile-app
./gradlew assembleRelease

# Sign APK
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 \
  -keystore release-key.keystore \
  app-release-unsigned.apk \
  alias_name

# Install on terminal
adb install app-release.apk
```

### **2. Terminal Configuration**
```bash
# Configure terminal for production
adb shell settings put global development_settings_enabled 0
adb shell settings put global adb_enabled 0
adb shell am start -n com.paxpos.terminal/.MainActivity
```

This deployment guide ensures your PAX A920 Pro POS terminal is production-ready, secure, and capable of processing real money transactions with full PCI compliance.
