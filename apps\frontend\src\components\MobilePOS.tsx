import { useState, useEffect, useRef } from 'react';
import { useAppStore } from '../store/useAppStore';
import { paxTerminal, CardData } from '../services/paxTerminal';

type TransactionState = 
  | 'idle'
  | 'amount-entry'
  | 'waiting-card'
  | 'card-reading'
  | 'authorizing'
  | 'approved'
  | 'declined'
  | 'printing'
  | 'complete'
  | 'error';

// CardData interface imported from paxTerminal service

interface RealTimeTransaction {
  id: string;
  amount: number;
  status: TransactionState;
  paymentIntentId?: string;
  cardData?: CardData;
  authCode?: string;
  receiptData?: string;
  errorMessage?: string;
  timestamp: Date;
}

export function MobilePOS() {
  const [transaction, setTransaction] = useState<RealTimeTransaction>({
    id: '',
    amount: 0,
    status: 'idle',
    timestamp: new Date()
  });

  const [amountInput, setAmountInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [manualCardData, setManualCardData] = useState({
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    cardholderName: ''
  });

  const intervalRef = useRef<NodeJS.Timeout>();
  const { simulatePayment, isLoading } = useAppStore();

  // Check if PAX terminal hardware is available
  const isTerminalAvailable = () => {
    return paxTerminal.isTerminalAvailable();
  };

  // Initialize card reader
  useEffect(() => {
    if (transaction.status === 'waiting-card' && isTerminalAvailable()) {
      startCardReading();
    }

    return () => {
      paxTerminal.stopCardReading();
    };
  }, [transaction.status]);

  // Countdown timer for card reading
  useEffect(() => {
    if (transaction.status === 'waiting-card') {
      setCountdown(30);
      intervalRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            setTransaction(prev => ({
              ...prev,
              status: 'error',
              errorMessage: 'Card reading timeout - please try again'
            }));
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [transaction.status]);

  const startCardReading = async () => {
    try {
      await paxTerminal.startCardReading(
        (cardData: CardData) => {
          // Success callback
          paxTerminal.beep(200); // Success beep
          setTransaction(prev => ({
            ...prev,
            status: 'card-reading',
            cardData
          }));

          // Simulate card reading delay
          setTimeout(() => {
            processCardData(cardData);
          }, 2000);
        },
        (error: string) => {
          // Error callback
          paxTerminal.beep(500); // Error beep
          setTransaction(prev => ({
            ...prev,
            status: 'error',
            errorMessage: `Card reading failed: ${error}`
          }));
        }
      );
    } catch (error) {
      console.warn('PAX Terminal not available, showing manual entry option');
      setShowManualEntry(true);
    }
  };

  const processCardData = async (cardData: CardData) => {
    setTransaction(prev => ({ ...prev, status: 'authorizing' }));

    try {
      // Process payment with card data
      const response = await fetch('/api/v1/payments/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: transaction.amount,
          currency: 'usd',
          payment_method_data: {
            type: 'card',
            card: {
              number: cardData.pan?.replace(/\D/g, '').slice(-4) || '****',
              exp_month: cardData.expiryDate?.slice(2, 4) || '12',
              exp_year: cardData.expiryDate?.slice(0, 2) || '25',
            }
          },
          confirm: true,
          metadata: {
            card_type: cardData.cardType,
            terminal_id: 'PAX_A920_001'
          }
        }),
      });

      const paymentResult = await response.json();

      if (paymentResult.success && paymentResult.data.status === 'succeeded') {
        setTransaction(prev => ({
          ...prev,
          status: 'approved',
          paymentIntentId: paymentResult.data.id,
          authCode: generateAuthCode()
        }));

        // Auto-print receipt
        setTimeout(() => {
          generateAndPrintReceipt();
        }, 2000);
      } else {
        setTransaction(prev => ({
          ...prev,
          status: 'declined',
          errorMessage: paymentResult.error || 'Payment was declined'
        }));
      }
    } catch (error) {
      setTransaction(prev => ({
        ...prev,
        status: 'error',
        errorMessage: 'Payment processing failed'
      }));
    }
  };

  const processManualCard = async () => {
    if (!manualCardData.cardNumber || !manualCardData.expiryMonth || !manualCardData.expiryYear || !manualCardData.cvv) {
      alert('Please fill in all card details');
      return;
    }

    const cardData: CardData = {
      pan: manualCardData.cardNumber,
      expiryDate: `${manualCardData.expiryYear}${manualCardData.expiryMonth}`,
      cardholderName: manualCardData.cardholderName,
      cardType: 'swipe'
    };

    setShowManualEntry(false);
    setTransaction(prev => ({ ...prev, cardData }));
    await processCardData(cardData);
  };

  const startNewTransaction = () => {
    const newId = `TXN_${Date.now()}`;
    setTransaction({
      id: newId,
      amount: 0,
      status: 'amount-entry',
      timestamp: new Date()
    });
    setAmountInput('');
    setIsProcessing(false);
    setShowManualEntry(false);
    setManualCardData({
      cardNumber: '',
      expiryMonth: '',
      expiryYear: '',
      cvv: '',
      cardholderName: ''
    });
  };

  const processPayment = () => {
    if (!amountInput || parseFloat(amountInput) <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    const amount = Math.round(parseFloat(amountInput) * 100);
    setTransaction(prev => ({
      ...prev,
      amount,
      status: 'waiting-card'
    }));
  };

  const generateAndPrintReceipt = async () => {
    setTransaction(prev => ({ ...prev, status: 'printing' }));

    try {
      const response = await fetch(`/api/v1/receipts/${transaction.id}`);
      const receiptData = await response.json();

      if (receiptData.success) {
        const receiptText = receiptData.data.customerReceipt;
        setTransaction(prev => ({
          ...prev,
          receiptData: receiptText,
          status: 'complete'
        }));

        // Print using PAX terminal printer
        await paxTerminal.printReceipt(receiptText);
      }
    } catch (error) {
      console.error('Receipt generation failed:', error);
      setTransaction(prev => ({ ...prev, status: 'complete' }));
    }
  };

  const generateAuthCode = () => {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  };

  const getStatusDisplay = () => {
    switch (transaction.status) {
      case 'idle':
        return { text: 'Ready', icon: '💳', color: 'bg-blue-500' };
      case 'amount-entry':
        return { text: 'Enter Amount', icon: '💰', color: 'bg-yellow-500' };
      case 'waiting-card':
        return { text: 'Insert or Tap Card', icon: '📱', color: 'bg-orange-500' };
      case 'card-reading':
        return { text: 'Reading Card...', icon: '📖', color: 'bg-blue-600' };
      case 'authorizing':
        return { text: 'Authorizing...', icon: '⚡', color: 'bg-purple-600' };
      case 'approved':
        return { text: 'Approved!', icon: '✅', color: 'bg-green-500' };
      case 'declined':
        return { text: 'Declined', icon: '❌', color: 'bg-red-500' };
      case 'printing':
        return { text: 'Printing...', icon: '🖨️', color: 'bg-indigo-500' };
      case 'complete':
        return { text: 'Complete', icon: '🎉', color: 'bg-green-600' };
      case 'error':
        return { text: 'Error', icon: '⚠️', color: 'bg-red-600' };
      default:
        return { text: 'Ready', icon: '💳', color: 'bg-gray-500' };
    }
  };

  const status = getStatusDisplay();

  return (
    <div className="max-w-md mx-auto h-full">
      <div className="bg-white rounded-2xl shadow-2xl border border-slate-200 overflow-hidden h-full flex flex-col">
        {/* Status Header */}
        <div className={`${status.color} p-6 text-white text-center flex-shrink-0`}>
          <div className="text-4xl sm:text-6xl mb-3">{status.icon}</div>
          <h1 className="text-xl sm:text-3xl font-bold mb-2">{status.text}</h1>
          {transaction.amount > 0 && (
            <div className="text-xl sm:text-2xl font-mono">
              ${(transaction.amount / 100).toFixed(2)}
            </div>
          )}
          {countdown > 0 && (
            <div className="text-sm sm:text-lg mt-2 opacity-90">
              {countdown}s remaining
            </div>
          )}
        </div>

        {/* Content Area */}
        <div className="flex-1 p-4 sm:p-6 overflow-auto">
          {/* Amount Entry */}
          {transaction.status === 'amount-entry' && (
            <div className="space-y-6">
              <div>
                <label className="block text-lg font-semibold text-slate-700 mb-4 text-center">
                  Transaction Amount
                </label>
                <div className="relative">
                  <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-500 text-2xl">$</span>
                  <input
                    type="number"
                    value={amountInput}
                    onChange={(e) => setAmountInput(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 border-2 border-slate-300 rounded-2xl focus:outline-none focus:ring-4 focus:ring-blue-500 focus:border-transparent text-2xl sm:text-3xl font-bold text-center"
                    placeholder="0.00"
                    min="0.01"
                    step="0.01"
                    autoFocus
                  />
                </div>
              </div>
              
              <button
                onClick={processPayment}
                disabled={!amountInput || parseFloat(amountInput) <= 0}
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 px-6 rounded-2xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-bold text-lg shadow-lg"
              >
                💳 Process Payment
              </button>
            </div>
          )}

          {/* Waiting for Card */}
          {transaction.status === 'waiting-card' && (
            <div className="text-center space-y-6">
              <div className="animate-pulse">
                <div className="text-6xl mb-4">💳</div>
                <p className="text-lg text-slate-600">
                  {isTerminalAvailable() 
                    ? 'Insert, tap, or swipe your card'
                    : 'Card reader not available'
                  }
                </p>
              </div>
              
              {!isTerminalAvailable() && (
                <button
                  onClick={() => setShowManualEntry(true)}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-xl hover:bg-blue-700 transition-colors font-semibold"
                >
                  Enter Card Details Manually
                </button>
              )}
            </div>
          )}

          {/* Manual Card Entry */}
          {showManualEntry && (
            <div className="space-y-4">
              <h3 className="text-lg font-bold text-center text-slate-800">Enter Card Details</h3>
              
              <input
                type="text"
                placeholder="Card Number"
                value={manualCardData.cardNumber}
                onChange={(e) => setManualCardData(prev => ({ ...prev, cardNumber: e.target.value }))}
                className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxLength={19}
              />
              
              <div className="grid grid-cols-2 gap-3">
                <select
                  value={manualCardData.expiryMonth}
                  onChange={(e) => setManualCardData(prev => ({ ...prev, expiryMonth: e.target.value }))}
                  className="px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Month</option>
                  {Array.from({ length: 12 }, (_, i) => (
                    <option key={i + 1} value={String(i + 1).padStart(2, '0')}>
                      {String(i + 1).padStart(2, '0')}
                    </option>
                  ))}
                </select>
                
                <select
                  value={manualCardData.expiryYear}
                  onChange={(e) => setManualCardData(prev => ({ ...prev, expiryYear: e.target.value }))}
                  className="px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Year</option>
                  {Array.from({ length: 10 }, (_, i) => (
                    <option key={i} value={String(new Date().getFullYear() + i).slice(-2)}>
                      {new Date().getFullYear() + i}
                    </option>
                  ))}
                </select>
              </div>
              
              <input
                type="text"
                placeholder="CVV"
                value={manualCardData.cvv}
                onChange={(e) => setManualCardData(prev => ({ ...prev, cvv: e.target.value }))}
                className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxLength={4}
              />
              
              <input
                type="text"
                placeholder="Cardholder Name (Optional)"
                value={manualCardData.cardholderName}
                onChange={(e) => setManualCardData(prev => ({ ...prev, cardholderName: e.target.value }))}
                className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setShowManualEntry(false)}
                  className="bg-gray-600 text-white py-3 px-4 rounded-xl hover:bg-gray-700 transition-colors font-semibold"
                >
                  Cancel
                </button>
                <button
                  onClick={processManualCard}
                  className="bg-green-600 text-white py-3 px-4 rounded-xl hover:bg-green-700 transition-colors font-semibold"
                >
                  Process
                </button>
              </div>
            </div>
          )}

          {/* Processing States */}
          {['card-reading', 'authorizing'].includes(transaction.status) && (
            <div className="text-center space-y-6">
              <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto"></div>
              <div className="text-lg text-slate-600">
                {transaction.status === 'card-reading' && 'Do not remove your card...'}
                {transaction.status === 'authorizing' && 'Contacting your bank...'}
              </div>
            </div>
          )}

          {/* Success State */}
          {transaction.status === 'approved' && (
            <div className="text-center space-y-4">
              <div className="text-6xl">✅</div>
              <div className="text-2xl font-bold text-green-600">Payment Approved!</div>
              <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="font-medium text-slate-600">Amount:</span>
                    <div className="text-lg font-bold">${(transaction.amount / 100).toFixed(2)}</div>
                  </div>
                  <div>
                    <span className="font-medium text-slate-600">Card:</span>
                    <div className="font-mono">****{transaction.cardData?.pan?.slice(-4) || '****'}</div>
                  </div>
                  <div className="col-span-2">
                    <span className="font-medium text-slate-600">Auth Code:</span>
                    <div className="font-mono text-lg">{transaction.authCode}</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Error States */}
          {['declined', 'error'].includes(transaction.status) && (
            <div className="text-center space-y-6">
              <div className="text-6xl">❌</div>
              <div className="text-xl font-bold text-red-600">
                {transaction.status === 'declined' ? 'Payment Declined' : 'Transaction Error'}
              </div>
              {transaction.errorMessage && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-4 text-red-700 text-sm">
                  {transaction.errorMessage}
                </div>
              )}
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setTransaction(prev => ({ ...prev, status: 'amount-entry' }))}
                  className="bg-blue-600 text-white py-3 px-4 rounded-xl hover:bg-blue-700 transition-colors font-semibold"
                >
                  🔄 Retry
                </button>
                <button
                  onClick={startNewTransaction}
                  className="bg-gray-600 text-white py-3 px-4 rounded-xl hover:bg-gray-700 transition-colors font-semibold"
                >
                  ❌ Cancel
                </button>
              </div>
            </div>
          )}

          {/* Complete State */}
          {transaction.status === 'complete' && (
            <div className="text-center space-y-6">
              <div className="text-6xl">🎉</div>
              <div className="text-xl font-bold text-green-600">Transaction Complete!</div>
              <div className="text-sm text-slate-600">Receipt has been printed</div>
              
              <button
                onClick={startNewTransaction}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-2xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-500 transition-all duration-200 font-bold text-lg shadow-lg"
              >
                🆕 New Transaction
              </button>
            </div>
          )}

          {/* Idle State */}
          {transaction.status === 'idle' && (
            <div className="text-center">
              <button
                onClick={startNewTransaction}
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-6 px-8 rounded-2xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-500 transition-all duration-200 font-bold text-xl shadow-lg"
              >
                🚀 Start Transaction
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
