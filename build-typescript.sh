#!/bin/bash

set -e

echo "🚀 Building PAX A920 Pro POS Terminal - Pure TypeScript"
echo "======================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Clean previous builds
print_status "Cleaning previous builds..."
rm -rf dist/
rm -rf apps/*/dist/

print_success "Cleaned previous builds"

# Install dependencies
print_status "Installing dependencies..."
npm install

# Install backend dependencies
cd apps/backend
npm install
cd ../..

# Install frontend dependencies
cd apps/frontend
npm install
cd ../..

# Install mock bank dependencies
cd apps/mock-bank
npm install
cd ../..

print_success "Dependencies installed"

# Build backend
print_status "Building backend..."

cd apps/backend
npm run build
cd ../..

print_success "Backend built successfully"

# Build frontend
print_status "Building frontend..."

cd apps/frontend
npm run build
cd ../..

print_success "Frontend built successfully"

# Build mock bank
print_status "Building mock bank..."

cd apps/mock-bank
npm run build
cd ../..

print_success "Mock bank built successfully"

# Create TypeScript production bundle
print_status "Creating TypeScript production bundle..."

mkdir -p dist/typescript

# Copy backend
cp -r apps/backend/dist dist/typescript/backend
cp apps/backend/package.json dist/typescript/backend/

# Copy frontend
cp -r apps/frontend/dist dist/typescript/frontend

# Copy mock bank
cp -r apps/mock-bank/dist dist/typescript/mock-bank
cp apps/mock-bank/package.json dist/typescript/mock-bank/

# Create startup script
cat > dist/typescript/start.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting PAX A920 Pro POS Terminal (TypeScript)"

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18 or later."
    exit 1
fi

# Install production dependencies
echo "📦 Installing production dependencies..."

cd backend && npm install --production --silent && cd ..
cd mock-bank && npm install --production --silent && cd ..

echo "✅ Dependencies installed"

# Start services
echo "🔄 Starting services..."

# Start backend
echo "Starting backend on port 3001..."
cd backend && npm start &
BACKEND_PID=$!
cd ..

# Start mock bank
echo "Starting mock bank on port 3002..."
cd mock-bank && npm start &
MOCK_BANK_PID=$!
cd ..

# Start frontend server
echo "Starting frontend on port 3000..."
cd frontend

# Try different methods to serve static files
if command -v python3 &> /dev/null; then
    python3 -m http.server 3000 &
    FRONTEND_PID=$!
elif command -v python &> /dev/null; then
    python -m http.server 3000 &
    FRONTEND_PID=$!
elif command -v npx &> /dev/null; then
    npx serve -s . -l 3000 &
    FRONTEND_PID=$!
else
    echo "❌ No static file server available. Please install Python or Node.js serve package."
    kill $BACKEND_PID $MOCK_BANK_PID
    exit 1
fi

cd ..

echo ""
echo "🎉 All services started successfully!"
echo "=================================="
echo "Frontend:  http://localhost:3000"
echo "Backend:   http://localhost:3001"
echo "Mock Bank: http://localhost:3002"
echo ""
echo "Process IDs:"
echo "Backend:   $BACKEND_PID"
echo "Mock Bank: $MOCK_BANK_PID"
echo "Frontend:  $FRONTEND_PID"
echo ""
echo "To stop all services:"
echo "kill $BACKEND_PID $MOCK_BANK_PID $FRONTEND_PID"
echo ""
echo "Press Ctrl+C to stop all services"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping all services..."
    kill $BACKEND_PID $MOCK_BANK_PID $FRONTEND_PID 2>/dev/null
    echo "✅ All services stopped"
    exit 0
}

# Set trap to cleanup on exit
trap cleanup SIGINT SIGTERM

# Wait for any process to exit
wait
EOF

chmod +x dist/typescript/start.sh

# Create Windows startup script
cat > dist/typescript/start.bat << 'EOF'
@echo off
echo 🚀 Starting PAX A920 Pro POS Terminal (TypeScript)

:: Check if Node.js is available
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18 or later.
    exit /b 1
)

:: Install production dependencies
echo 📦 Installing production dependencies...

cd backend && npm install --production --silent && cd ..
cd mock-bank && npm install --production --silent && cd ..

echo ✅ Dependencies installed

:: Start services
echo 🔄 Starting services...

:: Start backend
echo Starting backend on port 3001...
cd backend
start "Backend" cmd /c "npm start"
cd ..

:: Start mock bank
echo Starting mock bank on port 3002...
cd mock-bank
start "Mock Bank" cmd /c "npm start"
cd ..

:: Start frontend server
echo Starting frontend on port 3000...
cd frontend

:: Try different methods to serve static files
where python >nul 2>&1
if %errorlevel% == 0 (
    start "Frontend" cmd /c "python -m http.server 3000"
) else (
    where npx >nul 2>&1
    if %errorlevel% == 0 (
        start "Frontend" cmd /c "npx serve -s . -l 3000"
    ) else (
        echo ❌ No static file server available. Please install Python or Node.js serve package.
        exit /b 1
    )
)

cd ..

echo.
echo 🎉 All services started successfully!
echo ==================================
echo Frontend:  http://localhost:3000
echo Backend:   http://localhost:3001
echo Mock Bank: http://localhost:3002
echo.
echo Press any key to stop all services...
pause >nul

:: Stop services
echo 🛑 Stopping all services...
taskkill /f /im node.exe /t >nul 2>&1
taskkill /f /im python.exe /t >nul 2>&1
echo ✅ All services stopped
EOF

# Create environment template
cat > dist/typescript/.env.example << 'EOF'
# PAX A920 Pro POS Terminal Configuration (TypeScript)
NODE_ENV=production

# Database
MONGO_URI=mongodb://localhost:27017/pax_pos

# Stripe Configuration (REPLACE WITH YOUR KEYS)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Security
JWT_SECRET=your_super_secure_jwt_secret_at_least_32_characters
CORS_ORIGIN=http://localhost:3000

# Terminal Configuration
TERMINAL_ID=PAX_A920_001
MERCHANT_ID=your_merchant_id
DEFAULT_CURRENCY=usd
PAYMENT_TIMEOUT=30000
MIN_AMOUNT=50
MAX_AMOUNT=********

# Features
CARD_READER_ENABLED=true
PRINTER_ENABLED=true
AUDIO_ENABLED=true
VIBRATION_ENABLED=true
MANUAL_ENTRY_ENABLED=true

# Receipt
RECEIPT_ENABLED=true
RECEIPT_COPIES=2
RECEIPT_SIGNATURE=true
RECEIPT_FOOTER=Thank you for your business!

# UI
UI_THEME=auto
UI_LANGUAGE=en
UI_FONT_SIZE=medium
UI_ORIENTATION=portrait

# Logging
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000
EOF

# Create README for TypeScript build
cat > dist/typescript/README.md << 'EOF'
# PAX A920 Pro POS Terminal - TypeScript Build

This is a pure TypeScript implementation of the PAX A920 Pro POS terminal system.
No Rust or WASM required - everything is built with TypeScript!

## Features

✅ **Pure TypeScript**: No Rust/WASM dependencies
✅ **Hardware Integration**: Web APIs for card readers
✅ **Mobile Responsive**: Optimized for PAX A920 Pro
✅ **Real Payments**: Stripe integration
✅ **Simulation Mode**: Works without hardware
✅ **Cross-Platform**: Windows, macOS, Linux

## Hardware Support

- **Android WebView**: Direct PAX SDK integration
- **Web USB**: USB card readers
- **Web Serial**: Serial card readers
- **Simulation**: Development/testing mode

## Quick Start

1. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your Stripe keys
   ```

2. **Start Services**:
   ```bash
   # Linux/macOS
   ./start.sh
   
   # Windows
   start.bat
   ```

3. **Access Application**:
   - Frontend: http://localhost:3000
   - Backend: http://localhost:3001
   - Mock Bank: http://localhost:3002

## Card Reading

The system supports multiple card reading methods:

1. **PAX Hardware** (Android WebView)
2. **USB Card Readers** (Web USB API)
3. **Serial Card Readers** (Web Serial API)
4. **Manual Entry** (Fallback)
5. **Simulation** (Development)

## Deployment

This build is ready for:
- Server deployment
- Docker containers
- APK creation (with Cordova)
- Cloud hosting

## No Rust Required!

This TypeScript implementation provides all the functionality
of the WASM version without requiring Rust compilation.
EOF

print_success "TypeScript production bundle created in dist/typescript/"

echo ""
echo "🎉 TypeScript build completed successfully!"
echo "=========================================="
echo ""
echo "📦 Build Output: dist/typescript/"
echo ""
echo "🚀 To start the application:"
echo "   cd dist/typescript"
echo "   ./start.sh (Linux/macOS) or start.bat (Windows)"
echo ""
echo "🔧 Configuration:"
echo "   1. Copy .env.example to .env"
echo "   2. Configure your Stripe keys"
echo "   3. Set up MongoDB connection"
echo ""
echo "📱 For APK creation:"
echo "   bash create-simple-apk.sh"
echo ""
echo "✅ Features:"
echo "   - Pure TypeScript (no Rust/WASM)"
echo "   - Hardware integration via Web APIs"
echo "   - Mobile responsive design"
echo "   - Real payment processing"
echo "   - Simulation mode for development"
echo "   - Cross-platform compatibility"
echo ""
print_success "Ready for deployment! 🚀"
