# PAX A920 Pro POS Terminal System

A production-ready Point of Sale (POS) system built for the PAX A920 Pro terminal with Stripe payment integration and ISO 8583 protocol simulation.

## 🚀 Features

- **💳 Payment Processing**: Stripe Terminal SDK integration for card-present payments
- **🏦 Protocol Simulation**: ISO 8583-style protocol handlers (101.1, 101.3, 101.5, 101.8)
- **🔄 Mock Banking**: Simulated bank communication for testing and development
- **🧾 Receipt Printing**: Optional receipt generation and printing capabilities
- **📊 Real-time Logging**: Comprehensive logging and monitoring system
- **🐳 Production Ready**: Docker containerization with CI/CD pipeline
- **🔒 Security**: Rate limiting, CORS protection, input validation, and secure headers
- **📱 WebView Compatible**: Frontend optimized for PAX terminal WebView

## 🏗️ Architecture

This is a TypeScript monorepo containing:

- **Frontend**: React + TypeScript + Vite + Tailwind CSS + Zustand
- **Backend**: Node.js + Fastify + TypeScript + MongoDB + Zod validation
- **Mock Bank**: Express.js service simulating ISO 8583 bank responses
- **Shared Types**: Common TypeScript interfaces and API contracts

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- <PERSON>er and Docker Compose
- Stripe account (for payment processing)

### Development Setup

1. **Clone and install**:
```bash
git clone <repository-url>
cd stripe-pos
npm install
```

2. **Environment setup**:
```bash
cp .env.example .env.development
# Edit .env.development with your Stripe keys and configuration
```

3. **Start development**:
```bash
chmod +x scripts/dev.sh
./scripts/dev.sh dev
```

**Development URLs**:
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- Mock Bank: http://localhost:3002
- MongoDB Admin: http://localhost:8081

### Production Deployment

1. **Production environment**:
```bash
cp .env.example .env
# Edit .env with production values
```

2. **Deploy with Docker**:
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

**Production URLs**:
- Application: http://localhost
- Backend API: http://localhost:3001
- Mock Bank: http://localhost:3002

## 📚 API Documentation

### Core Endpoints

#### Stripe Integration
- `POST /api/v1/stripe/connection_token` - Get Stripe Terminal connection token
- `POST /api/v1/stripe/payment_intent` - Create payment intent
- `POST /api/v1/stripe/capture_intent` - Capture payment intent

#### Payment Simulation
- `POST /api/v1/payment/simulate` - Simulate payment with protocol triggers

#### Protocol Communication
- `POST /api/v1/protocol/trigger` - Trigger ISO 8583 protocol messages

#### Transaction Management
- `GET /api/v1/transaction` - List transactions (with pagination)
- `GET /api/v1/transaction/:id` - Get transaction by ID
- `POST /api/v1/transaction` - Create transaction

#### System Health
- `GET /health` - Application health check

### Protocol Codes

| Code  | Description | Purpose |
|-------|-------------|---------|
| 101.1 | Authorization Request | Initial payment authorization |
| 101.3 | Capture Request | Capture authorized payment |
| 101.5 | Reversal Request | Reverse/void transaction |
| 101.8 | Settlement Request | End-of-day settlement |

## 🛠️ Development

### Available Scripts

```bash
# Monorepo Commands (Run all services at once)
npm run dev:all              # Start all development servers
npm run build:all            # Build all packages
npm run start                # Start all production services

# Individual Development
npm run dev:backend          # Start backend only
npm run dev:frontend         # Start frontend only
npm run dev:mock-bank        # Start mock bank only

# Individual Building
npm run build:shared         # Build shared types
npm run build:backend        # Build backend
npm run build:frontend       # Build frontend
npm run build:mock-bank      # Build mock bank

# Other Commands
npm run lint                 # Run linting
npm run test                 # Run tests
npm run format               # Format code
```

## 🐳 Deployment

### Docker Deployment Options

```bash
# Full deployment
./scripts/deploy.sh

# View service status
./scripts/deploy.sh status

# View logs
./scripts/deploy.sh logs

# Restart services
./scripts/deploy.sh restart

# Stop services
./scripts/deploy.sh stop

# Clean deployment (removes volumes)
./scripts/deploy.sh clean
```

### Environment Configuration

#### Required Variables
```bash
STRIPE_SECRET_KEY=sk_test_...           # Stripe secret key
STRIPE_PUBLISHABLE_KEY=pk_test_...      # Stripe publishable key
STRIPE_WEBHOOK_SECRET=whsec_...         # Stripe webhook secret
JWT_SECRET=your_32_char_secret          # JWT signing secret
MONGO_ROOT_PASSWORD=secure_password     # MongoDB root password
```

## 🔒 Security Features

- **🛡️ Input Validation**: Zod schema validation on all endpoints
- **🚦 Rate Limiting**: Configurable rate limiting per IP
- **🔐 CORS Protection**: Configurable CORS origins
- **🔒 Security Headers**: Helmet.js security headers
- **🚫 Secret Protection**: Stripe keys never exposed to frontend
- **📝 Request Logging**: Comprehensive request/response logging
- **🏥 Health Checks**: Built-in health monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes with tests
4. Run quality checks: `./scripts/dev.sh lint && ./scripts/dev.sh test`
5. Commit changes: `git commit -m 'Add amazing feature'`
6. Push to branch: `git push origin feature/amazing-feature`
7. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Built with ❤️ for PAX A920 Pro terminals**