[package]
name = "pax-terminal-wasm"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
wasm-bindgen = "0.2"
js-sys = "0.3"
serde = { version = "1.0", features = ["derive"] }
serde-wasm-bindgen = "0.4"
wasm-bindgen-futures = "0.4"
getrandom = { version = "0.2", features = ["js"] }
console_error_panic_hook = "0.1"
serde_json = "1.0"

[dependencies.web-sys]
version = "0.3"
features = [
  "console",
  "Window",
  "Document",
  "Element",
  "HtmlElement",
  "Navigator",
  "MediaDevices",
  "MediaStream",
  "MediaStreamTrack",
  "Bluetooth",
  "BluetoothDevice",
  "BluetoothRemoteGattServer",
  "BluetoothRemoteGattService",
  "BluetoothRemoteGattCharacteristic",
  "SerialPort",
  "Serial",
  "UsbDevice",
  "Usb",
  "HidDevice",
  "Hid",
  "Permissions",
  "PermissionStatus",
  "Notification",
  "NotificationPermission",
  "Vibration",
  "AudioContext",
  "OscillatorNode",
  "GainNode",
  "AudioDestinationNode",
]

[profile.release]
opt-level = "s"
lto = true
