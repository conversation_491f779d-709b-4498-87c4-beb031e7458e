import mongoose from 'mongoose';
import { env } from './env';
import { logger } from './logger';

const dbLogger = logger.child({ module: 'database' });

export async function connectDatabase(): Promise<void> {
  try {
    const options: mongoose.ConnectOptions = {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferCommands: false,
      bufferMaxEntries: 0,
    };

    if (env.NODE_ENV === 'production') {
      options.retryWrites = true;
      options.w = 'majority';
    }

    await mongoose.connect(env.MONGO_URI, options);
    
    dbLogger.info('Database connected successfully');
  } catch (error) {
    dbLogger.error({ error }, 'Database connection failed');
    throw error;
  }
}

export async function disconnectDatabase(): Promise<void> {
  try {
    await mongoose.disconnect();
    dbLogger.info('Database disconnected successfully');
  } catch (error) {
    dbLogger.error({ error }, 'Database disconnection failed');
    throw error;
  }
}

mongoose.connection.on('error', (error) => {
  dbLogger.error({ error }, 'Database connection error');
});

mongoose.connection.on('disconnected', () => {
  dbLogger.warn('Database disconnected');
});

mongoose.connection.on('reconnected', () => {
  dbLogger.info('Database reconnected');
});
